def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
//    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
    throw new Exception("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

def vCode = (new Date().getTime() / 1000).intValue()

def dartEnvironmentVariables = [APP_CHANNEL: 'Test']
if (project.hasProperty('dart-defines')) {
    dartEnvironmentVariables = dartEnvironmentVariables + project.property('dart-defines')
            .split(',')
            .collectEntries { entry ->
                def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
                [(pair.first()): pair.last()]
            }
}

android {
    compileSdkVersion 34
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }
    defaultConfig {
        applicationId "com.xinpai.zhihuiwuliu"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode vCode
        versionName flutterVersionName
        multiDexEnabled true
        ndk {
            //选择要添加的对应 cpu 类型的 .so 库。
            abiFilters 'armeabi', 'armeabi-v7a', 'x86', 'x86_64', 'mips', 'mips64', 'arm64-v8a'
        }
        manifestPlaceholders = [
                JPUSH_PKGNAME : applicationId,
                JPUSH_APPKEY  : "dff4337d07f5c22d79b6c7af",
                JPUSH_CHANNEL : "developer-default",
//                // OPPO平台
                OPPO_APPID    : "OP-30938307",
                OPPO_APPKEY   : "OP-648cfd99d8ee4aefbc6908708f9e7bc6",
                OPPO_APPSECRET: "OP-96a37b90616e423c8b5463302c325a9c",
//                // 小米
                XIAOMI_APPID : "MI-2882303761520214069",
                XIAOMI_APPKEY : "MI-5532021495069",
//                //ViVo
                VIVO_APPKEY : "72852af1a56a94ba360ac2a1546826da",
                VIVO_APPID : "105617673",
//                // 荣耀
                HONOR_APPID : "104408802",
        ]
    }

    lintOptions {
        disable 'InvalidPackage'
        checkReleaseBuilds false
        abortOnError false
    }

    signingConfigs {
        release {
            keyAlias 'sign'
            keyPassword 'xinpai'
            storeFile file('key/sign.jks')
            storePassword 'xinpai'
        }
    }
    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    buildTypes {
        release {
            manifestPlaceholders = [applicationName: "android.app.Application"]
            signingConfig signingConfigs.release
            zipAlignEnabled true //对齐app资源使程序更加高效
            shrinkResources false //除去无用的资源文件。
            minifyEnabled false //混淆
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
//            signingConfig signingConfigs.release
            manifestPlaceholders = [applicationName: "android.app.Application"]
        }
        build {
            manifestPlaceholders = [applicationName: "android.app.Application"]
        }
    }

    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                output ->
                    def version = variant.versionName //(variant.versionName).substring(0, 5) + '.' + (variant.versionName).substring(5)
                    outputFileName = "EnterpriseSafety_${dartEnvironmentVariables.APP_CHANNEL}_${new Date().format("MM-dd")}[v${version}].apk"
            }
    }

    configurations.all {
        resolutionStrategy {
            force 'androidx.core:core:1.10.0'
        }
    }

}

flutter {
    source '../..'
}

repositories {
    flatDir {
        dirs 'libs'
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    //极光厂商基础服务
    def jPushVersion = '4.8.4'
    implementation 'cn.jiguang.sdk:jcore:3.3.6'
    implementation "cn.jiguang.sdk:jpush:$jPushVersion"
    // 接入 OPPO 厂商
    implementation "cn.jiguang.sdk.plugin:oppo:$jPushVersion"
    // 接入华为厂商
    implementation 'com.huawei.hms:push:6.5.0.300'
    implementation "cn.jiguang.sdk.plugin:huawei:$jPushVersion"
    // 接入小米厂商
    implementation "cn.jiguang.sdk.plugin:xiaomi:$jPushVersion"
    // 接入ViVo厂商
    implementation "cn.jiguang.sdk.plugin:vivo:$jPushVersion"
    // 接入荣耀厂商
    implementation "cn.jiguang.sdk.plugin:honor:$jPushVersion"
}
apply plugin: 'com.huawei.agconnect' //此处用以调用agconnect动态状态管理器

// SHA265: 3C:51:6B:0C:F0:93:B1:ED:79:13:1A:72:3F:A1:A6:20:3F:1C:66:5C:07:F0:41:8A:D9:A1:EA:B4:4F:14:72:FC