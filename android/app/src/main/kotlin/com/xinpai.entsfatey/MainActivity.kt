package com.xinpai.zhihuiwuliu

import android.app.Notification;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugins.GeneratedPluginRegistrant;

import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.android.FlutterActivity

import com.example.lc_print_sdk.PrintUtil
import com.example.lc_print_sdk.PrintUtil.PrinterBinderListener
import com.example.lc_print_sdk.PrintConfig

class MainActivity: FlutterActivity() {


    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        GeneratedPluginRegistrant.registerWith(flutterEngine);
        MethodChannel(flutterEngine.getDartExecutor(), "back/desktop").setMethodCallHandler { methodCall, result ->
            if (methodCall.method.equals("backDesktop")) {
                moveTaskToBack(false)
                result.success(true)
            }
        }

        // 新增打印通道
        val CHANNEL = "com.xinpai.printer/print"
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { methodCall, result ->
            when (methodCall.method) {
                "printLabel" -> {
                    val dataToPrint = methodCall.argument<String>("data") ?: "No data received"
                    val printSuccess = printLabel(dataToPrint)
                    if (printSuccess) {
                        result.success("标签打印完成")
                    } else {
                        result.error("PRINT_ERROR", "打印失败", null)
                    }
                }
                else -> result.notImplemented()
            }
        }

    }

    private fun printLabel(dataToPrint: String): Boolean {
        try {
            val printUtil = PrintUtil.getInstance(this)  // 获取PrintUtil的实例

            // 设置打印结果回调监听
            PrintUtil.setPrintEventListener(object : PrintUtil.PrinterBinderListener {
                override fun onPrintCallback(param1Int: Int) {
                    Log.d("PrintCallback", "Callback code: $param1Int")
                }

                override fun onVersion(param1String: String) {
                    Log.d("PrinterVersion", "Printer version: $param1String")
                }
            })

            // 标签设置
            PrintUtil.setUnwindPaperLen(50) // 设置回纸距离
            PrintUtil.printEnableMark(true) // 开启黑标
            PrintUtil.printConcentration(25) // 设置浓度

            // 打印标签二维码和文本
            PrintUtil.printQR(PrintConfig.Align.ALIGN_CENTER, 240, dataToPrint);
            PrintUtil.printText(PrintConfig.Align.ALIGN_CENTER, PrintConfig.FontSize.TOP_FONT_SIZE_MIDDLE, false, false, dataToPrint);

            // 走纸
            printUtil.printGoToNextMark()  // 通过实例调用非静态方法

            return true // 打印成功
        } catch (e: Exception) {
            Log.e("PrintTest", "打印异常: ${e.message}")
            return false // 打印失败
        }
    }

}