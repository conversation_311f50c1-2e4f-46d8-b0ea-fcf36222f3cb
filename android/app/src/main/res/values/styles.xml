<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base application theme. -->
    <!--<style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">-->
        <!--&lt;!&ndash; Customize your theme here. &ndash;&gt;-->
    <!--</style>-->

    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             Flutter draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
</resources>
