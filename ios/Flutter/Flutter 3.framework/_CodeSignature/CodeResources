<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Flutter.h</key>
		<data>
		LQBk1GD+u1YEANQovLVv2R/uUag=
		</data>
		<key>Headers/FlutterAppDelegate.h</key>
		<data>
		VQRY5CUL6CLzdXzfScbDvTdpElA=
		</data>
		<key>Headers/FlutterBinaryMessenger.h</key>
		<data>
		3TKVS3KoBmkupZxin9YbnrPtMCk=
		</data>
		<key>Headers/FlutterCallbackCache.h</key>
		<data>
		v6UEJmd8GTKddiiNFrVRsY9DAZo=
		</data>
		<key>Headers/FlutterChannels.h</key>
		<data>
		J+6KY/2b9wt7nlA7nZ/R0T7hQQI=
		</data>
		<key>Headers/FlutterCodecs.h</key>
		<data>
		sQOBG5Yk0MkcfzryPhwuvyoOubw=
		</data>
		<key>Headers/FlutterDartProject.h</key>
		<data>
		GaPT5YuPi7h3m5Ztnur9wJSDg28=
		</data>
		<key>Headers/FlutterEngine.h</key>
		<data>
		LJQOfMrJadCb3BZ18IdY6i8wWZQ=
		</data>
		<key>Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		M8/dhQH3VcQVHupUxQ1dGsbG3Ek=
		</data>
		<key>Headers/FlutterMacros.h</key>
		<data>
		XzRpNYIQNfYUVORhhCHV7HOtCGM=
		</data>
		<key>Headers/FlutterPlatformViews.h</key>
		<data>
		qG4SZHnA7c2rlrc06oKxPzEKFfU=
		</data>
		<key>Headers/FlutterPlugin.h</key>
		<data>
		iA41oFWXYC7nUQQX+BRMVxVOdnA=
		</data>
		<key>Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		JclXijgaQMogMkl0i4L/a3RxO8g=
		</data>
		<key>Headers/FlutterTexture.h</key>
		<data>
		VaK8X9GOryz168OCiyjlq8MI110=
		</data>
		<key>Headers/FlutterViewController.h</key>
		<data>
		E4Ln9BDq+LPTdIlJi/kdpt5ZAdA=
		</data>
		<key>Info.plist</key>
		<data>
		J3jiG8FYdA9v3xxFHS/AIv+B5D8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>icudtl.dat</key>
		<data>
		z32dyLOVE4+TKehZcBQlHkxsxKQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Flutter.h</key>
		<dict>
			<key>hash</key>
			<data>
			LQBk1GD+u1YEANQovLVv2R/uUag=
			</data>
			<key>hash2</key>
			<data>
			8ZWbAXinM+GRkpUfLzcvsat4nccJ7ZNgGrAhy0iglXs=
			</data>
		</dict>
		<key>Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			VQRY5CUL6CLzdXzfScbDvTdpElA=
			</data>
			<key>hash2</key>
			<data>
			TYSAedf2/9SM/60199M+eUXHFYq5f85akCVmksptiNg=
			</data>
		</dict>
		<key>Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash</key>
			<data>
			3TKVS3KoBmkupZxin9YbnrPtMCk=
			</data>
			<key>hash2</key>
			<data>
			USlivwosbaNJJAtX2mpOdBVwZE9osgj/I3NnC9DCS04=
			</data>
		</dict>
		<key>Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash</key>
			<data>
			v6UEJmd8GTKddiiNFrVRsY9DAZo=
			</data>
			<key>hash2</key>
			<data>
			5i4mQAU2+ZEryb3POeTu9hkEM1IZkS/uLLxqLrcXRv0=
			</data>
		</dict>
		<key>Headers/FlutterChannels.h</key>
		<dict>
			<key>hash</key>
			<data>
			J+6KY/2b9wt7nlA7nZ/R0T7hQQI=
			</data>
			<key>hash2</key>
			<data>
			PR53IJWNUfRGtYq5TSYWcAST7FY8FLP5wXRVXTfNkgE=
			</data>
		</dict>
		<key>Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash</key>
			<data>
			sQOBG5Yk0MkcfzryPhwuvyoOubw=
			</data>
			<key>hash2</key>
			<data>
			Meh20V/Phs36p98gfrUXPhoRtwz75kFim+WL3huzgSQ=
			</data>
		</dict>
		<key>Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash</key>
			<data>
			GaPT5YuPi7h3m5Ztnur9wJSDg28=
			</data>
			<key>hash2</key>
			<data>
			8TvHOh9AUqcR7tUKWWnDXAs48Gwn/rc2V0fqqwedyL4=
			</data>
		</dict>
		<key>Headers/FlutterEngine.h</key>
		<dict>
			<key>hash</key>
			<data>
			LJQOfMrJadCb3BZ18IdY6i8wWZQ=
			</data>
			<key>hash2</key>
			<data>
			M4bhndP7yk3GSrTS/lp5u6/pwR6LGFou02UwSZfy94o=
			</data>
		</dict>
		<key>Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash</key>
			<data>
			M8/dhQH3VcQVHupUxQ1dGsbG3Ek=
			</data>
			<key>hash2</key>
			<data>
			2LUPyCMVHwKeIFZP9I5vV64Kl7unE39yisrUZVrUiVo=
			</data>
		</dict>
		<key>Headers/FlutterMacros.h</key>
		<dict>
			<key>hash</key>
			<data>
			XzRpNYIQNfYUVORhhCHV7HOtCGM=
			</data>
			<key>hash2</key>
			<data>
			g8e3Y3ORucWSsYgMPClXqNe33oTSyyi3RVSgqKDp9KA=
			</data>
		</dict>
		<key>Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash</key>
			<data>
			qG4SZHnA7c2rlrc06oKxPzEKFfU=
			</data>
			<key>hash2</key>
			<data>
			TwEDM1i9uE3YWhZ4d0WxC8kV0YC/nhdBkL1mCx+z1Pw=
			</data>
		</dict>
		<key>Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash</key>
			<data>
			iA41oFWXYC7nUQQX+BRMVxVOdnA=
			</data>
			<key>hash2</key>
			<data>
			w5EgHMebCtKpo+84J4QDOp6ZvIEKx8D1UuOh/cKh5ig=
			</data>
		</dict>
		<key>Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JclXijgaQMogMkl0i4L/a3RxO8g=
			</data>
			<key>hash2</key>
			<data>
			bWfBJtnl0BrsLHRn6WuhfLJMU19Wf6csfSvQ9CTPk2Y=
			</data>
		</dict>
		<key>Headers/FlutterTexture.h</key>
		<dict>
			<key>hash</key>
			<data>
			VaK8X9GOryz168OCiyjlq8MI110=
			</data>
			<key>hash2</key>
			<data>
			YVRJhub9UBm9xvHnEeOSIj4BDyo+izrD1V2hURTeFYM=
			</data>
		</dict>
		<key>Headers/FlutterViewController.h</key>
		<dict>
			<key>hash</key>
			<data>
			E4Ln9BDq+LPTdIlJi/kdpt5ZAdA=
			</data>
			<key>hash2</key>
			<data>
			YA63QxtWlRZlOhmWqBpCeK9bAbF0hoOTOQNCvIquUdI=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			wJV5dCKEGl+FAtDc8wJJh/fvKXs=
			</data>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>icudtl.dat</key>
		<dict>
			<key>hash</key>
			<data>
			z32dyLOVE4+TKehZcBQlHkxsxKQ=
			</data>
			<key>hash2</key>
			<data>
			zdr5Vzvecp4snhYdlNG4xyqroPTCh6TeJWIkNMcEyTE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
