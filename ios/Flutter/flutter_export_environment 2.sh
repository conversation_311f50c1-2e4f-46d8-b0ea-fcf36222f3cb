#!/bin/sh
# This is a generated file; do not edit or check into version control.
export "FLUTTER_ROOT=/Users/<USER>/Library/Flutter/flutter"
export "FLUTTER_APPLICATION_PATH=/Users/<USER>/Documents/project/ent_security_app"
export "FLUTTER_TARGET=/Users/<USER>/Documents/project/ent_security_app/lib/main.dart"
export "FLUTTER_BUILD_DIR=build"
export "SYMROOT=${SOURCE_ROOT}/../build/ios"
export "OTHER_LDFLAGS=$(inherited) -framework Flutter"
export "FLUTTER_FRAMEWORK_DIR=/Users/<USER>/Library/Flutter/flutter/bin/cache/artifacts/engine/ios"
export "FLUTTER_BUILD_NAME=1.0.0"
export "FLUTTER_BUILD_NUMBER=1"
export "TRACK_WIDGET_CREATION=true"
