PODS:
  - app_settings (5.1.1):
    - Flutter
  - barcode_scan2 (0.0.1):
    - Flutter
    - MTBBarcodeScanner
    - SwiftProtobuf
  - camera_avfoundation (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - device_information (0.0.1):
    - Flutter
  - file_preview (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_nfc_kit (2.0.0):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - geolocator_apple (1.2.0):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - JCore (4.4.0)
  - JPush (5.2.0):
    - J<PERSON>ore (>= 2.0.0)
  - jpush_flutter (0.0.2):
    - Flutter
    - <PERSON><PERSON>ore (= 4.4.0)
    - JPush (= 5.2.0)
  - libwebp (1.3.2):
    - libwebp/demux (= 1.3.2)
    - libwebp/mux (= 1.3.2)
    - libwebp/sharpyuv (= 1.3.2)
    - libwebp/webp (= 1.3.2)
  - libwebp/demux (1.3.2):
    - libwebp/webp
  - libwebp/mux (1.3.2):
    - libwebp/demux
  - libwebp/sharpyuv (1.3.2)
  - libwebp/webp (1.3.2):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MTBBarcodeScanner (5.0.11)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pdf_viewer_plugin (0.0.1):
    - Flutter
  - permission_handler_apple (9.1.1):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - r_upgrade (0.0.1):
    - Flutter
  - SDWebImage (5.18.10):
    - SDWebImage/Core (= 5.18.10)
  - SDWebImage/Core (5.18.10)
  - SDWebImageWebPCoder (0.14.2):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - SwiftProtobuf (1.25.2)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_compress (0.3.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - barcode_scan2 (from `.symlinks/plugins/barcode_scan2/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - device_information (from `.symlinks/plugins/device_information/ios`)
  - file_preview (from `.symlinks/plugins/file_preview/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_nfc_kit (from `.symlinks/plugins/flutter_nfc_kit/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - jpush_flutter (from `.symlinks/plugins/jpush_flutter/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pdf_viewer_plugin (from `.symlinks/plugins/pdf_viewer_plugin/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - r_upgrade (from `.symlinks/plugins/r_upgrade/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)

SPEC REPOS:
  https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git:
    - FMDB
    - JCore
    - JPush
    - libwebp
    - Mantle
    - MTBBarcodeScanner
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftProtobuf

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  barcode_scan2:
    :path: ".symlinks/plugins/barcode_scan2/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  device_information:
    :path: ".symlinks/plugins/device_information/ios"
  file_preview:
    :path: ".symlinks/plugins/file_preview/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_nfc_kit:
    :path: ".symlinks/plugins/flutter_nfc_kit/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  jpush_flutter:
    :path: ".symlinks/plugins/jpush_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pdf_viewer_plugin:
    :path: ".symlinks/plugins/pdf_viewer_plugin/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  r_upgrade:
    :path: ".symlinks/plugins/r_upgrade/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"

SPEC CHECKSUMS:
  app_settings: 017320c6a680cdc94c799949d95b84cb69389ebc
  barcode_scan2: 0af2bb63c81b4565aab6cd78278e4c0fa136dbb0
  camera_avfoundation: 8b8d780bcfb6a4a02b0fbe2b4bd17b5b71946e68
  device_info_plus: c6fb39579d0f423935b0c9ce7ee2f44b71b9fce6
  device_information: d652f6dcea86bdc5300ffd495486de1d5047bf4f
  file_preview: f1f26bfb9836a41685d3098dccf3ebecd01f85b9
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_image_compress: 5a5e9aee05b6553048b8df1c3bc456d0afaac433
  flutter_nfc_kit: 965c98c3fa68f5609f1cc89abb968fe1b8ffdbaa
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  geolocator_apple: 9bcea1918ff7f0062d98345d238ae12718acfbc1
  image_picker_ios: 99dfe1854b4fa34d0364e74a78448a0151025425
  JCore: 05f0f3489672ea3fa55338bae4866224bc092b1f
  JPush: 76668b765fcfd7c15f86b05ca0e5cdc01945ce23
  jpush_flutter: 1f436f1a02db5c5ef9e9b42378f557096ccd328b
  libwebp: 1786c9f4ff8a279e4dac1e8f385004d5fc253009
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  pdf_viewer_plugin: 45fc2233c590780ca0ca623a1f575e4bd0a72b3a
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  photo_manager: ff695c7a1dd5bc379974953a2b5c0a293f7c4c8a
  r_upgrade: 44d715c61914cce3d01ea225abffe894fd51c114
  SDWebImage: fc8f2d48bbfd72ef39d70e981bd24a3f3be53fec
  SDWebImageWebPCoder: 633b813fca24f1de5e076bcd7f720c038b23892b
  sensors_plus: 42b9de1b8237675fa8d8121e4bb93be0f79fa61d
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sqflite: 50a33e1d72bd59ee092a519a35d107502757ebed
  SwiftProtobuf: 407a385e97fd206c4fbe880cc84123989167e0d1
  url_launcher_ios: bbd758c6e7f9fd7b5b1d4cde34d2b95fcce5e812
  video_compress: fce97e4fb1dfd88175aa07d2ffc8a2f297f87fbe
  video_player_avfoundation: 02011213dab73ae3687df27ce441fbbcc82b5579
  wakelock_plus: 8b09852c8876491e4b6d179e17dfe2a0b5f60d47

PODFILE CHECKSUM: 224846dc92347f1abd0e9cbde648ffbbfdf61b99

COCOAPODS: 1.16.2
