<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>安全生产管理平台</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>安全生产管理平台</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1712134263</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NFCReaderUsageDescription</key>
	<string>App需要您的同意，才能使用NFC功能，以便于完成巡检任务</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>APP需要您的同意，才能使用摄像头，以便于相机拍摄，上传、发布照片</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>App需要您的同意，才能访问位置信息，以便于发送险情所在位置</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>App需要您的同意，才能始终访问位置，以便于发送险情所在位置</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>App需要您的同意，才能在使用期间访问位置，以便于发送险情所在位置</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>App需要您的同意,才能访问麦克风，已便于录制音频</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>APP需要您的同意，才能访问相册，以便于图片选取、上传、发布</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>com.apple.developer.nfc.readersession.felica.systemcodes</key>
	<array>
		<string>8005</string>
		<string>8008</string>
		<string>0003</string>
		<string>fe00</string>
		<string>90b7</string>
		<string>927a</string>
		<string>86a7</string>
	</array>
	<key>com.apple.developer.nfc.readersession.formats</key>
	<array>
		<string>NDEF</string>
		<string>TAG</string>
	</array>
	<key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
	<array>
		<string>A0000002471001</string>
		<string>A000000003101001</string>
		<string>A000000003101002</string>
		<string>A0000000041010</string>
		<string>A0000000042010</string>
		<string>A0000000044010</string>
		<string>44464D46412E44466172653234313031</string>
		<string>D2760000850100</string>
		<string>D2760000850101</string>
		<string>00000000000000</string>
	</array>
	<key>io.flutter.embedded_views_preview</key>
	<string>YES</string>
</dict>
</plist>
