import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:flutter/material.dart';

import '../../../dnc/utils/WidgetUtils.dart';

class DetailPage extends StatefulWidget {
  final item;

  DetailPage(this.item, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return DetailPageState();
  }
}

class DetailPageState extends BasePageState<DetailPage> {

  @override
  String strTitle() => '车辆详情';

  @override
  void initState() {
    initData();
    super.initState();
  }

  initData() async {
    setData(widget.item, "inspectData");
  }

  @override
  Widget body(BuildContext context) {
    final idata = getData("inspectData");
    if (idata == null) return WU.emptyWidget(context);
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(children: [
            TextTile(title: '司机姓名', info: idata['driverNm']),
            TextTile(title: '车辆类型', info: idata['carType']),
            TextTile(title: '车牌号', info: idata['carNo']),
            TextTile(title: '车辆类型名称', info: idata['carTypeLabel']),
            TextTile(title: '运单日期', info: idata['billDate']),
            TextTile(title: '司机电话', info: idata['driverPhone']),
            TextTile(title: '单据类别', info: idata['type'].toString()),
            TextTile(title: '运单编号', info: idata['billNo'].toString()),
          ], isSingle: false),
        ],
      ),
    );
  }
}