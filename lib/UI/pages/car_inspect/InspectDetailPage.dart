import 'package:dio/dio.dart';
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/base/RequestStateProvider.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:flutter/material.dart';

import '../../../base/BaseViewModel.dart';
import '../../../common/global.dart';
import '../../../common/http.dart';
import '../../../dnc/widget/components/Pop.dart';
import '../../../dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';

import 'UpdateBoundPage.dart';

class InspectDetailPage extends StatefulWidget {
  final Map<String, dynamic> item;
  const InspectDetailPage(this.item, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return InspectDetailPageState();
  }
}

class InspectDetailPageState extends BasePageState<InspectDetailPage>
    with RequestStateProvider {
  final InspectViewModel _model = InspectViewModel();

  WidgetData<String> grossWeightController = WidgetData<String>();
  WidgetData<String> tareWeightController = WidgetData<String>();
  WidgetData<String> netWeightController = WidgetData<String>();
  String pictureFileController = '';

  List dataList=[];

  @override
  String strTitle() => '检车详情';

  @override
  Widget content(BuildContext context, contentData) {
    final data = contentData['abc'];
    final wayBillInfo = DU.safeMap(data, ['wayBillInfo']);
    final checkDetail = DU.safeList(data, ['checkDetail']);

    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 1. 运单信息
          TileCard(
            children: [
              Text("运单信息", style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              TextTile(title: '司机姓名', info: DU.safeString(wayBillInfo, ['driverNm'])),
              TextTile(title: '车辆类型', info: DU.safeString(wayBillInfo, ['carType', 'label'])),
              TextTile(title: '车牌号', info: DU.safeString(wayBillInfo, ['carNo'])),
              TextTile(title: '运单日期', info: DU.safeString(wayBillInfo, ['billDate'])),
              TextTile(title: '司机电话', info: DU.safeString(wayBillInfo, ['driverPhone'])),
              TextTile(title: '单据类别', info: DU.safeString(wayBillInfo, ['type'])),
              TextTile(title: '运单编号', info: DU.safeString(wayBillInfo, ['billNo'])),
            ],
            isSingle: false,
          ),

          const SizedBox(height: 20),

          // 2. 验车细明
          TileCard(
            children: [
              Text("验车细明", style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              ...checkDetail.map((item) {
                String checkItem = DU.safeString(item, ['checkItem']);
                item['itemQualified'] =  item['itemQualified']??'1';

                return Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: Text(checkItem)),
                    Expanded(
                      flex: 3,
                      child: Row(
                        children: [
                          Radio<String>(
                            value:  '1',
                            groupValue:  item['itemQualified'],
                            onChanged: (val) {
                              print(val);
                              setState(() {
                                item['itemQualified'] =  val!;
                              });
                            },
                          ),
                          const Text('合格'),
                          const SizedBox(width: 10),
                          Radio<String>(
                            value:  '2',
                            groupValue:  item['itemQualified'],
                            onChanged: (val) {
                              setState(() {
                                item['itemQualified'] =  val!;
                              });
                            },
                          ),
                          const Text('不合格'),
                        ],
                      ),
                    )
                  ],
                );
              }).toList(),
            ],
            isSingle: false,
          ),

          const SizedBox(height: 20),

          // 3. 磅单信息录入
          shortcutsButtonBuilder(context,dataList),

          // 4. 按钮区（返回、提交）
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: TileButton(
                  name: "返回",
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: TileButton(
                  name: "提交",
                  onPressed: onSubmit,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget shortcutsButtonBuilder(BuildContext context,List list) {

    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: dataList.map<Widget>((item) {

          final List<String> urls = item['pictureFile'] != null && item['pictureFile'].isNotEmpty
              ? item['pictureFile'].split(',')
              : [];  // 如果是 null 或空，就使用空列表
          return Column(
            children: [
              TileCard(
                children: [
                  Text(
                    "磅单信息",
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  TextTile(
                    title: '毛重(吨)',
                    info: DU.safeString(item, ['grossWeight'],'无数据'),
                  ),
                  TextTile(
                    title: '皮重(吨)',
                    info: DU.safeString(item, ['tareWeight'],'无数据'),
                  ),
                  TextTile(
                    title: '净重(吨)',
                    info: '${item['netWeight'] != null ? "${item['netWeight']}" : "无数据"}',
                  ),
                  GestureDetector(
                    onTap: () {
                      // 当图片被点击时，显示一个全屏的图片查看器
                    },
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(5),
                      ),
                      child: Wrap(
                        spacing: 12,
                        children: urls.map((url)  {
                          return  InkWell(
                            child:   Image.network(
                              url,
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                return Center(child: Text('图片无法显示'));
                              },
                              height: 100,
                            ),
                            onTap: (){
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return Dialog(
                                    child: Container(
                                      width: double.infinity,
                                      height: double.infinity,
                                      child: Image.network(
                                        url,
                                        fit: BoxFit.contain,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Center(child: Text('图片无法显示'));
                                        },
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          );
                        }).toList(),
                      ),
                    ),
                  ),

                  // 另一种显示图片的方法
                  // Container(
                  //   padding: EdgeInsets.symmetric(horizontal: 16.0),
                  //   child: Container(
                  //     margin: EdgeInsets.symmetric(horizontal: 8.0),
                  //     child: urls.isNotEmpty
                  //         ? Row(
                  //       children: urls.map((url) {
                  //         return Padding(
                  //           padding: EdgeInsets.symmetric(horizontal: 4.0),
                  //           child: Image.network(
                  //             url,
                  //             width: 96,
                  //             // height: 56,
                  //             fit: BoxFit.contain,
                  //           ),
                  //         );
                  //       }).toList(),
                  //     )
                  //         : Text('暂无图片', style: TextStyle(color: Colors.grey)),
                  //   ),
                  // ),

                  const SizedBox(height: 10),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // 修改
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 6,
                        child: TileButton(
                          name: "修改",
                          onPressed: () async{
                            final result = await WU.nextPage(context, UpdateBoundPage(boundData: item));
                            print("---3333--提交成功${result}");
                            if(result["type"] =='ok'){

                              dynamic result = await Request(
                                RequestType.GET,
                                '/appBussiness/menwei/checkWayBillDetail',
                                params: () => {
                                  'billNo': DU.safeString(widget.item, ['billNo']),
                                  'type': DU.safeString(widget.item, ['type'])
                                },
                              ).execute(showLoading: true);
                              if (result is DioException) {
                                Toast.show("异常提交失败，请重试");
                                return;
                              }

                              dataList = DU.safeList(result, ['boundList']);
                              setState(() {

                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 10),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
              const SizedBox(height: 20),
            ],
          );
        }).toList(),
      ),
    );
    // });
  }

  // 提交检车细明
  onSubmit() async {
    await _submitCheckDetails(() {
      Toast.show('提交成功');
      Navigator.pop(context);
    });
  }

  Future<void> _submitCheckDetails(Function successCallback) async {
    final data = contentData['abc'];
    final checkDetail = DU.safeList(data, ['checkDetail']);

    var params = {
      "billNo": data?['wayBillInfo']?['billNo'],
      "type": data?['wayBillInfo']?['type'],
      "checkDetail": checkDetail,
    };

    _model.checkWayBillItems(params, success: () {
      successCallback();
    }, fail: (err) {
      Toast.show('检车细明提交失败: $err');
    });
  }

  // 获取检车详情
  @override
  List<Request>? onCreateRequest() {
    final url = "/appBussiness/menwei/checkWayBillDetail";
    return [
      Request(
        RequestType.GET,
        url,
        params: () => {
          'billNo': DU.safeString(widget.item, ['billNo']),
          'type': DU.safeString(widget.item, ['type'])
        },
        tag: 'abc',
      ),
    ];
  }
  @override
  void onRequestAfter(first, contentData) {
    final data = contentData['abc'];
    dataList = DU.safeList(data, ['boundList']);
  }
}

class InspectViewModel extends BaseViewModel {

  checkWayBillItems(Map<String, dynamic> param, {VoidCallback? success, Function(dynamic err)? fail}) {
    final url = "/appBussiness/menwei/checkWayBillItems";
    param["entId"] = Global.entId;

    Http().post(url, param, success: (json) {
      if (success != null) {
        success();
      }
    }, fail: (reason, statuscode) {
      if (fail != null) {
        fail(reason);
      } else {
        Toast.show(reason);
      }
    });
  }
}