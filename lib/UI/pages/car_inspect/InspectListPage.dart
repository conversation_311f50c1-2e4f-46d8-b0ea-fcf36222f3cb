import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';

import '../../../base/BaseRefreshListViewModel.dart';
import '../../../common/http.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import 'DetailPage.dart';
import 'InspectDetailPage.dart';
import 'InspectSearchPage.dart';

class InspectListPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return InspectListPageState();
  }
}

class InspectListPageState extends BasePageState<InspectListPage> {
  InspectListViewModel _listModel = InspectListViewModel();

  @override
  String strTitle() => '待检车';

  @override
  void initState() {
    super.initState();
    _listModel = InspectListViewModel();
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
            child: CustomItemListWidget(
              model: _listModel,
              itemBuilder: itemBuilder,
              enablePullDown: true,
              enablePullUp: true,
              showSeparator: false,
            )),
      ],
    );
  }

  @override
  List<Widget> createActions(BuildContext context) => [
    IconButton(
      icon: Icon(Icons.search),
      tooltip: '待检车搜索',
      onPressed: () async {
        final result = await WU.nextPage(context, InspectSearchPage());
        if (result == null) return;
        Map data = result;
        _listModel.carNo = data['carNo'];
        _listModel.driverPhone = data['driverPhone'];
        _listModel.driverIdCard = data['driverIdCard'];
        _listModel.refresh();
      },
    ),
  ];

  Widget itemBuilder(BuildContext context, item, int index, bool isEnd) {
    final isQualifiedValue = item['isQualified'];

    String checkResultText;
    if (isQualifiedValue == null || isQualifiedValue.toString() == '0') {
      checkResultText = '待检验';
    } else if (isQualifiedValue.toString() == '1') {
      checkResultText = '合格';
    } else if (isQualifiedValue.toString() == '2') {
      checkResultText = '不合格';
    } else {
      checkResultText = '待检验';
    }

    // 待检验时显示“验车”，不合格时显示“重新验车”
    final buttonText = (checkResultText == '待检验') ? '验车' : '重新验车';

    var text;
    var textStyle;
    bool readOnly = item['status'] == "3";

    return SimpleCard(
      isSingle: false,
      tip: text,
      showShadow: false,
      style: textStyle,
      body: Container(
        padding: EdgeInsets.all(8),
        child: Column(
          children: [
            infoLabel(context, '运单编号:', item['billNo']),
            infoLabel(context, '运单日期:', item['billDate']),
            infoLabel(context, '车牌号:', item['carNo']),
            infoLabel(context, '司机:', item['driverNm']),
            infoLabel(context, '联系电话:', item['driverPhone']),
            infoLabel(context, '司机身份证:', item['driverIdCard']),
            infoLabel(context, '检车结果:', checkResultText),

            Column(
              children: [
                Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
                Container(
                  padding: EdgeInsets.fromLTRB(20, 12, 20, 6),
                  child: ContentButton(
                    name: buttonText,
                    minSize: Size(150, 40),
                    onPressed: () => toDetail2(context, item),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
      onTap: () => toDetail(context, item),
    );
  }

  infoLabel(BuildContext context, String name, String value) {
    return Container(
      padding: EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          Container(
            constraints: BoxConstraints(minWidth: 100),
            child: Text(
              name,
              style: Theme.of(context).primaryTextTheme.headline2,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: Theme.of(context).primaryTextTheme.headline2,
            ),
          ),
        ],
      ),
    );
  }

  toDetail(BuildContext context, var item) async {
    final result =  await WU.nextPage(context, DetailPage(item));
    if (result != null) {
      _listModel.refresh();
    }
  }

  toDetail2(BuildContext context, var item) async {
    final result =  await WU.nextPage(context, InspectDetailPage(item));

    if (result != null) {
      _listModel.refresh();
    }
  }
}

class InspectListViewModel extends BaseRefreshListViewModel {
  final url = '/appBussiness/menwei/checkWayBillList';

  String carNo = '';
  String driverPhone = '';
  String driverIdCard = '';

  @override
  Future<List> loadData({int pageNum = 1, int pageSize = 20}) async {
    var responseData = await Http().get(url, {
      "page": pageNum,
      "perPage": pageSize,
      "carNo": carNo,
      "driverPhone": driverPhone,
      "driverIdCard": driverIdCard
    });

    List list = responseData['items'];
    return list;
  }
}