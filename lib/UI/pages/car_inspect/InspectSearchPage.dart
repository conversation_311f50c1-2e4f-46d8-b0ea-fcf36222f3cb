import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../../../dnc/widget/components/Widget.dart';

class InspectSearchPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return InspectSearchPageState();
  }
}

class InspectSearchPageState extends BasePageState<InspectSearchPage> {
  WidgetData<String> carNo = WidgetData<String>();
  WidgetData<String> driverPhone = WidgetData();
  WidgetData<String> driverIdCard  = WidgetData<String>();
  @override
  String strTitle() => '待检车搜索';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(
            children: [
              InputTile(title: '车牌号', hint: '请输入车牌号', wd: carNo),
              InputTile(
                  title: '司机电话',
                  wd: driverPhone,
                  hint: '请输入司机电话',
                  limit: 11,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp("[0-9]"),
                    ),
                    FilteringTextInputFormatter.deny(
                      RegExp("[\\s]"),
                    )
                  ],
                  keyboardType: TextInputType.phone,
                  important: false,
                  readOnly: false),
              InputTile(
                  title: '司机身份证',
                  hint: '请输入司机身份证',
                  wd: driverIdCard,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')), // 允许字母和数字
                  ],
                  keyboardType: TextInputType.text),
            ],
            isSingle: true,
          ),
          SizedBox(height: 10),
          TileButton(name: "搜索", onPressed: onSubmit),
          SizedBox(height: 12)
        ],
      ),
    );
  }

  onSubmit() {
    var info = {
      "carNo": carNo.data != null ? carNo.data : "",
      "driverPhone": driverPhone.data != null ? driverPhone.data : "",
      "driverIdCard": driverIdCard.data != null ? driverIdCard.data : ""
    };
    Navigator.pop(context, info);
  }
}