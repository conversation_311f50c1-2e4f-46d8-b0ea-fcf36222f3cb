import 'package:dio/dio.dart';
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../../../dnc/network/Http.dart';
import '../../../dnc/widget/components/File.dart';
import '../../../dnc/widget/components/Pop.dart';

class UpdateBoundPage extends StatefulWidget {
  final dynamic boundData;

  const UpdateBoundPage({
    Key? key,
    required this.boundData,
  }) : super(key: key);

  @override
  _UpdateBoundPageState createState() => _UpdateBoundPageState();
}

class _UpdateBoundPageState extends State<UpdateBoundPage> {
  late WidgetData<String> id;
  late WidgetData<String> detailId;
  late WidgetData<String> billNo;
  late WidgetData<String> type;
  late WidgetData<String> grossWeightController;
  late WidgetData<String> tareWeightController;
  late WidgetData<String> netWeightController;
  late String pictureFileController;
  late WidgetList<FileData> picUrlDataWD;

  @override
  void initState() {
    super.initState();
    initData(widget.boundData);
  }

  void initData(dynamic data) {
    if (data == null) {
      return;
    }
    id = WidgetData<String>(DU.safeString(data, ['id']));
    detailId = WidgetData<String>(DU.safeString(data, ['detailId']));
    billNo = WidgetData<String>(DU.safeString(data, ['billNo']));
    type = WidgetData<String>(DU.safeString(data, ['type']));
    grossWeightController = WidgetData<String>(DU.safeString(data, ['grossWeight']));
    tareWeightController = WidgetData<String>(DU.safeString(data, ['tareWeight']));
    netWeightController = WidgetData<String>(DU.safeString(data, ['netWeight']));
    pictureFileController = DU.safeString(data, ['pictureFile']);
    picUrlDataWD = WidgetList<FileData>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('修改磅单'),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: <Widget>[
            InputTile(
              title: '毛重(吨)',
              wd: grossWeightController,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9]")),],
              keyboardType: TextInputType.number,
              important: true,
            ),
            InputTile(
              title: '皮重(吨)',
              wd: tareWeightController,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9]")),],
              keyboardType: TextInputType.number,
              important: true,
            ),
            InputTile(
              title: '净重(吨)',
              wd: netWeightController,
              inputFormatters: [FilteringTextInputFormatter.allow(RegExp("[0-9]")),],
              keyboardType: TextInputType.number,
              important: true,
            ),
            PhotoSelectorTile(
              title: '上传磅单图片',
              wl: picUrlDataWD,
              readOnly: false,
              important: true,
              bottomLine: false,
            ),
            TileButton(
              name: '提交',
              onPressed: () => onSubmit(context),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> onSubmit(BuildContext context) async {
    if (!picUrlDataWD.isEmpty) {
      // 首先尝试上传图片
      bool result = await Http().uploadWidgetList(wl: picUrlDataWD, tag: 'bound');
      if (!result) {
        Toast.show("图片上传失败，请重试");
        return;
      }
      // 图片上传成功后，提取和构建完整的URLs
      List<String> fullUrls = picUrlDataWD.list.where((fileData) => fileData.url != null)
          .map((fileData) => fileData.url!)
          .toList();
      // 如果有有效的URL，使用逗号拼接成一个字符串
      if (fullUrls.isNotEmpty) {
        pictureFileController = fullUrls.join(",");
      }
    }

    final result = await Request(RequestType.POST, '/waybill/driver/updateBound', post: () {
      return {
        'id': id.data,
        'detailId': detailId.data,
        'billNo': billNo.data,
        'type': type.data,
        'grossWeight': grossWeightController.data,
        'tareWeight': tareWeightController.data,
        'netWeight': netWeightController.data,
        'pictureFile': pictureFileController,
      };
    }).execute(showLoading: true);
    if (result is DioException) {
      Toast.show("提交失败，请检查网络并重试");
      return;
    }
    print("---bangdan1-1111-提交成功");
    WU.closePage(context, arguments: {"type":'ok', 'params':{
      'grossWeight': grossWeightController.data,
      'tareWeight': tareWeightController.data,
      'netWeight': netWeightController.data,
      'pictureFile': pictureFileController,
    }});
  }
}