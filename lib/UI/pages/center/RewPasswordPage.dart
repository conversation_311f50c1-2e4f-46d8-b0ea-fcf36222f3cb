import 'dart:async';
import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:flutter/material.dart';

import '../../../common/global.dart';
import '../../../common/http.dart';
import '../../../dnc/network/Request.dart';
import '../../../dnc/plugs/Encryptor.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/advance/PasswordTextFormField.dart';
import '../../../dnc/widget/base/BasePage.dart';
import '../../../dnc/widget/base/RequestStateProvider.dart';
import '../../../dnc/widget/components/Button.dart';
import '../../../dnc/widget/components/Pop.dart';

class RewPasswordPage extends StatefulWidget {
  RewPasswordPage();

  @override
  State<StatefulWidget> createState() => RewPasswordPageState();
}

class RewPasswordPageState extends BasePageState<RewPasswordPage>
    with RequestStateProvider {
  TextEditingController eCOldPTC = new TextEditingController();
  TextEditingController eCNewP1TC = new TextEditingController();
  TextEditingController eCNewP2TC = new TextEditingController();

  @override
  String strTitle() => '修改密码';

  @override
  List<Request>? onCreateRequest() {
    return null;
  }

  @override
  void onRequestAfter(first, contentData) {}

  @override
  Widget content(BuildContext context, contentData) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            color: Colors.white,
            padding: EdgeInsets.only(left: 20, right: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Container(
                        // color: MyColors.red,
                        width: 70,
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text("原密码",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: ColorsI.black66,
                                  height: 2,
                                  fontSize: 16,
                                )))),
                    Expanded(
                      child: PasswordTextFormField(
                        controller: eCOldPTC,
                        obscureText: true,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                        // color: MyColors.red,
                        width: 70,
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text("新密码",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.black,
                                  height: 2,
                                  fontSize: 16,
                                )))),
                    Expanded(
                      child: PasswordTextFormField(
                        controller: eCNewP1TC,
                        obscureText: true,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Container(
                        // color: MyColors.red,
                        width: 70,
                        child: Align(
                            alignment: Alignment.centerLeft,
                            child: Text("确认密码",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.black,
                                  height: 2,
                                  fontSize: 16,
                                )))),
                    Expanded(
                      child: PasswordTextFormField(
                        controller: eCNewP2TC,
                        obscureText: true,
                        //校验密码
                      ),
                    ),
                  ],
                ),
                Container(
                  margin: EdgeInsets.fromLTRB(0, 24, 0, 0),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Container(
                          child: CustomButton(
                            name: '取消',
                            padding: EdgeInsets.symmetric(
                                vertical: 10, horizontal: 56),
                            textColor: Colors.black38,
                            bgColor: Colors.black12,
                            onPressed: () => returnButton(context),
                          ),
                        ),
                        Container(
                          // width: 160,
                          // height: 40,
                          child: CustomButton(
                            name: '提交',
                            padding: EdgeInsets.symmetric(
                                vertical: 10, horizontal: 56),
                            textColor: Color(0xffffffff),
                            bgColor: Color(0xff3A86DF),
                            onPressed: () => commitButton(context),
                          ),
                        ),
                      ]),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> commitButton(BuildContext context) async {
    bool result = false;

    if (eCOldPTC.text.isEmpty) {
      Toast.show('请输入原密码');
      return;
    }
    if (eCNewP1TC.text.isEmpty) {
      Toast.show('请输入新密码');
      return;
    }
    if (eCNewP2TC.text.isEmpty) {
      Toast.show('请输入确认密码');
      return;
    }
    if (eCNewP1TC.text != eCNewP2TC.text) {
      Toast.show('两次密码输入不一致');
      return;
    }

    result = await toEdit();

    if (result) {
      Toast.show('提交成功');
      WU.closePage(context);
    }
  }

  Future<bool> toEdit() async {
    String oldKey = Global.loginIsEncrypt
        ? await Encryptor().encrypt(eCOldPTC.text)
        : eCOldPTC.text;
    String newKey = Global.loginIsEncrypt
        ? await Encryptor().encrypt(eCNewP1TC.text)
        : eCNewP1TC.text;
    final userInfo = {"credentials": oldKey, "newPassword": newKey, "verificationMode":"password"};
    return onEdit(context, userInfo);
  }

  // Future<bool> onEdit(BuildContext context, dynamic userInfo) async {
  //   Completer<bool> completer = Completer();
  //   await Request(
  //     RequestType.POST,
  //     '/user/modify-password',
  //     post: () => userInfo,
  //     interceptor: (data) {
  //       if (data == null) {
  //         completer.complete(true);
  //       } else {
  //         completer.complete(false);
  //       }
  //     },
  //   ).execute(showLoading: true);
  //
  //   return completer.future;
  // }

  Future<bool> onEdit(BuildContext context, dynamic userInfo) async {
    Completer<bool> completer = Completer();
    final url = '/user/modify-password';
    await Http().patchs(url,userInfo, fail: (reason, code) {
      Toast.show(reason);
    });
    completer.complete(true);
    return completer.future;
  }

  Future<void> returnButton(BuildContext context) async {
    Navigator.pop(context);
  }
}
