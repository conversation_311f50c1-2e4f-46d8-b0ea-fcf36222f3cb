import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../dnc/network/Request.dart';
import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/base/BasePage.dart';
import '../../../dnc/widget/base/RequestStateProvider.dart';
import '../../../dnc/widget/components/Button.dart';
import '../../../dnc/widget/components/Pop.dart';
import '../../../dnc/widget/components/StateTile.dart';
import '../../../dnc/widget/components/Tile.dart';
import '../../../dnc/widget/components/Widget.dart';

class RewUserEditPage extends StatefulWidget {
  final String? arguments;

  RewUserEditPage({this.arguments});

  @override
  State<StatefulWidget> createState() => RewUserEditPageState();
}

class RewUserEditPageState extends BasePageState<RewUserEditPage>
    with RequestStateProvider {
  WidgetData<String> eCNameWD = WidgetData();
  WidgetData<String> eCUserNameWD = WidgetData();
  WidgetData<String> eCPhoneWD = WidgetData();
  WidgetData<String> ecAgeWD = WidgetData();
  WidgetData selectSexWD = WidgetData();
  WidgetData<String> eCEmailWD = WidgetData();
  WidgetData<String> eCDepartmentWD = WidgetData();
  WidgetData<String> eCZhiWUWD = WidgetData();
  WidgetData<String> dpIdWD = WidgetData();

  @override
  String strTitle() => '修改个人信息';

  @override
  List<Request>? onCreateRequest() {
    return [
      Request(
        RequestType.GET,
        '/qyaq/user/info',
        tag: 'pitfallDetailData',
        params: () => {},
      )
    ];
  }

  @override
  void onRequestAfter(first, contentData) {
    final data = contentData['pitfallDetailData'];
    eCNameWD.data = DU.safeString(data, ['name']);
    eCUserNameWD.data = DU.safeString(data, ['username']);
    eCPhoneWD.data = DU.safeString(data, ['mobile']);
    ecAgeWD.data =
        DU.safeString(data, ['age']) == "" ? "" : DU.safeString(data, ['age']);
    selectSexWD.data = {
      'code': DU.safeString(data, ['sex'])
    };
    eCEmailWD.data = DU.safeString(data, ['email']);
    eCDepartmentWD.data = DU.safeString(data, ['departmentName']);
    eCZhiWUWD.data = DU.safeString(data, ['duty']);
    dpIdWD.data = DU.safeString(data, ['departmentId']);
  }

  @override
  Widget content(BuildContext context, contentData) {
    final data = contentData['pitfallDetailData'];
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            color: Colors.white,
            padding: EdgeInsets.only(left: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.only(right: 24),
                  child: TextTile(
                    title: '用户名',
                    info: DU.safeString(data, ['username']),
                  ),
                ),
                InputTile(
                    title: '姓名',
                    wd: eCNameWD,
                    hint: '请输入姓名',
                    limit: 32,
                    important: false,
                    readOnly: false),
                SingleSelectorTile(
                  title: '性别',
                  data: () async => [
                    {'name': '男', 'code': '1'},
                    {'name': '女', 'code': '2'},
                  ],
                  wd: selectSexWD,
                  important: false,
                  onChange: (typeData) {
                    setState(() {});
                  },
                ),
                InputTile(
                    title: '年龄',
                    wd: ecAgeWD,
                    hint: '请输入年龄',
                    limit: 32,
                    important: false,
                    readOnly: false,
                    inputFormatters: <TextInputFormatter>[
                      FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
                      //限制长度
                    ]),
                InputTile(
                    title: '手机号',
                    wd: eCPhoneWD,
                    hint: '请输入手机号',
                    limit: 32,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp("[0-9]"),
                      ),
                      FilteringTextInputFormatter.deny(
                        RegExp("[\\s]"),
                      )
                    ],
                    important: false,
                    readOnly: false),
                InputTile(
                    title: '电子邮箱',
                    wd: eCEmailWD,
                    hint: '请输入电子邮箱',
                    limit: 32,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(30),
                      FilteringTextInputFormatter.deny(
                        RegExp("[\\s]"),
                      )
                    ],
                    important: false,
                    readOnly: false),
                Container(
                  padding: EdgeInsets.only(right: 24),
                  child: TextTile(
                    title: '部门',
                    info: DU.safeString(data, ['departmentName']),
                  ),
                ),
                InputTile(
                    title: '职务',
                    wd: eCZhiWUWD,
                    hint: '请输入职务',
                    limit: 32,
                    important: false,
                    readOnly: false),
                Container(
                  margin: EdgeInsets.fromLTRB(0, 25, 20, 11),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Container(
                          child: CustomButton(
                            name: '取消',
                            padding: EdgeInsets.symmetric(vertical:10,horizontal:56),
                            textColor: Colors.black38,
                            bgColor: Colors.black12,
                            onPressed: () => returnButton(context, data),
                          ),
                        ),
                        Container(
                          child: CustomButton(
                            name: '提交',
                            padding: EdgeInsets.symmetric(vertical:10,horizontal:56),
                            textColor: Color(0xffffffff),
                            bgColor: Color(0xff3A86DF),
                            onPressed: () => commitButton(context, data),
                          ),
                        ),
                      ]),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> commitButton(
      BuildContext context, dynamic pitfallDetailData) async {
    bool result = false;

    RegExp exp = RegExp(
        r'^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\d{8}$');
    bool matched = exp.hasMatch(eCPhoneWD.data.toString());
    if (!matched) {
      Toast.show('手机号输入不正确');
      return;
    }
    if (eCEmailWD.data != null && eCEmailWD.data!.length > 0) {
      eCEmailWD.data = eCEmailWD.data?.trim();
      String regexEmail =
          "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*\$";
      if (!RegExp(regexEmail).hasMatch(eCEmailWD.data.toString())) {
        Toast.show('邮箱格式不正确');
        return;
      }
    }
    result = await toEdit(context, pitfallDetailData);
    if (result) {
      Toast.show('提交成功');
      WU.closePage(context);
    } else {
      Toast.show('提交失败');
    }
  }

  Future<bool> toEdit(BuildContext context, dynamic pitfallDetailData) async {
    final userInfo = Map.of(pitfallDetailData);
    userInfo['name'] = eCNameWD.data;
    userInfo['age'] = int.parse(ecAgeWD.data ?? '');
    userInfo['mobile'] = eCPhoneWD.data;
    userInfo['email'] = eCEmailWD.data;
    userInfo['duty'] = eCZhiWUWD.data;
    userInfo['sex'] = selectSexWD.data["code"];
    return onEdit(context, userInfo);
  }

  Future<bool> onEdit(BuildContext context, dynamic userInfo) async {
    Completer<bool> completer = Completer();
    await Request(RequestType.POST, '/user/update', post: () => userInfo)
        .execute(showLoading: true);
    completer.complete(true);
    return completer.future;
  }

  Future<void> returnButton(
      BuildContext context, dynamic pitfallDetailData) async {
    Navigator.pop(context);
  }
}
