import 'package:flutter/material.dart';
import 'package:ent_secutity_app/model/user_info.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';

class UserDetailPage extends StatefulWidget {
  final UserInfo user;

  const UserDetailPage({Key? key, required this.user}) : super(key: key);

  @override
  State<UserDetailPage> createState() => _UserDetailPageState();
}

class _UserDetailPageState extends BasePageState<UserDetailPage> {
  @override
  String strTitle() => '用户详情';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Center(
            child: CircleAvatar(
              radius: 40,
              backgroundImage:
                  AssetImage('assets/images/module/other/user_manage.png'),
            ),
          ),
          Si<PERSON><PERSON><PERSON>(height: 20),
          TileCard(
            children: [
              TextTile(title: '姓名', info: widget.user.name),
              TextTile(title: '用户名', info: widget.user.username),
              TextTile(title: '手机号', info: widget.user.mobile),
              TextTile(title: '邮箱', info: widget.user.email),
              TextTile(title: '职位', info: widget.user.duty),
              TextTile(title: '部门', info: widget.user.departmentName),
              TextTile(title: '性别', info: widget.user.sex == 1 ? '男' : '女'),
              TextTile(title: '年龄', info: widget.user.age.toString()),
            ],
            isSingle: true,
          ),
        ],
      ),
    );
  }
}
