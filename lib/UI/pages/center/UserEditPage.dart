import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ent_secutity_app/model/user_info.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';
import 'package:ent_secutity_app/viewmodel/user/UserViewModel.dart';

class UserEditPage extends StatefulWidget {
  final UserInfo? user;

  const UserEditPage({Key? key, this.user}) : super(key: key);

  @override
  State<UserEditPage> createState() => _UserEditPageState();
}

class _UserEditPageState extends BasePageState<UserEditPage> {
  late UserInfo user;
  final UserViewModel _viewModel = UserViewModel();

  WidgetData<String> nameWd = WidgetData<String>();
  WidgetData<String> usernameWd = WidgetData<String>();
  WidgetData<String> mobileWd = WidgetData<String>();
  WidgetData<String> emailWd = WidgetData<String>();
  WidgetData<String> dutyWd = WidgetData<String>();
  WidgetData<String> departmentNameWd = WidgetData<String>();
  WidgetData<String> ageWd = WidgetData<String>();
  int sexValue = 0;

  @override
  void initState() {
    super.initState();
    user = widget.user ?? UserInfo();

    nameWd.data = user.name;
    usernameWd.data = user.username;
    mobileWd.data = user.mobile;
    emailWd.data = user.email;
    dutyWd.data = user.duty;
    departmentNameWd.data = user.departmentName;
    ageWd.data = user.age.toString();
    sexValue = user.sex;
  }

  @override
  String strTitle() => widget.user == null ? '添加用户' : '编辑用户';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          TileCard(
            children: [
              InputTile(
                  title: '姓名', hint: '请输入姓名', wd: nameWd, important: true),
              InputTile(
                  title: '用户名',
                  hint: '请输入用户名',
                  wd: usernameWd,
                  important: true),
              InputTile(
                title: '手机号',
                hint: '请输入手机号',
                wd: mobileWd,
                limit: 11,
                important: true,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                  FilteringTextInputFormatter.deny(RegExp("[\\s]")),
                ],
                keyboardType: TextInputType.phone,
              ),
              InputTile(title: '邮箱', hint: '请输入邮箱', wd: emailWd),
              InputTile(title: '职位', hint: '请输入职位', wd: dutyWd),
              InputTile(title: '部门', hint: '请输入部门', wd: departmentNameWd),
              InputTile(
                title: '年龄',
                hint: '请输入年龄',
                wd: ageWd,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                ],
                keyboardType: TextInputType.number,
              ),
              BaseTile(
                head: Text('性别',
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                        color: Colors.black54)),
                body: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Radio(
                      value: 1,
                      groupValue: sexValue,
                      onChanged: (value) {
                        setState(() {
                          sexValue = value as int;
                        });
                      },
                    ),
                    Text('男'),
                    SizedBox(width: 20),
                    Radio(
                      value: 0,
                      groupValue: sexValue,
                      onChanged: (value) {
                        setState(() {
                          sexValue = value as int;
                        });
                      },
                    ),
                    Text('女'),
                  ],
                ),
              ),
            ],
            isSingle: true,
          ),
          SizedBox(height: 20),
          TileButton(name: "保存", onPressed: onSubmit),
        ],
      ),
    );
  }

  void onSubmit() {
    if (nameWd.data == null || nameWd.data!.isEmpty) {
      Toast.show('请输入姓名');
      return;
    }

    if (usernameWd.data == null || usernameWd.data!.isEmpty) {
      Toast.show('请输入用户名');
      return;
    }

    if (mobileWd.data == null || mobileWd.data!.isEmpty) {
      Toast.show('请输入手机号');
      return;
    }

    user.name = nameWd.data!;
    user.username = usernameWd.data!;
    user.mobile = mobileWd.data!;
    user.email = emailWd.data ?? '';
    user.duty = dutyWd.data ?? '';
    user.departmentName = departmentNameWd.data ?? '';
    user.age = int.tryParse(ageWd.data ?? '0') ?? 0;
    user.sex = sexValue;

    // 实际项目中应调用保存API
    if (widget.user == null) {
      // 添加用户
      _viewModel.editUserInfo(user.toMap(), subSuccess: () {
        Navigator.pop(context, 'refresh');
      });
    } else {
      // 编辑用户
      _viewModel.editUserInfo(user.toMap(), subSuccess: () {
        Navigator.pop(context, 'refresh');
      });
    }
  }
}
