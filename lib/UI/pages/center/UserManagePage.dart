import 'package:flutter/material.dart';
import 'package:ent_secutity_app/model/user_info.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:ent_secutity_app/viewmodel/user/UserListViewModel.dart';
import 'UserSearchPage.dart';
import 'UserDetailPage.dart';
import 'UserEditPage.dart';

class UserManagePage extends StatefulWidget {
  UserManagePage({Key? key}) : super(key: key);

  @override
  State<UserManagePage> createState() => _UserManagePageState();
}

class _UserManagePageState extends BasePageState<UserManagePage> {
  UserListViewModel _listModel = UserListViewModel();
  String? searchUsername;

  @override
  void initState() {
    super.initState();
    _listModel = UserListViewModel();
  }

  @override
  String strTitle() => '用户管理';

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
            child: CustomItemListWidget(
          model: _listModel,
          itemBuilder: itemBuilder,
          enablePullDown: true,
          enablePullUp: true,
          showSeparator: false,
        )),
      ],
    );
  }

  @override
  List<Widget> createActions(BuildContext context) => [
        IconButton(
          icon: Icon(Icons.search),
          tooltip: '用户搜索',
          onPressed: () async {
            final result = await WU.nextPage(context, UserSearchPage());
            if (result == null) return;
            Map data = result;
            searchUsername = data['username'];
            _listModel.username = searchUsername;
            _listModel.refresh();
          },
        ),
      ];

  @override
  Widget? createFloatingActionButton(BuildContext context) =>
      FloatingActionButton(
        onPressed: () async {
          final result = await WU.nextPage(context, UserEditPage());
          if (result == 'refresh') {
            _listModel.refresh();
          }
        },
        child: Icon(Icons.add),
        tooltip: '添加用户',
      );

  Widget itemBuilder(BuildContext context, item, int index, bool isEnd) {
    final user = UserInfo.fromJson(item);
    final registrationTime = item['createTime'] != null
        ? DateTime.fromMillisecondsSinceEpoch(item['createTime'])
            .toString()
            .substring(0, 19)
        : '未知';

    return CustomCard(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundImage:
                    AssetImage('assets/images/module/other/user_manage.png'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(user.name,
                        style: const TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Text('手机号: ${user.mobile}',
                        style:
                            const TextStyle(fontSize: 14, color: Colors.grey)),
                    const SizedBox(height: 2),
                    Text('职位: ${user.duty}',
                        style:
                            const TextStyle(fontSize: 14, color: Colors.grey)),
                    const SizedBox(height: 2),
                    Text('注册时间: $registrationTime',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey)),
                  ],
                ),
              ),
            ],
          ),
          Divider(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton.icon(
                icon: Icon(Icons.edit, size: 18),
                label: Text('编辑'),
                onPressed: () async {
                  final result =
                      await WU.nextPage(context, UserEditPage(user: user));
                  if (result == 'refresh') {
                    _listModel.refresh();
                  }
                },
              ),
              SizedBox(width: 16),
              TextButton.icon(
                icon: Icon(Icons.delete, size: 18, color: Colors.red),
                label: Text('删除', style: TextStyle(color: Colors.red)),
                onPressed: () {
                  SDialog.beSure(
                    context,
                    content: '确定要删除用户 ${user.name} 吗？',
                    onBeSure: () async {
                      // 实际项目中应调用删除API
                      Toast.show('用户删除成功');
                      _listModel.refresh();
                    },
                  );
                },
              ),
            ],
          ),
        ],
      ),
      onTap: () {
        WU.nextPage(context, UserDetailPage(user: user));
      },
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      radius: 10,
      shadow: CustomCard.defShadow,
    );
  }
}
