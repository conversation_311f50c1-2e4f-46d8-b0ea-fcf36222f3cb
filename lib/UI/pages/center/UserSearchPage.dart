import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';

class UserSearchPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return UserSearchPageState();
  }
}

class UserSearchPageState extends BasePageState<UserSearchPage> {
  WidgetData<String> username = WidgetData<String>();

  @override
  String strTitle() => '搜索';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(
            children: [
              InputTile(title: '用户名称', hint: '请输入用户名称', wd: username),
            ],
            isSingle: true,
          ),
          SizedBox(height: 10),
          TileButton(name: "搜索", onPressed: onSubmit),
          SizedBox(height: 12)
        ],
      ),
    );
  }

  onSubmit() {
    var info = {
      "username": username.data != null ? username.data : "",
    };
    Navigator.pop(context, info);
  }
}
