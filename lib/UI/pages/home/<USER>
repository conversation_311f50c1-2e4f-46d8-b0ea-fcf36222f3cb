import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:ent_secutity_app/UI/pages/home/<USER>';
import 'package:ent_secutity_app/UI/pages/home/<USER>';
// import 'package:ent_secutity_app/UI/pages/home/<USER>';
import 'package:ent_secutity_app/common/AppConfig.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/advance/Search.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:ent_secutity_app/dnc/widget/base/Reader.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';
import 'package:shimmer/shimmer.dart';

import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/advance/Swiper.dart';
import '../../../dnc/widget/base/BasePage.dart';
import '../../../dnc/widget/components/Pop.dart';
import '../../../main.dart';
import '../../../viewmodel/home/<USER>';
// import '../../../viewmodel/home/<USER>'; //消息
// import '../dev_management/dev_account/AccountListPage.dart';
// import '../dev_management/dev_maintenance/MaintenanceListPage.dart';
// import '../dev_management/dev_services/ServicesListPage.dart';
// import '../education/training_management/TrainingManagementListPage.dart';
// import '../emergency_response/CreateEmergencyPage.dart';
// import '../pr_pitfall/CreatePitfall.dart';
// import '../task_list/WorkOrderListPage.dart';
import 'HomePage.dart';
// import 'TodoTasksPage.dart';
// import 'business_type/BusinessItemFactory.dart';

class HomeTab with HomePageTabMixin {
  final HomePageState pageState;

  HomeTab(this.pageState);

  @override
  PreferredSizeWidget appbar(BuildContext context) {
    return AppBar(
      leading: null,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
      title: HomeTabHeadWidget(),
    );
    // return PreferredSize(
    //     preferredSize: const Size(double.infinity, kToolbarHeight),
    //     child: SafeArea(
    //       child: Material(
    //         color: Theme.of(context).appBarTheme.backgroundColor,
    //         elevation: 4.0,
    //         shadowColor: Color(0xFF000000),
    //         child: HomeTabHeadWidget(),
    //       ),
    //     ));
  }

  @override
  Widget content(BuildContext context) {
    return HomeTabWidget(this.pageState);
  }

  Widget headMenuItemBuilder(
      BuildContext context, IconData data, String name, void Function() onTap) {
    return GestureDetector(
      child: Container(
        padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
        color: Colors.transparent,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(data, size: 24, color: Color(0x8a000000)),
            SizedBox(width: 8),
            Text(name,
                style: TextStyle(
                  color: Color(0x9a000000),
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                )),
          ],
        ),
      ),
      onTap: onTap,
    );
  }

  @override
  TabItem bottom(BuildContext context) {
    return TabItem(
      icon: Image.asset(
        'assets/images/home/<USER>',
        fit: BoxFit.fill,
      ),
      activeIcon: Image.asset(
        'assets/images/home/<USER>',
      ),
      title: "首页",
    );
  }
}

class HomeTabHeadWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return HomeTabHeadWidgetState();
  }
}

class HomeTabHeadWidgetState extends BaseStatefulWidgetState
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool isShow = false;

  @override
  void initState() {
    _animationController =
        AnimationController(duration: Duration(milliseconds: 100), vsync: this);
    _animation = Tween<double>(
      begin: 0,
      end: 0.125,
    ).chain(CurveTween(curve: Curves.easeIn)).animate(_animationController)
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          isShow = true;
        } else if (status == AnimationStatus.dismissed) {
          isShow = false;
        }
      });
    super.initState();
  }

  @override
  Widget body(BuildContext context) {
    late BuildContext tagContext;
    return Container(
      color: Theme.of(context).appBarTheme.backgroundColor,
      width: double.infinity,
      height: kToolbarHeight,
      alignment: Alignment.center,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center, // 主轴居中对齐
        children: [
          GestureDetector(
            child: Container(
              color: Colors.transparent,
              height: 24,
              padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
              child: Reader(
                enable: false,
                showMark: false,
                child: Shimmer.fromColors(
                  child: Image.asset(
                    'assets/images/login/title.png',
                    fit: BoxFit.contain,
                  ),
                  baseColor: Colors.white,
                  highlightColor: Colors.transparent,
                  direction: ShimmerDirection.ltr,
                  loop: 1,
                ),
              ),
            ),
            onTap: () async {
              if (!kDebugMode) {
                return;
              }

              // WU.nextPage(context, TrainingManagementListPage());
              // WU.nextPage(context, MaintenanceListPage());
              // WU.nextPage(context, ServicesListPage());
              // WU.nextPage(context, AccountListPage());


              // WU.nextPage(context, WorkOrderListPage());
              // WU.nextPage(context, StatisticsHomePage());
              // final imei = await IMEIInfo().get();
              // print(imei);
              // final result = IMEIInfo().getImeiLast('***************');
              // print(result);
              // WU.nextPage(context, TestPage());
            },
          ),
          // Expanded(
          //   child: SearchWidget(
          //     onTap: () {
          //       WU.nextPage(
          //           context,
          //           SearchPage(
          //             tag: 'homeSearch',
          //             resultData: (search) async {
          //               // search = '我';
          //               return await HomeViewModel().homeSearch(search);
          //             },
          //             resultBuilder: searchResultBuilder,
          //           ));
          //     },
          //   ),
          // ),
          // GestureDetector(
          //   child: Builder(builder: (context) {
          //     tagContext = context;
          //     return Container(
          //       padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
          //       child: RotationTransition(
          //         turns: _animation,
          //         child: Icon(CupertinoIcons.add_circled,
          //             size: 24, color: Colors.white),
          //       ),
          //     );
          //   }),
          //   onTap: () async {
          //     CursorDialog.show(tagContext, headMenuBuilder, onDismiss: () {
          //       _animationController.reverse();
          //     });
          //     _animationController.forward();
          //   },
          // ),
        ],
      ),
    );
  }

  prtS(int i) {
    String b = '';
    for (int a = 0; a < i; a++) {
      b += '*';
    }
    return b;
  }

  // Widget headMenuBuilder(BuildContext context) {
  //   return Container(
  //     width: 128 + 32,
  //     padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
  //     child: Column(
  //       mainAxisSize: MainAxisSize.max,
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         headMenuItemBuilder(context, CupertinoIcons.qrcode_viewfinder, '扫一扫',
  //             () {
  //           ScanQRCode.scan(context);
  //           CursorDialog.dismiss();
  //         }),
  //         Divider(color: Colors.black12, height: 1, thickness: 1),
  //         headMenuItemBuilder(
  //             context, CupertinoIcons.camera_viewfinder, '隐患随手拍', () {
  //           // WU.nextPage(context, CreatePitfall());
  //           CursorDialog.dismiss();
  //         }),
  //         Divider(color: Colors.black12, height: 1, thickness: 1),
  //         headMenuItemBuilder(context, CupertinoIcons.bell, '应急响应', () {
  //           // WU.nextPage(context, CreateEmergencyPage());
  //           CursorDialog.dismiss();
  //         }),
  //       ],
  //     ),
  //   );
  // }

  Widget headMenuItemBuilder(
      BuildContext context, IconData data, String name, void Function() onTap) {
    return GestureDetector(
      child: Container(
        padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
        color: Colors.transparent,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(data, size: 24, color: Color(0x8a000000)),
            SizedBox(width: 8),
            Text(name,
                style: TextStyle(
                  color: Color(0x9a000000),
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                )),
          ],
        ),
      ),
      onTap: onTap,
    );
  }

  // Widget searchResultBuilder(BuildContext context, dynamic result) {
  //   final List models = DU.safeList(result, ['menus']);
  //   return Container(
  //     padding: EdgeInsets.fromLTRB(8, 8, 8, 8),
  //     color: Colors.white,
  //     child: CustomScrollView(slivers: [
  //       SliverToBoxAdapter(
  //         child: Container(
  //           padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
  //           child: Text(
  //             '应用',
  //             style: TextStyle(
  //                 fontSize: 18.0,
  //                 color: Colors.black87,
  //                 fontWeight: FontWeight.w500),
  //           ),
  //         ),
  //       ),
  //       SliverToBoxAdapter(
  //         child: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             Container(
  //               padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
  //               width: double.infinity,
  //               child: Wrap(
  //                 direction: Axis.horizontal,
  //                 alignment: WrapAlignment.start,
  //                 spacing: 26,
  //                 runSpacing: 0,
  //                 children: models.map<Widget>((data) {
  //                   return MenuWidget(data);
  //                 }).toList(),
  //               ),
  //             ),
  //             Divider(
  //                 color: Theme.of(context).dividerColor,
  //                 height: 1,
  //                 thickness: 1),
  //           ],
  //         ),
  //       ),
  //       // SliverList(
  //       //   delegate: SliverChildBuilderDelegate(
  //       //       (BuildContext context, int index) {},
  //       //       childCount: 0),
  //       // ),
  //     ]),
  //   );
  // }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

class HomeTabWidget extends StatefulWidget {
  final HomePageState pageState;

  HomeTabWidget(this.pageState);

  @override
  State<StatefulWidget> createState() {
    return HomeTabWidgetState();
  }
}

class HomeTabWidgetState extends BaseStatefulWidgetState<HomeTabWidget>
    with TickerProviderStateMixin, RouteAware {
  dynamic todoTasksData;

  @override
  void initState() {
    // getTaskData();
    super.initState();
  }

  // getTaskData() async {
  //   if (!AC.MessageEnable) {
  //     return;
  //   }
  //   final data =
  //       await TodoTasksListViewModel().baseData(pageNum: 1, pageSize: 5);
  //   final PageData? page = PageData.of(context);
  //   if (page != null) {
  //     page.setData(data, 'todoTasks');
  //   }
  // }

  @override
  Widget body(BuildContext context) {
    final double barMaxHeight = 168;
    return CustomScrollView(
        physics: AlwaysScrollableScrollPhysics(
          parent: BouncingScrollPhysics(),
        ),
        slivers: <Widget>[
          SliverAppBar(
            backgroundColor: Theme.of(context).backgroundColor,
            automaticallyImplyLeading: false,
            pinned: false,
            expandedHeight: barMaxHeight,
            flexibleSpace: LayoutBuilder(
              builder: (BuildContext context, BoxConstraints constraints) {
                double barHeight =
                    constraints.biggest.height; // kToolbarHeight;
                var barOpacity = (barMaxHeight - barHeight) /
                    (barMaxHeight - kToolbarHeight);
                barOpacity = barOpacity < 0 ? 0 : barOpacity;
                return FlexibleSpaceBar(
                  titlePadding: EdgeInsets.all(0),
                  // title: Opacity(
                  //   opacity: barOpacity,
                  //   child: HomeTabHeadWidget(),
                  // ),
                  collapseMode: CollapseMode.parallax,
                  stretchModes: [
                    StretchMode.zoomBackground,
                    StretchMode.blurBackground,
                    StretchMode.fadeTitle,
                  ],
                  background: bannerBuilder(context),
                );
              },
            ),
            stretch: true,
          ),
          SliverToBoxAdapter(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                mineMenusBuilder(context),
                // StatisticsTile(),
                AC.MessageEnable ? todoTasksBuilder(context) : Container(),
              ],
            ),
          )
        ]);
  }

  Widget bannerBuilder(BuildContext context) {
    List banners = [];
    final PageData? page = PageData.reg(context);
    if (page != null) {
      banners = page.getData('banners', []);
    }
    return Container(
      padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: DU.isListEmpty(banners)
          ? Swiper(
        itemBuilder: (BuildContext context, int index) {
          return  Image.asset('assets/images/home/<USER>', fit: BoxFit.fill);
          //Image.network(DU.safeString(banners[index], ['picUrl']),fit: BoxFit.fill）
        },
        autoplay: true,
        loop: true,
        pagination: SwiperPagination(
          //指示器显示的位置
          alignment: Alignment.bottomCenter,
          // 位置 Alignment.bottomCenter 底部中间
          // 距离调整
          margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
          // 指示器构建
          builder: SwiperIndicator(),
        ),
        viewportFraction: 0.88,
        scale: 0.9,
        itemCount: 1,
      )
          : Container(),
    );
  }

  Widget mineMenusBuilder(BuildContext context) {
    List mineMenus = [];
    final PageData? page = PageData.reg(context);
    if (page != null) {
      mineMenus = page.getData('mineMenus', []);
    }
    // print('-安全计划--123---选择菜单-:${mineMenus}');
    return LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final side = (constraints.maxWidth - MenuWidget.menuSide * 4) / 4;
          return Container(
            alignment: Alignment.topLeft,
            color: Colors.white,
            padding: EdgeInsets.fromLTRB(side / 2, 32, side / 2, 32),
            child: Wrap(
                direction: Axis.horizontal,
                alignment: WrapAlignment.start,
                spacing: side,
                runSpacing: 8,
                children: [
                  ...mineMenus
                      .map(
                        (data) => MenuWidget(data),
                  )
                      .toList(),
                  // menuMoreBuilder(context),
                ]),
          );
        });
  }

  // Widget menuMoreBuilder(BuildContext context) {
  //   return GestureDetector(
  //     child: Container(
  //       width: MenuWidget.menuSide,
  //       child: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         children: [
  //           Container(
  //             padding: EdgeInsets.fromLTRB(6, 6, 6, 6),
  //             child: Image(
  //                 image: AssetImage('assets/images/home/<USER>'),
  //                 fit: BoxFit.contain),
  //           ),
  //           Container(
  //             padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
  //             child: Text(
  //               '更多',
  //               textAlign: TextAlign.center,
  //               style: TextStyle(fontSize: 14, color: Colors.black87),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //     onTap: () {
  //       widget.pageState.changePage(1);
  //       // if (AC.MessageEnable) {
  //       //   widget.pageState.changePage(2);
  //       // } else {
  //       //   widget.pageState.changePage(1);
  //       // }
  //     },
  //   );
  // }

  Widget todoTasksBuilder(BuildContext context) {
    dynamic taskData;
    final PageData? page = PageData.reg(context);
    if (page != null) {
      taskData = page.getData('todoTasks', null);
    }
    int totalCount = DU.safeValue<int>(taskData, ['totalCount'], 0);
    List tasksData = DU.safeList(taskData, ['list']);
    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // todoTaskTitleBuilder(context, totalCount),
          todoTaskListBuilder(context, tasksData),
        ],
      ),
    );
  }

  // Widget todoTaskTitleBuilder(BuildContext context, int totalCount) {
  //   return GestureDetector(
  //     child: Container(
  //       padding: EdgeInsets.fromLTRB(8, 8, 8, 8),
  //       color: Colors.transparent,
  //       child: Row(
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         children: [
  //           GestureDetector(
  //             child: Row(
  //               children: [
  //                 Image.asset('assets/images/home/<USER>',
  //                     height: 18, width: 18),
  //                 SizedBox(width: 8),
  //                 Text('待办任务',
  //                     style: TextStyle(
  //                         color: Colors.black87,
  //                         fontSize: 16,
  //                         fontWeight: FontWeight.w500)),
  //               ],
  //             ),
  //             onTap: () {
  //               // todoTaskListRefresh();
  //               // animationC.reset();
  //             },
  //           ),
  //           Spacer(),
  //           totalCount > 0
  //               ? Container(
  //                   padding: EdgeInsets.fromLTRB(8, 0, 8, 0),
  //                   decoration: BoxDecoration(
  //                     borderRadius: BorderRadius.all(Radius.circular(16)),
  //                     color: Color(0xffFF5B5A),
  //                   ),
  //                   child: Text(
  //                     totalCount.toString(),
  //                     style: TextStyle(
  //                       fontSize: 14,
  //                       color: Colors.white,
  //                       fontWeight: FontWeight.w400,
  //                     ),
  //                   ),
  //                 )
  //               : Container(),
  //           Icon(
  //             CupertinoIcons.forward,
  //             size: 24,
  //             color: Colors.grey,
  //           ),
  //         ],
  //       ),
  //     ),
  //     onTap: () {
  //       // WU.nextPage(context, TodoTasksPage());
  //     },
  //   );
  // }

  // void todoTaskListRefresh() {
  //   getTaskData();
  // }

  Widget todoTaskListBuilder(BuildContext context, List tasksData) {
    return Column(
      // mainAxisSize: MainAxisSize.min,
      // children: tasksData.map<Widget>((data) {
      //   return BusinessItemFactory().build(context, data, todoTaskListRefresh);
      // }).toList(),
    );
  }

  @override
  // void didPopNext() {
  //   getTaskData();
  //   super.didPopNext();
  // }

  @override
  void didChangeDependencies() {
    MyApp.routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    MyApp.routeObserver.unsubscribe(this);
    super.dispose();
  }
}
