// import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
// import 'package:flutter/material.dart';
//
// import '../../../dnc/utils/DataUtils.dart';
// import '../../../dnc/widget/base/BasePage.dart';
// import '../../../viewmodel/home/<USER>';
// import 'TodoTasksPage.dart';
//
// class MessagePage extends StatefulWidget {
//   final String title;
//
//   MessagePage({required this.title});
//
//   @override
//   State<StatefulWidget> createState() => MessagePageState();
// }
//
// class MessagePageState extends BasePageState<MessagePage> with RouteAware {
//   MessageListViewModel listModel = MessageListViewModel();
//
//   @override
//   String strTitle() => widget.title;
//
//   @override
//   Widget body(BuildContext context) {
//     return Container(
//       child: CustomItemListWidget(
//         // reverse: true,
//         enablePullUp: true,
//         enablePullDown: false,
//         itemBuilder: (BuildContext context, data, int index, bool isEnd) {
//           return timeListBuilder(context, data);
//         },
//         model: MessageListViewModel(),
//       ),
//     );
//   }
//
//   Widget timeListBuilder(BuildContext context, dynamic data) {
//     return Container(
//       padding: EdgeInsets.only(top: 16),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         crossAxisAlignment: CrossAxisAlignment.center,
//         children: [
//           Container(
//             padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.all(Radius.circular(4)),
//               color: Colors.white,
//             ),
//             child: Text(
//               DU.formatDate(data, ['time'], format: 'MM月dd日 HH:mm'),
//               style: TextStyle(
//                 fontSize: 14,
//                 color: Color(0xff999999),
//                 fontWeight: FontWeight.w400,
//               ),
//             ),
//           ),
//           ...DU
//               .safeList(data, ['dataList'])
//               .reversed
//               .map<Widget>((item) {
//                 return messageItemBuilder(context, item);
//               })
//               .toList(),
//         ],
//       ),
//     );
//   }
//
//   void messageRefresh() {}
//
//   Widget messageItemBuilder(BuildContext context, dynamic item) {
//     // print('messageItemBuilder: $item');
//     final business = DU.safeString(item, ['businessType']).split('-');
//     if (DU.isListEmpty(business)) {
//       return SizedBox();
//     }
//     String imageName = '';
//     Widget itemWidget = TodoTasksPageState.taskItemBuilder(context, item, messageRefresh);
//     switch (business[0]) {
//       case '1': //计划任务
//         imageName = 'assets/images/module/schedule_task/page_2.png';
//         break;
//       case '2': //隐患排查
//         imageName = 'assets/images/module/take_picture/page_2.png';
//         break;
//       case '3': //安全检查
//         imageName = 'assets/images/module/safety_checks/page_1.png';
//         break;
//       case '4': //日常巡检
//         imageName = 'assets/images/module/xj_task_simple/page_1.png';
//         break;
//       case '5': //设备管理
//         imageName = 'assets/images/module/device/page_3.png';
//         break;
//       case '6': //危险作业
//         imageName = 'assets/images/module/work_permit/page_0.png';
//         break;
//       case '7': //学习与培训
//         imageName = 'assets/images/module/online_examination/page_1.png';
//         break;
//       case '9': //应急响应
//         imageName = 'assets/images/module/emergency/page_1_0.png';
//         break;
//       case '11': //企业证书
//         imageName = 'assets/images/module/other/other_certificate.png';
//         break;
//       case '12': //资格证
//         imageName = 'assets/images/module/other/other_qualification.png';
//         break;
//     }
//     return Container(
//       margin: EdgeInsets.fromLTRB(12, 8, 0, 8),
//       child: Row(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Container(
//             margin: EdgeInsets.only(top: 10),
//             child: Image.asset(
//               imageName,
//               width: 48,
//               height: 48,
//             ),
//           ),
//           Expanded(
//             child: itemWidget,
//           ),
//         ],
//       ),
//     );
//   }
// }
