import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:ent_secutity_app/viewmodel/home/<USER>';
import 'package:flutter/material.dart';

import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/widget/components/Widget.dart';
import 'HomePage.dart';
import 'business_type/BusinessItemFactory.dart';

class MessageTab with HomePageTabMixin {
  @override
  AppBar appbar(BuildContext context) {
    return AppBar(
      leading: Container(),
      centerTitle: true,
      backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
      title: Text('消息'),
      actions: null,
    );
  }

  @override
  Widget content(BuildContext context) {
    return MessageTabWidget();
  }

  @override
  TabItem bottom(BuildContext context) {
    return TabItem(
      icon: Image.asset(
        'assets/images/home/<USER>',
        // scale: 2,
      ),
      activeIcon: Image.asset(
        'assets/images/home/<USER>',
        // scale: 2,
      ),
      title: "消息",
    );
  }
}

class MessageTabWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return MessageTabWidgetState();
  }
}

class MessageTabWidgetState extends BaseStatefulWidgetState<MessageTabWidget> with RouteAware {
  MessageListViewModel listModel = MessageListViewModel();

  @override
  Widget body(BuildContext context) {
    return Container(
      child: CustomItemListWidget(
        // reverse: true,
        enablePullUp: true,
        enablePullDown: false,
        itemBuilder: (BuildContext context, data, int index, bool isEnd) {
          return timeListBuilder(context, data);
        },
        model: listModel,
      ),
    );
  }
  // listModel.refresh()
  Widget timeListBuilder(BuildContext context, dynamic data) {
    // print("----shuj-消息-:${data}");
    return Container(
      padding: EdgeInsets.only(top: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
        //   Container(
        //     padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
        //     decoration: BoxDecoration(
        //       borderRadius: BorderRadius.all(Radius.circular(4)),
        //       color: Colors.white,
        //     ),
        //     child: Text(
        //       DU.formatDate(data, ['receivingTime'], format: 'MM月dd日 HH:mm'),
        //       style: TextStyle(
        //         fontSize: 14,
        //         color: Color(0xff999999),
        //         fontWeight: FontWeight.w400,
        //       ),
        //     ),
        //   ),
        //   ...DU
        //       .safeList(data, ['dataList'])
        //       .reversed
        //       .map<Widget>((item) {
        //         return messageItemBuilder(context, item);
        //       })
        //       .toList(),
          messageItemBuilder(context, data),
        ],
      ),
    );
  }

  void messageRefresh() {}

  Widget messageItemBuilder(BuildContext context, dynamic item) {
    // print('messageItemBuilder: $item');
    // String imageName = BusinessItemFactory().typeImage(item);
    String imageName = "assets/images/module/other/other_remind.png";
    Widget itemWidget = BusinessItemFactory().build(context, item, messageRefresh,listModel);
    print("----安全--消息--123-:${item}");
    return Container(
      margin: EdgeInsets.fromLTRB(12, 8, 0, 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 10),
            child: Image.asset(
              imageName,
              width: 48,
              height: 48,
            ),
          ),
          Expanded(
              child: itemWidget,
            ),
        ],
      ),
    );
  }
}
