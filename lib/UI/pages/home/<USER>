import 'dart:io';

import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

// import '../../../common/http.dart';
import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/components/Pop.dart';
import '../../../viewmodel/APPModel.dart';
// import '../center/psd_edit.dart';
import '../center/RewUserEditPage.dart';
// import '../center/user_edit.dart';
import '../center/RewPasswordPage.dart';
import '../login/UpgradeDialog.dart';
import 'HomePage.dart';

class MineTab with HomePageTabMixin {
  @override
  PreferredSizeWidget appbar(BuildContext context) {
    return PreferredSize(
      preferredSize: const Size(0, 0),
      child: Container(
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  @override
  Widget content(BuildContext context) {
    return MineTabWidget();
  }

  @override
  TabItem bottom(BuildContext context) {
    return TabItem(
      icon: Image.asset(
        'assets/images/home/<USER>',
        // scale: 2,
      ),
      activeIcon: Image.asset(
        'assets/images/home/<USER>',
        // scale: 2,
      ),
      title: "我的",
    );
  }
}

class MineTabWidget extends StatefulWidget {
  static const String dataTag = 'classicStyle';

  @override
  State<StatefulWidget> createState() {
    return MineTabWidgetState();
  }
}

class MineTabWidgetState extends BaseStatefulWidgetState {
  bool classicStyle = false;
  bool waterMark = false;

  @override
  void initState() {
    loadData();
    super.initState();
  }

  loadData() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      classicStyle = prefs.getBool(MineTabWidget.dataTag) ?? false;
      waterMark = prefs.getBool('waterMark') ?? true;
    });
  }

  @override
  Widget body(BuildContext context) {
    return SafeArea(
      child: Container(
        alignment: Alignment.topLeft,
        decoration: BoxDecoration(
          image: DecorationImage(
            alignment: Alignment.topLeft,
            image: AssetImage('assets/images/home/<USER>'),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            titleBuilder(context),
            userBuilder(context),
            settingBuilder(context),
            SizedBox(height: 8),
            logoutBuilder(context),
          ],
        ),
      ),
    );
  }

  Widget titleBuilder(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: true,
      titleSpacing: 0,
      centerTitle: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      //消除阴影
      title: Text(
        '我的',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget userBuilder(BuildContext context) {
    final avatar = Global.userInfo['avatar'];
    final defaultAvatar = Image.asset('assets/images/home/<USER>', height: 48, width: 48);
    // final tag = DU.safeString(Global.userInfo, ['entInfo', 'name']);
    return Flexible(
      fit: FlexFit.loose,
      child: Container(
        padding: EdgeInsets.fromLTRB(24, 8, 16, 16),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: avatar != null
                  ? Image.network(avatar, width: 48, height: 48, errorBuilder: (ctx, err, stackTrace) => defaultAvatar)
                  : defaultAvatar,
            ),
            Flexible(
              fit: FlexFit.loose,
              child: Container(
                padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      alignment: WrapAlignment.start,
                      spacing: 4,
                      crossAxisAlignment: WrapCrossAlignment.end,
                      children: [
                        Text(
                          Global.name,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    Wrap(
                      alignment: WrapAlignment.start,
                      spacing: 4,
                      crossAxisAlignment: WrapCrossAlignment.end,
                      children: [
                        Text(
                          Global.userName,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget settingBuilder(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(16, 12, 12, 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // settingItemBuilder(context, 'assets/images/home/<USER>', '修改个人信息', () {
          //   WU.nextPage(context, RewUserEditPage());
          // }),
          Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
          settingItemBuilder(context, 'assets/images/home/<USER>', '修改密码', () {
            WU.nextPage(context, RewPasswordPage());
          }),
          Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
          versionBuilder(context),
          Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
          // waterMarkBuilder(context),
          Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
          // homeStyleBuilder(context),
          // Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
        ],
      ),
    );
  }

  Widget settingItemBuilder(BuildContext context, String asset, String name, Function() onTap) {
    return GestureDetector(
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.fromLTRB(0, 12, 0, 12),
        child: Row(
          children: [
            Image.asset(asset, width: 20, height: 20),
            SizedBox(width: 12),
            Text(
              name,
              style: TextStyle(
                fontSize: 15,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
            Spacer(), // use Space
            Icon(
              CupertinoIcons.forward,
              size: 24,
              color: Colors.grey,
            ),
          ],
        ),
      ),
      onTap: onTap,
    );
  }

  Widget versionBuilder(BuildContext context) {
    return GestureDetector(
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.fromLTRB(0, 12, 0, 12),
        child: Row(
          children: [
            Image.asset('assets/images/home/<USER>', width: 20, height: 20),
            SizedBox(width: 12),
            Text(
              '版本检查',
              style: TextStyle(
                fontSize: 15,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
            Spacer(),
            Text(
              DU.showVersionValue(Global.appVersion, Global.appVersionCode),
              style: TextStyle(
                fontSize: 15,
                color: Colors.black87,
                fontWeight: FontWeight.w400,
              ),
            ),
            Icon(
              CupertinoIcons.forward,
              size: 24,
              color: Colors.grey,
            ),
          ],
        ),
      ),
      onTap: compareVersion,
    );
  }

  // Widget waterMarkBuilder(BuildContext context) {
  //   return Container(
  //     padding: EdgeInsets.fromLTRB(0, 12, 0, 12),
  //     child: Row(
  //       children: [
  //         Image.asset('assets/images/home/<USER>', width: 20, height: 20),
  //         // Icon(
  //         //   CupertinoIcons.camera_on_rectangle,
  //         //   size: 20,
  //         //   color: Color(0xff3e8afa),
  //         // ),
  //         SizedBox(width: 12),
  //         Text(
  //           '拍照水印标签',
  //           style: TextStyle(
  //             fontSize: 15,
  //             color: Colors.black87,
  //             fontWeight: FontWeight.w500,
  //           ),
  //         ),
  //         Spacer(),
  //         Transform.scale(
  //           scale: 0.8,
  //           child: CupertinoSwitch(
  //             value: waterMark,
  //             activeColor: Theme.of(context).primaryColor,
  //             // activeColor: Colors.yellow, // 选中时 轨道颜色
  //             // thumbColor: Colors.orange, // 滑块颜色
  //             // trackColor: Colors.cyan[200], // 未选中时 轨道颜色
  //             onChanged: (value) {
  //               setState(() {
  //                 waterMark = value;
  //               });
  //               if (waterMark) {
  //                 SDialog.showInfo(context, info: '拍照后会自动插入相关信息');
  //               }
  //             },
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget homeStyleBuilder(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(0, 12, 0, 12),
      child: Row(
        children: [
          Image.asset('assets/images/home/<USER>', width: 20, height: 20),
          SizedBox(width: 12),
          Text(
            '使用经典首页',
            style: TextStyle(
              fontSize: 15,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
          Spacer(),
          Transform.scale(
            scale: 0.8,
            child: CupertinoSwitch(
              value: classicStyle,
              activeColor: Theme.of(context).primaryColor,
              // activeColor: Colors.yellow, // 选中时 轨道颜色
              // thumbColor: Colors.orange, // 滑块颜色
              // trackColor: Colors.cyan[200], // 未选中时 轨道颜色
              onChanged: (value) {
                setState(() {
                  classicStyle = value;
                });
                SDialog.showInfo(context, info: '重新登录后生效!');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget logoutBuilder(BuildContext context) {
    return GestureDetector(
      child: Container(
        alignment: Alignment.center,
        width: double.infinity,
        padding: EdgeInsets.fromLTRB(16, 12, 16, 12),
        color: Colors.white,
        child: Text(
          '退 出 登 录',
          style: TextStyle(
            fontSize: 15,
            color: Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      onTap: () {
        Global.reset();
        Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
      },
    );
  }

  compareVersion() {
    SDialog.showInfo(context, info: '您已经是最新版本!');
    // APPModel().getAppVersion(subSuccess: (json) {
    //   final newVersion = DU.safeString(json, ["versionName"]);
    //   if (DU.compareVersion(newVersion, Global.appVersion) && Platform.isAndroid) {
    //     showDialog(
    //         context: context,
    //         barrierDismissible: false,
    //         builder: (context) {
    //           return UpgradeDialog(description: json["updateInfo"], apkUrl: json["apkPath"]);
    //         });
    //   } else {
    //     SDialog.showInfo(context, info: '您已经是最新版本!');
    //   }
    // });
  }

  saveData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(MineTabWidget.dataTag, classicStyle);
    await prefs.setBool('waterMark', waterMark);
  }

  @override
  void dispose() {
    saveData();
    super.dispose();
  }
}
