import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:flutter/material.dart';

import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/widget/components/Pop.dart';

class MenuWidget extends StatefulWidget {
  static const double menuSide = 66;
  final dynamic menuData;

  const MenuWidget(this.menuData, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => MenuWidgetState();
}

class MenuWidgetState extends BaseStatefulWidgetState<MenuWidget> {
  @override
  void initState() {
    checkState();
    super.initState();
  }

  @override
  Widget body(BuildContext context) {
    AssetImage asset = AssetImage(
        widget.menuData["stateAsset"] != null ? widget.menuData["stateAsset"] ! : DU.safeString(widget.menuData, ['icon']));
    Image img = Image(
      image: asset,
      fit: BoxFit.contain,
      errorBuilder: (BuildContext context, Object error, StackTrace? stackTrace) {
        return Container(
          alignment: Alignment.center,
          width: 54,
          height: 54,
          decoration: BoxDecoration(
            color: Colors.black12,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Text(
            "已废弃",
            style: TextStyle(
              color: Colors.blueGrey,
              fontSize: 12,
            ),
          ),
        );
      },
    );
    return GestureDetector(
      child: Container(
        width: MenuWidget.menuSide,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.fromLTRB(6, 6, 6, 6),
              child: img,
            ),
            Container(
              padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
              child: Text(
                DU.safeString(widget.menuData, ['name']),
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14, color: Colors.black87),
              ),
            ),
          ],
        ),
      ),
      onTap: () async {
        // 获取用户角色
        final List userRoles = Global.role;
        // 获取菜单项的类型
        final String menuType = widget.menuData['type'];
        // if (DU.safeString(widget.menuData, ['menuType']) == 'pop') {
        //   _showChildMenus(context, DU.safeList(widget.menuData, ['children']));
        // } else {
        //   await Navigator.pushNamed(context, widget.menuData['url'],
        //       arguments: widget.menuData['arguments'] != null
        //           ? jsonDecode(widget.menuData['arguments'])
        //           : {});
        // }
        // 检查用户是否有权限访问该页面
        if (userRoles.contains(menuType) || userRoles.contains('administrator')) {
          // 如果有权限，导航到页面
            await Navigator.pushNamed(context, widget.menuData['url'],
                arguments: widget.menuData['arguments'] != null
                    ? jsonDecode(widget.menuData['arguments'])
                    : {});
        } else {
          // 如果没有权限，给出提示
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text('权限不足'),
                content: Text('您没有权限访问此页面。'),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: Text('确定'),
                  ),
                ],
              );
            },
          );
        }

      },
    );
  }

  void checkState() async {
    final stateIndex = DU.safeValue<int>(widget.menuData, ['stateIndex'], 0);
    if (widget.menuData["stateAsset"] != null || stateIndex < 1) {
      return;
    }
    switch (stateIndex) {
      case 1:
        widget.menuData["stateAsset"] = await getAsset1();
        break;
    }
    setState(() {});
  }

  Future<String> getAsset1() async {
    final isWarn = await Request(
      RequestType.GET,
      '/qb/report/yj_warn_handle_num_report',
      params: () => {'userName': Global.userName, 'entId': Global.entId},
      interceptor: (result) {
        if (result is DioException) {
          return false;
        } else {
          final data = result.first;
          final warnNum = DU.safeValue<int>(data, ['warnNum'], 0);
          final handleNum = DU.safeValue<int>(data, ['handleNum'], 0);
          return warnNum > 0 && Global.YJMaster || handleNum > 0;
        }
      },
    ).execute();
    return isWarn
        ? 'assets/images/module/emergency/page_1_1.gif'
        : 'assets/images/module/emergency/page_1_0.png';
  }

  _showChildMenus(BuildContext context, List menus) {
    BottomSheetPop.show(context, ChildMenus(menus), title: '应急管理');
  }
}

//===============================================================
class ChildMenus extends StatelessWidget {
  final List menus;

  const ChildMenus(this.menus, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(bottom: 28),
        child: Column(
          children: DU.transformList<Widget, dynamic>(menus, (menu) {
            return itemBuilder(context, menu);
          }),
        ),
      ),
    );
  }

  Widget itemBuilder(BuildContext context, dynamic item) {
    return GestureDetector(
        child: Container(
          color: Colors.transparent,
          padding: EdgeInsets.fromLTRB(28, 28, 28, 0),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.only(right: 16),
                child: Image(
                    width: 30,
                    height: 30,
                    image: AssetImage(
                      DU.safeString(item, ['icon']),
                    ),
                    fit: BoxFit.contain),
              ),
              Text(
                DU.safeString(item, ['name']),
                style: TextStyle(fontSize: 16, color: Color(0x8a000000)),
              ),
            ],
          ),
        ),
        onTap: () {
          Navigator.of(context).pop();
          Navigator.pushNamed(context, item['url'],
              arguments: item['arguments'] != null ? jsonDecode(item['arguments']) : {});
        });
  }
}
