// import 'package:convex_bottom_bar/convex_bottom_bar.dart';
// import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
// import 'package:ent_secutity_app/viewmodel/home/<USER>';
// import 'package:flutter/material.dart';
//
// import '../../../dnc/utils/DataUtils.dart';
// import '../../../dnc/utils/WidgetUtils.dart';
// import '../../../main.dart';
// import 'HomePage.dart';
// import 'MessagePage.dart';
//
// class MessageTab with HomePageTabMixin {
//   @override
//   AppBar appbar(BuildContext context) {
//     return AppBar(
//       leading: Container(),
//       centerTitle: true,
//       backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
//       title: Text('消息'),
//       actions: null,
//     );
//   }
//
//   @override
//   Widget content(BuildContext context) {
//     return MessageTabWidget();
//   }
//
//   @override
//   TabItem bottom(BuildContext context) {
//     return TabItem(
//       icon: Image.asset(
//         'assets/images/home/<USER>',
//         // scale: 2,
//       ),
//       activeIcon: Image.asset(
//         'assets/images/home/<USER>',
//         // scale: 2,
//       ),
//       title: "消息",
//     );
//   }
// }
//
// class MessageTabWidget extends StatefulWidget {
//   @override
//   State<StatefulWidget> createState() {
//     return MessageTabWidgetState();
//   }
// }
//
// class MessageTabWidgetState extends BaseStatefulWidgetState with RouteAware {
//   List typeInfo = [];
//
//   @override
//   void initState() {
//     initData();
//     super.initState();
//   }
//
//   initData() async {
//     final List result = await MessageListViewModel().typeInfo();
//     setState(() {
//       typeInfo = result;
//     });
//   }
//
//   @override
//   Widget body(BuildContext context) {
//     return typeInfo.isEmpty
//         ? WU.emptyWidget(context)
//         : SingleChildScrollView(
//             child: Container(
//               padding: EdgeInsets.all(8),
//               color: Colors.white,
//               child: Column(
//                 children: typeInfo.map<Widget>((data) {
//                   return messageTypeItemBuilder(context, data);
//                 }).toList(),
//               ),
//             ),
//           );
//   }
//
//   Widget messageTypeItemBuilder(BuildContext context, dynamic data) {
//     // print('messageTypeItemBuilder: $data');
//     final String title = DU.safeString(data, ['type']);
//     String imageName = '';
//     switch (title) {
//       case '通知提醒':
//         imageName = 'assets/images/home/<USER>';
//         break;
//       case '群组消息':
//         imageName = 'assets/images/home/<USER>';
//         break;
//       case '系统通知':
//         imageName = 'assets/images/home/<USER>';
//         break;
//     }
//     final count = DU.safeValue<int>(data, ['count'], 0);
//     if (count == 0) {
//       data['content'] = '暂时没有新的$title';
//     } else {
//       data['content'] = '您有${count > 1 ? '多' : '1'}条$title,请及时处理';
//     }
//     return GestureDetector(
//       child: Container(
//           padding: EdgeInsets.fromLTRB(8, 8, 8, 8),
//           color: Colors.transparent,
//           child: Column(
//             children: [
//               Row(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Image.asset(
//                     imageName,
//                     width: 48,
//                     height: 48,
//                   ),
//                   Expanded(
//                     child: Column(
//                       mainAxisSize: MainAxisSize.min,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Container(
//                           padding: EdgeInsets.fromLTRB(8, 0, 4, 4),
//                           child: Text(
//                             title,
//                             style: TextStyle(fontSize: 16, color: Colors.black87, fontWeight: FontWeight.w500),
//                           ),
//                         ),
//                         Container(
//                           padding: EdgeInsets.fromLTRB(8, 0, 4, 0),
//                           child: Text(
//                             DU.safeString(data, ['content']),
//                             style: TextStyle(
//                               fontSize: 13,
//                               color: Color(0xff999999),
//                               fontWeight: FontWeight.w400,
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                   Column(
//                     crossAxisAlignment: CrossAxisAlignment.end,
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Container(
//                         padding: EdgeInsets.fromLTRB(4, 4, 0, 4),
//                         child: Text(
//                           DU.formatSmartDate(data, ['time']),
//                           style: TextStyle(
//                             fontSize: 14,
//                             color: Color(0xff999999),
//                             fontWeight: FontWeight.w400,
//                           ),
//                         ),
//                       ),
//                       count > 0
//                           ? Container(
//                               padding: EdgeInsets.fromLTRB(8, 0, 8, 0),
//                               decoration: BoxDecoration(
//                                 borderRadius: BorderRadius.all(Radius.circular(16)),
//                                 color: Color(0xffFF5B5A),
//                               ),
//                               child: Text(
//                                 DU.safeString(data, ['count']),
//                                 style: TextStyle(
//                                   fontSize: 14,
//                                   color: Colors.white,
//                                   fontWeight: FontWeight.w400,
//                                 ),
//                               ),
//                             )
//                           : SizedBox(),
//                     ],
//                   ),
//                 ],
//               ),
//               SizedBox(height: 8),
//               Divider(
//                 color: Theme.of(context).dividerColor,
//                 height: 1,
//                 thickness: 1,
//                 indent: 52,
//               ),
//             ],
//           )),
//       onTap: () {
//         WU.nextPage(context, MessagePage(title: title));
//       },
//     );
//   }
//
//   @override
//   void didPopNext() {
//     initData();
//     super.didPopNext();
//   }
//
//   @override
//   void didChangeDependencies() {
//     MyApp.routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
//     super.didChangeDependencies();
//   }
//
//   @override
//   void dispose() {
//     MyApp.routeObserver.unsubscribe(this);
//     super.dispose();
//   }
// }
