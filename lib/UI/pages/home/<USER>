import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:ent_secutity_app/UI/pages/home/<USER>';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:flutter/material.dart';
import 'package:reorderables/reorderables.dart';

import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/widget/base/BasePage.dart';
import '../../../viewmodel/home/<USER>';
import 'HomePage.dart';

class MenusTab with HomePageTabMixin {
  final HomePageState pageState;
  bool isEditing = false;

  MenusTab(this.pageState);

  @override
  AppBar appbar(BuildContext context) {
    return AppBar(
      leading: null,
      automaticallyImplyLeading: false,
      centerTitle: true,
      backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
      title: Text(
        '工作台',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18,
          fontWeight: FontWeight.w500,
        ),
      ),
      actions: [
        InfoButton(
          name: isEditing ? '保存' : '管理',
          onPressed: () async {
            if (isEditing) {
              final PageData? page = PageData.of(context);
              if (page == null) {
                return;
              }
              final menus = await HomeViewModel().updateMineMenus(page.getData('mineMenus', []));
              page.setData(menus, 'mineMenus');
            }

            isEditing = !isEditing;
            pageState.tabRefresh();
          },
        )
      ],
    );
  }

  @override
  Widget content(BuildContext context) {
    return MenusTabWidget(isEditing);
  }

  @override
  TabItem bottom(BuildContext context) {
    return TabItem(
      icon: Image.asset(
        'assets/images/home/<USER>',
        // scale: 2,
      ),
      activeIcon: Image.asset(
        'assets/images/home/<USER>',
        // scale: 2,
      ),
      title: "工作",
    );
  }
}

class MenusTabWidget extends StatefulWidget {
  final bool isEditing;

  MenusTabWidget(this.isEditing);

  @override
  State<StatefulWidget> createState() {
    return MenusTabWidgetState();
  }
}

class MenusTabWidgetState extends BaseStatefulWidgetState<MenusTabWidget> {
  @override
  Widget body(BuildContext context) {
    List mineMenus = [];
    final PageData? page = PageData.reg(context);
    if (page != null) {
      mineMenus = page.getData('mineMenus', []);
    }
    final menus = Global.menus;
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          widget.isEditing
              ? Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.fromLTRB(0, 8, 0, 0),
                  child: Text(
                    '拖动可调整应用位置',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                )
              : Container(
                  padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                ),
          mineBuilder(context, mineMenus),
          ...menus.map<Widget>((menuData) {
            return menuLevelBuilder(context, mineMenus, menuData);
          }).toList(),
          SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget mineBuilder(BuildContext context, dynamic mineMenus) {
    return SimpleCard(
      isSingle: false,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Text(
              '首页应用',
              style: TextStyle(fontSize: 18.0, color: Colors.black87, fontWeight: FontWeight.w500),
            ),
          ),
          mineWarpBuilder(context, mineMenus),
        ],
      ),
    );
  }

  Widget menuLevelBuilder(BuildContext context, List mineMenus, dynamic item) {
    return SimpleCard(
      isSingle: false,
      body: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              DU.safeString(item, ['name']),
              style: TextStyle(fontSize: 18.0, color: Colors.black87, fontWeight: FontWeight.w500),
            ),
          ),
          menusBuilder(context, mineMenus, DU.safeValue(item, ['children'], [])),
        ],
      ),
    );
  }

  Widget mineWarpBuilder(BuildContext context, dynamic childMenus) {
    List<Widget> menus = childMenus.map<Widget>((data) {
      return Stack(
        alignment: Alignment.topRight,
        children: [
          MenuWidget(data),
          widget.isEditing
              ? IConButton(
                  Icon(Icons.remove_circle, color: Colors.grey),
                  size: Size(16, 16),
                  bgColor: Colors.white,
                  onPressed: () {
                    setState(() {
                      childMenus.remove(data);
                    });
                  },
                )
              : SizedBox(),
        ],
      );
    }).toList();
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
      final side = (constraints.maxWidth - MenuWidget.menuSide * 4) / 4;
      return Container(
        padding: EdgeInsets.fromLTRB(side / 2, 8, side / 2, 16),
        width: double.infinity,
        child: ReorderableWrap(
          direction: Axis.horizontal,
          alignment: WrapAlignment.start,
          spacing: side,
          runSpacing: 8,
          children: menus,
          needsLongPressDraggable: false,
          enableReorder: widget.isEditing,
          onReorder: (int oldIndex, int newIndex) {
            setState(() {
              final row = childMenus.removeAt(oldIndex);
              childMenus.insert(newIndex, row);
            });
          },
        ),
      );
    });
  }

  Widget menusBuilder(BuildContext context, List mineMenus, List childMenus) {
    List<Widget> menus = childMenus.map<Widget>((data) {
      bool showEdit = true;
      for (var item in mineMenus) {
        if (item['name'] == data['name']) {
          showEdit = false;
          break;
        }
      }
      return Stack(
        alignment: Alignment.topRight,
        children: [
          MenuWidget(data),
          widget.isEditing && showEdit
              ? IConButton(
                  Icon(Icons.add_circle, color: Colors.blue),
                  size: Size(16, 16),
                  bgColor: Colors.white,
                  onPressed: () {
                    setState(() {
                      mineMenus.add(data);
                    });
                  },
                )
              : SizedBox(),
        ],
      );
    }).toList();

    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final side = (constraints.maxWidth - MenuWidget.menuSide * 4) / 4;
        return Container(
          padding: EdgeInsets.fromLTRB(side / 2, 8, side / 2, 16),
          width: double.infinity,
          child: Wrap(
            direction: Axis.horizontal,
            alignment: WrapAlignment.start,
            spacing: side,
            runSpacing: 8,
            children: menus,
          ),
        );
      },
    );
  }
}
