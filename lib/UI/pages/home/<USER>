import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:convex_bottom_bar/src/style/transition_container.dart';
import 'package:dio/dio.dart';
import 'package:ent_secutity_app/UI/pages/home/<USER>';
import 'package:ent_secutity_app/UI/pages/home/<USER>';
import 'package:ent_secutity_app/UI/pages/home/<USER>';
// import 'package:ent_secutity_app/UI/pages/offline/xj_offline/OfflineManager.dart';
// import 'package:ent_secutity_app/UI/pages/xj_task_simple/cache/ExceptionEditCacheManager.dart';
import 'package:ent_secutity_app/common/AppConfig.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/viewmodel/home/<USER>';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../dnc/network/Request.dart';
import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/widget/components/Pop.dart';

import 'MessageTab.dart';

class HomePage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return HomePageState();
  }
}

class HomePageState extends BasePageState with SingleTickerProviderStateMixin {
  int currentIndex = 0;
  late List<HomePageTabMixin> pageTabs;
  late TabController _tabController;
  bool messageTip = true;

  // PageController pageController = PageController(initialPage: 0,keepPage: false);

  @override
  void initState() {
    // ExceptionEditCacheManager().deleteUselessExceptions();
    checkOfflineTask();
    print('---home-消息：${messageTip}');
    pageTabs = [
      HomeTab(this),
      // MenusTab(this),
      if(messageTip) MessageTab(),
      MineTab(),
    ];
    _tabController = TabController(
      initialIndex: 0,
      vsync: this,
      length: pageTabs.length,
    );
    _tabController.addListener(tabListener);
    getHomeData();
    // getMessageTip();
    super.initState();
  }

  Future checkOfflineTask() async {
    // final needPush = await OfflineManager().needPushRcjx();
    // if (needPush) {
    //   SDialog.beSure(context, content: '您有未提交的离线巡检操作,是否立即执行',
    //       onBeSure: () async {
    //     // OfflineManager().pushAll();
    //   });
    // }
  }

  void getHomeData() async {
    // final homeData = await HomeViewModel().getHomeData();
    final homeData = Global.menus;
    print('-安全计划--123-:${homeData}');
    print('-安全计划--7777-:${Global.menus}');
    // setData(homeData['appIndexAdvert'], 'banners');
    setData(homeData['menus'], 'mineMenus');
  }

  tabRefresh() {
    this.setState(() {});
  }

  @override
  Widget getPageWidget(BuildContext c) {
    return WillPopScope(
      child: Builder(
        builder: (BuildContext context) {
          return Scaffold(
            appBar: pageTabs[currentIndex].appbar(context),
            body: TabBarView(
              controller: _tabController,
              children: pageTabs
                  .map<Widget>((HomePageTabMixin tab) => tab.content(context))
                  .toList(),
              // onPageChanged: (index) {
              //   setState(() {
              //     currentIndex = index;
              //   });
              // },
            ),
            bottomNavigationBar: ConvexAppBar.builder(
              itemBuilder: _CustomBuilder(
                pageTabs
                    .map<TabItem>((HomePageTabMixin tab) => tab.bottom(context))
                    .toList(),
                Color(0xff2A80FF),
                messageTip,
              ),
              height: 40,
              top: -16,
              curveSize: 56,
              count: pageTabs.length,
              backgroundColor: Color(0xff2A80FF),
              controller: _tabController,
            ),

            // StyleProvider(
            //   style: Style(),
            //   child: ConvexAppBar(
            //     // backgroundColor: Colors.white,
            //     backgroundColor: Color(0xff2A80FF),
            //     activeColor: Colors.white,
            //     //Color(0xff2A80FF),
            //     controller: _tabController,
            //     style: TabStyle.titled,
            //     items: pageTabs.map<TabItem>((HomePageTabMixin tab) => tab.bottom(context)).toList(),
            //     initialActiveIndex: currentIndex,
            //     // onTap: (index) {
            //     //   changePage(index);
            //     // },
            //   ),
            // ),

            // BottomNavigationBar(
            //   items: pageTabs
            //       .map<BottomNavigationBarItem>(
            //           (HomePageTabMixin tab) => tab.bottom(context))
            //       .toList(),
            //   currentIndex: currentIndex,
            //   type: BottomNavigationBarType.fixed,
            //   onTap: (index) {
            //     changePage(index);
            //   },
            // ),
          );
        },
      ),
      onWillPop: () async {
        final platform = MethodChannel("back/desktop");
        try {
          await platform.invokeMethod("backDesktop");
        } on PlatformException {}
        return false;
      },
    );
  }

  void tabListener() {
    if (_tabController.index != currentIndex) {
      setState(() {
        currentIndex = _tabController.index;
      });
    }
    // if (_tabController.index.toDouble() == _tabController.animation?.value) {
    //   setState(() {
    //     currentIndex = _tabController.index;
    //   });
    // }
  }

  /*切换页面*/
  void changePage(int index) {
    if (index != currentIndex) {
      _tabController.animateTo(index,
          duration: Duration(milliseconds: 200), curve: Curves.ease);
      // pageController.animateToPage(index,
      //     duration: Duration(milliseconds: 200), curve: Curves.linear);
    }
  }
//获取消息
  // Future<void> getMessageTip() async {
  //   final result = await Request(RequestType.GET, '/qb/report/getMessageCount',
  //       params: () => {"userId": Global.userId}).execute();
  //   final messageCount = result is DioException
  //       ? 0
  //       : DU.safeValue<int>(result.first, ['messageCount'], 0);
  //   // print('messageCount $messageCount');
  //   messageTip = messageCount > 0;
  // }

  @override
  Widget body(BuildContext context) {
    return Container();
  }

  @override
  String strTitle() => '';
}

class _CustomBuilder extends DelegateBuilder {
  final List<TabItem> items;
  final Color backgroundColor;
  final bool messageTip;

  _CustomBuilder(this.items, this.backgroundColor, this.messageTip);

  @override
  Widget build(BuildContext context, int index, bool active) {
    final _item = items[index];
    final _icon = active ? _item.activeIcon : _item.icon;
    final _title = _item.title ?? "";
    final _tip = _title == '消息' && messageTip;
    return Center(
      child: active
          ? TransitionContainer.scale(
              data: index,
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(150),
                  gradient: RadialGradient(tileMode: TileMode.mirror, colors: [
                    Colors.white,
                    backgroundColor,
                    // Colors.white,
                  ], stops: [
                    0.8,
                    1,
                    // 0.9
                  ]),
                ),
                child: Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: _icon,
                  ),
                ),
              ),
              curve: Curves.easeInOut)
          : TransitionContainer.slide(
              child: Stack(
                alignment: Alignment.topRight,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                    child: Text(_title,
                        style: TextStyle(fontSize: 12, color: Colors.white)),
                  ),
                  _tip
                      ? Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Color(0xfffe3d3f),
                            borderRadius: BorderRadius.circular(90),
                          ),
                        )
                      : SizedBox(),
                ],
              ),
              // Column(
              //   mainAxisSize: MainAxisSize.max,
              //   mainAxisAlignment: MainAxisAlignment.center,
              //   children: [
              //     // Container(
              //     //     child: Center(
              //     //       child: SizedBox(
              //     //         width: 18,
              //     //         height: 18,
              //     //         child: _icon,
              //     //       ),
              //     //     ),
              //     //     ),
              //     // Text(_title, style: TextStyle(fontSize: 12, color: Colors.white)),
              //
              //
              //   ],
              // ),
              curve: Curves.easeInOut),
    );
  }

  @override
  bool fixed() {
    return false;
  }
}

mixin HomePageTabMixin {
  PreferredSizeWidget appbar(BuildContext context);

  Widget content(BuildContext context);

  TabItem bottom(BuildContext context);
}
