// import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
// import 'package:flame/experimental.dart';
// import 'package:flame/game.dart';
// import 'package:flame/palette.dart';
// import 'package:flutter/cupertino.dart';
// import 'dart:math' as math;
//
// import 'package:flame/components.dart';
//
// import 'package:flutter/material.dart';
//
// class FlamePage extends StatefulWidget {
//   @override
//   State<StatefulWidget> createState() => FlamePageState();
// }
//
// class FlamePageState extends BasePageState<FlamePage> {
//   @override
//   String strTitle() => 'FlamePage';
//
//   @override
//   Widget body(BuildContext context) {
//     return GameWidget(game: MyGame());
//   }
// }
//
// class MyGame extends FlameGame with TapCallbacks {
//   @override
//   Future<void> onLoad() async {
//     add(Square(size / 2));
//   }
//
//   @override
//   void onTapDown(TapDownEvent event) {
//     super.onTapDown(event);
//     if (!event.handled) {
//       final touchPoint = event.canvasPosition;
//       add(Square(touchPoint));
//     }
//   }
// }
//
// class Square extends RectangleComponent with TapCallbacks {
//   static const speed = 8 ;
//   static const squareSize = 128.0;
//   static const indicatorSize = 6.0;
//
//   static final Paint red = BasicPalette.red.paint();
//   static final Paint blue = BasicPalette.blue.paint();
//
//   Square(Vector2 position)
//       : super(
//           position: position,
//           size: Vector2.all(squareSize),
//           anchor: Anchor.center,
//         );
//
//   @override
//   void update(double dt) {
//     super.update(dt);
//     angle += speed * dt;
//     angle %= 2 * math.pi;
//   }
//
//   @override
//   Future<void> onLoad() async {
//     super.onLoad();
//     add(
//       RectangleComponent(
//         size: Vector2.all(indicatorSize),
//         paint: blue,
//       ),
//     );
//     add(
//       RectangleComponent(
//         position: size / 2,
//         size: Vector2.all(indicatorSize),
//         anchor: Anchor.center,
//         paint: red,
//       ),
//     );
//   }
//
//   @override
//   void onTapDown(TapDownEvent event) {
//     removeFromParent();
//     event.handled = true;
//   }
// }
