import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart'; //导入了Material UI组件库

import '../../../dnc/network/Request.dart';
import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/utils/PlusUtils.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/components/Pop.dart';
// import '../dev_management/dev_account/AccountDetailPage.dart';
// import '../device/MainDetailPage.dart';

class ScanQRCode {
  static testPage(BuildContext context) async {
    // final resule = await Navigator.push(context,
    //     MaterialPageRoute(builder: (BuildContext context) {
    //   return TestPage();
    // }));
  }

  static testNFC(BuildContext context) async {
    var info = '';
    late StateSetter st;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter stateSetter) {
              st = stateSetter;
              return Container(
                color: Colors.white,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(info),
                  ],
                ),
              );
            },
          );
        });
    NFC.getNFCInfo().then((tag) async {
      if (tag != null) {
        st(() {
          info = tag;
        });
      }
    });
  }

  /*
  type：
  jypxPlanClass 教育培训
  新双重机制：scyftx_unit,
  设备管理：device，
  老双重预防体系：xj_point,
  日常巡检：rcxj_point
   */
  static scan(BuildContext context) async {
    final result = await ScanQR.scan();
    if (result.isEmpty) {
      return;
    }
    Map? info;

    try {
      info = json.decode(result);
    } on Exception {
      info = null;
    }
    if (info == null) {
      Toast.show('无效的二维码');
      return;
    }

    final type = DU.safeString(info, ['type']);
    switch (type) {
      case 'device':
        // return WU.nextPage(context, DeviceMainDetailPage(deviceId: info['id']));
      case 'dm':
        // return WU.nextPage(context, AccountDetailPage(item: info));
      case 'edu_offline':
        dynamic result = await Request(
          RequestType.POST,
          '/qyaq/edu/train-teacher/train-user-scan',
          post: () => {
            "trainId": DU.safeString(info, ['id']),
          },
        ).execute(showLoading: true);


        if (result is DioException) {
          Toast.show("您不是该培训的学员，无法签到！");
          return;
        }
        Toast.show("签到成功");
        return;


    }
  }
}
