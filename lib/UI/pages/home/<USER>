import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/widget/base/RequestStateProvider.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../common/global.dart';
import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/widget/base/BasePage.dart';

class StatisticsPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => StatisticsPageState();
}

class StatisticsPageState extends BasePageState<StatisticsPage> with RequestStateProvider {
  @override
  String strTitle() => '统计分析';

  // var todayData = {};
  // var weekData = {};
  // var monthData = {};
  // var educationData1 = {};
  // var educationData2 = {};
  // var educationData3 = [];
  // var dualSystemData = [];
  // var dualSystemRankData = [];
  int currentEducationIndex = -1;
  bool showChart = true;

  @override
  bool contentEmpty(contentData) {
    return false;
  }

  @override
  Widget content(BuildContext context, contentData) {
    return CustomScrollView(
      slivers: [
        dayBuilder(context, contentData['todayData']),
        // SizedBox(height: 8),
        weekBuilder(context, contentData['weekData']),
        // SizedBox(height: 8),
        monthBuilder(context, contentData['monthData']),
        // SizedBox(height: 8),
        educationBuild(context, contentData),
        // SizedBox(height: 8),
        dualSystemBuilder(context, contentData),
      ],
    );
  }

  Widget dayBuilder(BuildContext context, dynamic todayData) {
    return infoItem(
      title: '今日数据',
      body: Column(
        children: [
          itemCard1(info: [
            ItemInfo(title: '计划巡检', info: DU.safeString(todayData, ['pointCount'], '0'), unit: '项'),
            ItemInfo(
                title: '已完成',
                info: DU.safeString(todayData, ['wcCount'], '0'),
                unit: '项',
                titleColor: Color(0xff537BC0)),
            ItemInfo(
                title: '未完成',
                info: DU.safeString(todayData, ['wwcCount'], '0'),
                unit: '项',
                titleColor: Color(0xffEB7A7C)),
          ]),
          itemCard2(info: [
            ItemInfo(
                title: '安全隐患',
                info: DU.safeString(todayData, ['yhCount'], '0'),
                unit: '项',
                icon: 'assets/images/home/<USER>/home_statistics_today_1.png'),
            ItemInfo(
                title: '危险作业',
                info: DU.safeString(todayData, ['pgCount'], '0'),
                unit: '项',
                icon: 'assets/images/home/<USER>/home_statistics_today_2.png'),
          ]),
        ],
      ),
    );
  }

  Widget weekBuilder(BuildContext context, dynamic weekData) {
    final count = DU.safeValue<int>(weekData, ['count'], 0);
    final overdueCount = DU.safeValue<int>(weekData, ['overdueCount'], 0);
    final onTimeCount = DU.safeValue<int>(weekData, ['onTimeCount'], 0);
    final ratio = (count != 0 && onTimeCount != 0 ? (onTimeCount * 100.0 / count) : 0).toStringAsFixed(2);
    return infoItem(
      title: '本周数据',
      body: Column(
        children: [
          itemCard1(info: [
            ItemInfo(title: '隐患整改', info: '$count', unit: '条'),
            ItemInfo(
                title: '已整改',
                info: DU.safeString(weekData, ['finishCount'], '0'),
                unit: '条',
                titleColor: Color(0xff537BC0)),
            ItemInfo(
                title: '未整改',
                info: DU.safeString(weekData, ['undoneCount'], '0'),
                unit: '条',
                titleColor: Color(0xffEB7A7C)),
          ]),
          itemCard2(info: [
            ItemInfo(
                title: '逾期未整改',
                info: '$overdueCount',
                unit: '项',
                icon: 'assets/images/home/<USER>/home_statistics_week_1.png'),
            ItemInfo(
                title: '按时整改率',
                info: ratio,
                unit: '%',
                icon: 'assets/images/home/<USER>/home_statistics_week_2.png'),
          ]),
        ],
      ),
    );
  }

  Widget monthBuilder(BuildContext context, dynamic monthData) {
    return infoItem(
      title: '本月数据',
      body: Column(
        children: [
          monthItem(
              icon: 'assets/images/home/<USER>/home_statistics_month_1.png',
              name: '安全检查',
              value: DU.safeString(monthData, ['wcCount'], '0'),
              unit: '次'),
          monthItem(
              icon: 'assets/images/home/<USER>/home_statistics_month_2.png',
              name: '检查问题点',
              value: DU.safeString(monthData, ['wtdCount'], '0'),
              unit: '次'),
          monthItem(
              icon: 'assets/images/home/<USER>/home_statistics_month_3.png',
              name: '累计安全投入',
              value: DU.safeString(monthData, ['none'], '0'),
              unit: '万'),
          monthItem(
              icon: 'assets/images/home/<USER>/home_statistics_month_4.png',
              name: '全年预算占比',
              value: DU.safeString(monthData, ['none'], '0.00'),
              unit: '%'),
        ],
      ),
    );
  }

  Widget educationBuild(BuildContext context, dynamic contentData) {
    final educationData1 = contentData['educationData1'];
    final educationData2 = contentData['educationData2'];
    final educationData3 = contentData['educationData3'];
    // print('educationData1: $educationData1');
    // print('educationData2: $educationData2');
    // print('educationData3: $educationData3');
    final ksrsCount = DU.safeValue<int>(educationData2, ['ksrsCount'], 0);
    final hegeCount = DU.safeValue<int>(educationData2, ['hegeCount'], 0);
    final ratio = (ksrsCount != 0 ? hegeCount * 100.0 / ksrsCount : 0).toStringAsFixed(2);
    return infoItem(
      title: '教育培训',
      body: Column(
        children: [
          itemCard1(info: [
            ItemInfo(title: '本月考试', info: DU.safeString(educationData1, ['ks'], '0'), unit: '次'),
            ItemInfo(
              title: '答题人次',
              info: '$ksrsCount',
              unit: '人',
            ),
            ItemInfo(title: '答题合格率', info: ratio, unit: '%', infoColor: Color(0xff287DF2)),
          ]),
          educationLineChart(educationData3),
          educationAreaChart(educationData3),
        ],
      ),
    );
  }

  Widget dualSystemBuilder(BuildContext context, dynamic contentData) {
    return infoItem(
      title: '双体系得分',
      command: Container(
        child: Row(
          children: [
            dualSystemCommand(
                name: '年度',
                selected: showChart,
                onTap: () {
                  setState(() {
                    showChart = !showChart;
                  });
                }),
            dualSystemCommand(
                name: '公司排名',
                selected: !showChart,
                onTap: () {
                  setState(() {
                    showChart = !showChart;
                  });
                }),
          ],
        ),
      ),
      body: showChart
          ? dualSystemChart(contentData['dualSystemData'])
          : dualSystemRank(contentData['dualSystemRankData']),
    );
  }

  Widget infoItem({required String title, required Widget body, Widget? command}) {
    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.all(16),
        color: Colors.white,
        child: Column(
          children: [
            Container(
              child: Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    color: Color(0xff2880FC),
                  ),
                  SizedBox(width: 12),
                  Text(
                    title,
                    style: TextStyle(fontSize: 16, color: Colors.black87, fontWeight: FontWeight.w600),
                  ),
                  Spacer(),
                  command != null ? command : SizedBox(),
                ],
              ),
            ),
            body,
          ],
        ),
      ),
    );
  }

  Widget itemCard1({required List<ItemInfo> info}) {
    return Container(
      margin: EdgeInsets.only(top: 8),
      height: 64,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4)),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1), //阴影在X轴和Y轴上的偏移
            color: Color(0xffE8E8E8), //阴影颜色
            blurRadius: 1, //阴影程度
            spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
          ),
        ],
      ),
      alignment: Alignment.center,
      child: Row(
        children: [
          Flexible(
            child: card1Item(itemInfo: info[0]),
          ),
          VerticalDivider(
            color: Color(0xffE8E8E8),
            width: 1,
            indent: 8,
            endIndent: 8,
          ),
          Flexible(
            child: card1Item(itemInfo: info[1]),
          ),
          VerticalDivider(
            color: Color(0xffE8E8E8),
            width: 1,
            indent: 8,
            endIndent: 8,
          ),
          Flexible(
            child: card1Item(itemInfo: info[2]),
          ),
        ],
      ),
    );
  }

  Widget card1Item({required ItemInfo itemInfo}) {
    return Container(
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            child: Text(
              itemInfo.title,
              style: TextStyle(fontSize: 14, color: itemInfo.titleColor ?? Colors.black87, fontWeight: FontWeight.w400),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  itemInfo.info,
                  style:
                      TextStyle(fontSize: 16, color: itemInfo.infoColor ?? Colors.black87, fontWeight: FontWeight.w600),
                ),
                Text(
                  itemInfo.unit,
                  style:
                      TextStyle(fontSize: 12, color: itemInfo.infoColor ?? Colors.black87, fontWeight: FontWeight.w400),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget itemCard2({required List<ItemInfo> info}) {
    return Container(
      child: Row(
        children: [
          Flexible(child: card2Item(itemInfo: info[0])),
          SizedBox(width: 8),
          Flexible(child: card2Item(itemInfo: info[1])),
        ],
      ),
    );
  }

  Widget card2Item({required ItemInfo itemInfo}) {
    return Container(
      margin: EdgeInsets.only(top: 8),
      height: 64,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4)),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1), //阴影在X轴和Y轴上的偏移
            color: Color(0xffE8E8E8), //阴影颜色
            blurRadius: 1, //阴影程度
            spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            child: Container(
              padding: EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    child: Row(
                      children: [
                        Image.asset(
                          itemInfo.icon ?? '',
                          width: 18,
                          height: 18,
                        ),
                        SizedBox(width: 8),
                        Text(
                          itemInfo.title,
                          style: TextStyle(
                              fontSize: 14, color: itemInfo.titleColor ?? Colors.black87, fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          itemInfo.info,
                          style: TextStyle(
                              fontSize: 16, color: itemInfo.infoColor ?? Colors.black87, fontWeight: FontWeight.w600),
                        ),
                        Text(
                          itemInfo.unit,
                          style: TextStyle(
                              fontSize: 12, color: itemInfo.infoColor ?? Colors.black87, fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: -16,
            right: -16,
            child: Container(
              width: 64,
              height: 64,
              child: Opacity(
                opacity: 0.1,
                child: Image.asset(
                  itemInfo.icon ?? '',
                  fit: BoxFit.fill,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget monthItem({required String icon, required String name, required String value, required String unit}) {
    return Container(
      margin: EdgeInsets.only(top: 8),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4)),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1), //阴影在X轴和Y轴上的偏移
            color: Color(0xffE8E8E8), //阴影颜色
            blurRadius: 1, //阴影程度
            spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
          ),
        ],
      ),
      child: Row(
        children: [
          Image.asset(
            icon,
            width: 32,
            height: 32,
          ),
          SizedBox(width: 8),
          Text(
            name,
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w400,
            ),
          ),
          Spacer(),
          Text(
            value,
            style: TextStyle(fontSize: 16, color: Colors.black87, fontWeight: FontWeight.w600),
          ),
          Text(
            unit,
            style: TextStyle(fontSize: 12, color: Colors.black87, fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }

  Widget educationLineChart(dynamic educationData3) {
    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(0, 4, 0, 4),
            child: Text(
              '答题人数',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.all(4),
            child: Text(
              '单位:人',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xff4f8bca),
              ),
            ),
          ),
          SizedBox(
            height: 196,
            child: SfCartesianChart(
              primaryXAxis: CategoryAxis(
                isVisible: true,
                opposedPosition: false,
                isInversed: false,
                majorGridLines: MajorGridLines(color: Colors.transparent),
                majorTickLines: MajorTickLines(size: 0),
              ),
              selectionType: SelectionType.point,
              isTransposed: false,
              selectionGesture: ActivationMode.singleTap,
              trackballBehavior: TrackballBehavior(
                lineColor: Color(0xff4f8bca),
                lineType: TrackballLineType.horizontal,
                activationMode: ActivationMode.singleTap,
                enable: true,
                tooltipAlignment: ChartAlignment.near,
                tooltipSettings: InteractiveTooltip(
                  color: Color(0x203B93F5),
                  borderColor: Color(0xff4f8bca),
                  borderRadius: 4,
                  borderWidth: 1,
                  textStyle: TextStyle(
                    fontSize: 12,
                    color: Color(0xff4F8BCA),
                  ),
                ),
                shouldAlwaysShow: true,
                tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
              ),
              series: <ChartSeries<dynamic, String>>[
                AreaSeries<dynamic, String>(
                  isVisibleInLegend: false,
                  dataSource: educationData3,
                  xValueMapper: (data, _) {
                    final monthCount = DU.safeString(data, ['monthCount'], '').split('-');
                    final month = '${monthCount.length == 2 ? monthCount[1] : 00}月';
                    return month;
                  },
                  yValueMapper: (data, _) {
                    // var rng = new Random();
                    // print(rng.nextInt(100));//
                    // return rng.nextInt(100);
                    return DU.safeValue<int>(data, ['count'], 0);
                  },
                  borderWidth: 1,
                  borderColor: Color(0xff3B93F5),
                  markerSettings: MarkerSettings(
                    isVisible: true,
                    height: 8,
                    width: 8,
                    color: Color(0xff3B93F5),
                    shape: DataMarkerType.circle,
                  ),
                  color: Color(0x203B93F5),
                ),
              ],
              onTrackballPositionChanging: (TrackballArgs arg) {
                // print('arg: ${arg.chartPointInfo.dataPointIndex}  $currentEducationIndex');
                final int index = arg.chartPointInfo.dataPointIndex ?? 0;
                if (_educationAreaChartSetter != null && currentEducationIndex != index) {
                  _educationAreaChartSetter!(() {
                    currentEducationIndex = index;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  StateSetter? _educationAreaChartSetter;

  Widget educationAreaChart(dynamic educationData3) {
    return StatefulBuilder(
      builder: (BuildContext context, void Function(void Function()) setState) {
        _educationAreaChartSetter = setState;
        if (currentEducationIndex < 0) {
          return Container();
        }
        final data = educationData3[currentEducationIndex];

        var total = DU.safeValue<int>(data, ['mfCount'], 0) +
            DU.safeValue<int>(data, ['yxCount'], 0) +
            DU.safeValue<int>(data, ['lhCount'], 0) +
            DU.safeValue<int>(data, ['jgCount'], 0) +
            DU.safeValue<int>(data, ['bjgCount'], 0);
        total = total == 0 ? 1 : total;
        final pieData = [
          {
            'name': '满分',
            'value': DU.safeValue<int>(data, ['mfCount'], 0),
            'rate': DU.safeValue<int>(data, ['mfCount'], 0) / total * 1.0,
            'color': Color(0xff71DBB5),
          },
          {
            'name': '优秀',
            'value': DU.safeValue<int>(data, ['yxCount'], 0),
            'rate': DU.safeValue<int>(data, ['yxCount'], 0) / total * 1.0,
            'color': Color(0xff3593EB),
          },
          {
            'name': '良好',
            'value': DU.safeValue<int>(data, ['lhCount'], 0),
            'rate': DU.safeValue<int>(data, ['lhCount'], 0) / total * 1.0,
            'color': Color(0xff3565ED),
          },
          {
            'name': '及格',
            'value': DU.safeValue<int>(data, ['jgCount'], 0),
            'rate': DU.safeValue<int>(data, ['jgCount'], 0) / total * 1.0,
            'color': Color(0xff862FEA),
          },
          {
            'name': '不及格',
            'value': DU.safeValue<int>(data, ['bjgCount'], 0),
            'rate': DU.safeValue<int>(data, ['bjgCount'], 0) / total * 1.0,
            'color': Color(0xffFFC837),
          },
        ];
        return Container(
          padding: EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.fromLTRB(0, 4, 0, 4),
                child: Text(
                  '答题成绩',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              SizedBox(
                height: 196,
                child: SfCircularChart(
                  enableMultiSelection: true,
                  margin: EdgeInsets.zero,
                  // 显示图例，position显示所在位置
                  legend: Legend(
                      isVisible: true,
                      position: LegendPosition.right,
                      alignment: ChartAlignment.far,
                      legendItemBuilder: (String legendText, dynamic series, dynamic point, int seriesIndex) {
                        Color color = Color(0xff71DBB5);
                        switch (legendText) {
                          case '满分':
                            color = Color(0xff71DBB5);
                            break;
                          case '优秀':
                            color = Color(0xff3593EB);
                            break;
                          case '良好':
                            color = Color(0xff3565ED);
                            break;
                          case '及格':
                            color = Color(0xff862FEA);
                            break;
                          case '不及格':
                            color = Color(0xffFFC837);
                            break;
                        }
                        final count = '${pieData[seriesIndex]['value']}人';
                        return Container(
                          padding: EdgeInsets.all(4),
                          width: 104,
                          child: Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: new BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              Text(
                                legendText,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: color,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Spacer(),
                              Text(
                                count,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: color,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                  series: <CircularSeries>[
                    DoughnutSeries<dynamic, String>(
                      explode: true,
                      explodeOffset: '8',
                      dataSource: pieData,
                      pointColorMapper: (data, _) {
                        return data['color'];
                      },
                      xValueMapper: (data, _) {
                        return data['name'];
                      },
                      yValueMapper: (data, _) {
                        return data['value'];
                      },
                      dataLabelMapper: (data, _) {
                        return '${(data['rate'] * 100).toStringAsFixed(1)}%';
                      },
                      dataLabelSettings: DataLabelSettings(isVisible: true, margin: EdgeInsets.zero),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget dualSystemCommand({required String name, required bool selected, required Function() onTap}) {
    return GestureDetector(
      child: Container(
        margin: EdgeInsets.fromLTRB(8, 0, 0, 0),
        height: 24,
        width: 56,
        alignment: Alignment.center,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(4)),
            color: selected ? Color(0xff3B90FD) : Colors.white,
            border: Border.all(color: Color(0xff3B90FD), width: 1.0, style: BorderStyle.solid)),
        child: Text(
          name,
          style: TextStyle(
            fontSize: 12,
            color: selected ? Colors.white : Color(0xff3B90FD),
          ),
        ),
      ),
      onTap: onTap,
    );
  }

  Widget dualSystemChart(dynamic dualSystemData) {
    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(4),
            child: Text(
              '单位:分',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xff4f8bca),
              ),
            ),
          ),
          SizedBox(
            height: 228,
            child: SfCartesianChart(
              primaryXAxis: CategoryAxis(
                isVisible: true,
                opposedPosition: false,
                isInversed: false,
                majorGridLines: MajorGridLines(color: Colors.transparent),
                majorTickLines: MajorTickLines(size: 0),
              ),
              selectionType: SelectionType.point,
              isTransposed: false,
              selectionGesture: ActivationMode.singleTap,
              trackballBehavior: TrackballBehavior(
                lineColor: Color(0xff4f8bca),
                lineType: TrackballLineType.horizontal,
                activationMode: ActivationMode.singleTap,
                enable: true,
                tooltipAlignment: ChartAlignment.near,
                tooltipSettings: InteractiveTooltip(
                  color: Color(0x203B93F5),
                  borderColor: Color(0xff4f8bca),
                  borderRadius: 4,
                  borderWidth: 1,
                  textStyle: TextStyle(
                    fontSize: 12,
                    color: Color(0xff4F8BCA),
                  ),
                ),
                shouldAlwaysShow: true,
                tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
              ),
              series: <ChartSeries<dynamic, String>>[
                AreaSeries<dynamic, String>(
                  isVisibleInLegend: false,
                  dataSource: dualSystemData,
                  xValueMapper: (data, _) {
                    final monthCount = DU.safeString(data, ['month'], '').split('-');
                    final month = '${monthCount.length == 2 ? monthCount[1] : 00}月';
                    return month;
                  },
                  yValueMapper: (data, _) {
                    return DU.safeValue<double>(data, ['score'], 0);
                  },
                  // dataLabelMapper: (data, _) {
                  //   return '${DU.safeValue<double>(data, ['score'], 0)}分';
                  // },
                  // dataLabelSettings: DataLabelSettings(
                  //   isVisible: true,
                  //   textStyle: TextStyle(
                  //     fontSize: 12,
                  //     color:Color(0xff4F8BCA),
                  //     fontWeight: FontWeight.w300
                  //   ),
                  // ),
                  borderWidth: 1,
                  borderColor: Color(0xff3B93F5),
                  markerSettings: MarkerSettings(
                    isVisible: true,
                    height: 8,
                    width: 8,
                    color: Color(0xff3B93F5),
                    shape: DataMarkerType.circle,
                  ),
                  color: Color(0x203B93F5),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget dualSystemRank(dynamic dualSystemRankData) {
    return Container(
      constraints: BoxConstraints(
        minHeight: 288,
      ),
      padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Color(0xff82AFFE),
              borderRadius: BorderRadius.only(topLeft: Radius.circular(4), topRight: Radius.circular(4)),
            ),
            child: Row(
              children: [
                Container(
                  alignment: Alignment.center,
                  width: 64,
                  child: Text(
                    '排名',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      '公司名称',
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 80,
                  alignment: Alignment.center,
                  child: Text(
                    '双体系评分',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.black87,
                    ),
                  ),
                )
              ],
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final item = dualSystemRankData[index];
              return Container(
                padding: EdgeInsets.all(8),
                child: Row(
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: 64,
                      child: index < 3
                          ? Builder(builder: (context) {
                              String icon = 'assets/images/home/<USER>/home_statistics_rank_1.png';
                              switch (index) {
                                case 1:
                                  icon = 'assets/images/home/<USER>/home_statistics_rank_2.png';
                                  break;
                                case 2:
                                  icon = 'assets/images/home/<USER>/home_statistics_rank_3.png';
                                  break;
                              }
                              return Image.asset(
                                icon,
                                scale: 2,
                              );
                            })
                          : Text(
                              '${index + 1}',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                    ),
                    Expanded(
                      child: Container(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          DU.safeString(item, ['name']),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: 80,
                      alignment: Alignment.center,
                      child: Text(
                        '${DU.safeValue<double>(item, ['score'], 0)}分',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                    )
                  ],
                ),
              );
            },
            itemCount: dualSystemRankData.length,
            separatorBuilder: (BuildContext context, int index) {
              return Divider(
                color: Color(0xffE8E8E8),
                thickness: 1,
                indent: 8,
                endIndent: 8,
              );
            }, //dualSystemRankData.length,
          ),
        ],
      ),
    );
  }

  @override
  List<Request>? onCreateRequest() {
    Request todayDataRequest = Request(
      RequestType.GET,
      '/qb/report/todayData',
      tag: 'todayData',
      params: () {
        final timestamp = DU.getDayStart();
        return {
          'entId': Global.entId,
          'startTime': timestamp.toIso8601String(),
          'endTime': DateTime.now().toIso8601String(),
        };
      },
      interceptor: (data) {
        print('interceptor: $data');
        final result = data.isEmpty ? {} : data.first;
        return result;
      },
    );

    Request weekDataRequest = Request(
      RequestType.GET,
      '/qb/report/weekYH',
      tag: 'weekData',
      params: () {
        final timestamp = DU.getWeekStart();
        return {
          'entId': Global.entId,
          'startTime': timestamp.toIso8601String(),
          'endTime': DateTime.now().toIso8601String(),
        };
      },
      interceptor: (data) {
        final result = data.isEmpty ? {} : data.first;
        return result;
      },
    );

    Request monthDataRequest = Request(
      RequestType.GET,
      '/qb/report/monthData',
      tag: 'monthData',
      params: () {
        final timestamp = DU.getMonthStart();
        return {
          'entId': Global.entId,
          'startTime': timestamp.toIso8601String(),
          'endTime': DateTime.now().toIso8601String(),
        };
      },
      interceptor: (data) {
        final result = data.isEmpty ? {} : data.first;
        return result;
      },
    );

    Request educationData1Request = Request(
      RequestType.GET,
      '/qb/report/ksCount',
      tag: 'educationData1',
      params: () {
        final start = DU.getMonthStart();
        final end = DU.getMonthEnd();
        return {
          'entId': Global.entId,
          'startTime': start.toIso8601String(),
          'endTime': end.toIso8601String(),
        };
      },
      interceptor: (data) {
        final result = data.isEmpty ? {} : data.first;
        return result;
      },
    );

    Request educationData2Request = Request(
      RequestType.GET,
      '/qb/report/answerPeopleCount',
      tag: 'educationData2',
      params: () {
        final start = DU.getMonthStart();
        final end = DU.getMonthEnd();
        return {
          'entId': Global.entId,
          'startTime': start.toIso8601String(),
          'endTime': end.toIso8601String(),
        };
      },
      interceptor: (data) {
        final result = data.isEmpty ? {} : data.first;
        return result;
      },
    );

    Request educationData3Request = Request(
      RequestType.GET,
      '/qb/report/aqTrainPolyline',
      tag: 'educationData3',
      params: () {
        final timestamp = DU.getYearStart();
        return {
          'entId': Global.entId,
          'startTime': timestamp.toIso8601String(),
          'endTime': DateTime.now().toIso8601String(),
        };
      },
    );

    Request dualSystemDataRequest = Request(
      RequestType.GET,
      '/qb/report/group_single',
      tag: 'dualSystemData',
      params: () {
        final timestamp = DU.getYearStart();
        return {
          'entId': Global.entId,
          'beginDate': timestamp.toIso8601String(),
          'endDate': DateTime.now().toIso8601String(),
        };
      },
    );

    Request dualSystemRankDataRequest = Request(
      RequestType.GET,
      '/qb/report/group_singleTable',
      // '/qb/report/conpanyScoreTable',
      tag: 'dualSystemRankData',
      params: () {
        final now = DateTime.now();
        final timestamp = DateTime(now.year, now.month - 1);
        return {
          'entId': Global.entId,
          'beginDate': DU.getMonthStart(offside: -1).toIso8601String(),
          'endDate': DU.getMonthEnd(offside: -1).toIso8601String(),
          // 'month': DateUtil.formatDate(timestamp, format: DateFormats.y_mo),
        };
      },
    );
    return [
      todayDataRequest,
      weekDataRequest,
      monthDataRequest,
      educationData1Request,
      educationData2Request,
      educationData3Request,
      dualSystemDataRequest,
      dualSystemRankDataRequest,
    ];
  }

  @override
  bool onCreateRequestInorder() => false;
}

class ItemInfo {
  final String title;
  final String info;
  final String unit;
  final Color? titleColor;
  final Color? infoColor;
  final String? icon;

  ItemInfo({
    required this.title,
    required this.info,
    required this.unit,
    this.titleColor,
    this.infoColor,
    this.icon,
  });
}
