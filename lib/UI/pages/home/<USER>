import 'dart:convert';

import 'package:ent_secutity_app/common/colors.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:flutter/material.dart'; //导入了Material UI组件库
import 'package:flutter/services.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';

import '../../../dnc/utils/PlusUtils.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import 'MineTab.dart';
import 'ScanQR.dart';

//home -Main
class MainPage extends StatefulWidget {
//_MyHomePageState类是MyHomePage类对应的状态类
  @override
  MainState createState() => MainState();
}

class MainState extends State<MainPage> with AutomaticKeepAliveClientMixin {
  var _isExpandeds = [];
  var todoShow = false;

  // JPushModel jPushModel = JPushModel();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    //
    // print("============================GET START========================");
    //
    // JPush jPush = JPush();
    // jPush.setup(
    //   appKey: "dff4337d07f5c22d79b6c7af",
    //   channel: "theChannel",
    //   production: false,
    //   debug: true, // 设置是否打印 debug 日志
    // );
    // jPush.getRegistrationID().then((registrationId) {
    //   print("=========================REGISTRATIONID=======================");
    //   print('registrationId: $registrationId');
    //   jPushModel.submitRegistrationId(registrationId);
    //   print("============================GET END===========================");
    // });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    Global.menus.forEach((e) {
      _isExpandeds.add(true);
    });
    return WillPopScope(
      onWillPop: () async {
        showTips();
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          leading: GestureDetector(
            child: Icon(Icons.qr_code),
            onTap: () => ScanQRCode.scan(context),
            // onDoubleTap: () => testNFC(context, true),
          ),
          centerTitle: true,
          backgroundColor: MyColors.theme,
          title: Text("首页"),
          actions: [
            IconButton(
                icon: Icon(Icons.person),
                onPressed: () {
                  WU.nextPage(context, MineTabWidget());
                  // Navigator.pushNamed(context, '/my_center');
                }),
          ],
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                    image: DecorationImage(image: AssetImage("lib/assets/images/main_bg.png"), fit: BoxFit.cover)),
                child: banner(),
              ),
              Container(
                margin: const EdgeInsets.all(10),
                child: ExpansionPanelList(
                  children: Global.menus
                      .asMap()
                      .entries
                      .map(
                        (entry) => ExpansionPanel(
                          headerBuilder: (context, isExpanded) {
                            return ListTile(
                                title: Text(entry.value['name'],
                                    style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.w600)));
                          },
                          body: Align(
                            alignment: Alignment.centerLeft,
                            child: Wrap(
                              spacing: 10,
                              children: entry.value.containsKey('children')
                                  ? (entry.value['children'] as List)
                                      .map(
                                        (e) => GestureDetector(
                                          child: Container(
                                            margin: const EdgeInsets.only(bottom: 0),
                                            // decoration: BoxDecoration(color: Colors.blue),
                                            width: (MediaQuery.of(context).size.width - 60) / 4,
                                            height: 95.0,
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                                              children: [
                                                e.containsKey('icon')
                                                    ? Image(
                                                        image: AssetImage(e['icon']), width: 50, fit: BoxFit.contain)
                                                    : SizedBox(),
                                                Container(
                                                    margin: const EdgeInsets.only(top: 4),
                                                    height: 35.0,
                                                    child: Text(e['name'],
                                                        style: TextStyle(height: 1, color: Color(0xff555657)))),
                                              ],
                                            ),
                                          ),
                                          onTap: () {
                                            Navigator.pushNamed(context, e['url'],
                                                arguments:
                                                    e.containsKey('arguments') ? jsonDecode(e['arguments']) : {});
                                          },
                                        ),
                                      )
                                      .toList()
                                  : [],
                            ),
                          ),
                          isExpanded: _isExpandeds[entry.key],
                          canTapOnHeader: true,
                        ),
                      )
                      .toList(),
                  expansionCallback: (panelIndex, isExpanded) {
                    setState(() {
                      _isExpandeds[panelIndex] = !isExpanded;
                    });
                  },
                  animationDuration: kThemeAnimationDuration,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  //banner 图
  Widget banner() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: new BorderRadius.circular((8.0)),
      ),
      margin: EdgeInsets.fromLTRB(10, 15, 15, 10),
      height: 145.0,
      child: new Swiper(
        // 横向
        scrollDirection: Axis.horizontal,
        // 布局构建
        itemBuilder: (BuildContext context, int index) {
          return new Image(
              image: index == 0
                  ? AssetImage('lib/assets/images/lunbo_01.png')
                  : AssetImage('lib/assets/images/lunbo_02.png'),
              fit: BoxFit.contain);
        },
        //条目个数
        itemCount: 2,
        // 自动翻页
        autoplay: true,
        // 分页指示
        pagination: buildPlugin(),
        //点击事件
        onTap: (index) {
          print(" 点击 " + index.toString());
        },
        // 相邻子条目视窗比例
        viewportFraction: 1,
        // 布局方式
        //layout: SwiperLayout.STACK,
        // 用户进行操作时停止自动翻页
        autoplayDisableOnInteraction: true,
        // 无线轮播
        loop: true,
        //当前条目的缩放比例
        scale: 1,
      ),
    );
  }

  buildPlugin() {
    return SwiperPagination();
  }

  // 分页指示器
  buildSwiperPagination() {
    return SwiperPagination(
      //指示器显示的位置
      alignment: Alignment.bottomCenter, // 位置 Alignment.bottomCenter 底部中间
      // 距离调整
      margin: const EdgeInsets.fromLTRB(0, 0, 0, 5),
      // 指示器构建
      builder: DotSwiperPaginationBuilder(
          // 点之间的间隔
          space: 2,
          // 没选中时的大小
          size: 6,
          // 选中时的大小
          activeSize: 12,
          // 没选中时的颜色
          color: Colors.black54,
          //选中时的颜色
          activeColor: Colors.white),
    );
  }

  //主页面菜单
  mainMenuView(String img, String text, String route, {arguments}) {
    return Expanded(
      flex: 1,
      child: GestureDetector(
          child: Container(
            margin: EdgeInsets.fromLTRB(0, 15, 0, 0),
            child: Column(
              children: <Widget>[
                Image(image: AssetImage(img), width: 50, fit: BoxFit.contain),
                Container(
                    alignment: Alignment.center,
                    height: 30.0,
                    child: Text(text, style: TextStyle(color: Color(0xff555657)))),
              ],
            ),
          ),
          onTap: () {
            Navigator.pushNamed(context, route, arguments: arguments);
          }),
    );
  }

  //退出
  Future<void> pop() async {
    await SystemChannels.platform.invokeMethod('SystemNavigator.pop');
  }

  void showTips() async {
    bool flag = await showDialog<bool>(
            context: context,
            builder: (BuildContext context) {
              return new AlertDialog(
                title: Text("提示"),
                content: Text("您确定要退出吗?"),
                actions: [
                  TextButton(onPressed: () => Navigator.of(context).pop(true), child: Text("确定")),
                  TextButton(onPressed: () => Navigator.of(context).pop(false), child: Text("取消")),
                ],
              );
            }) ??
        false;
    if (flag) {
      //退出当前页面
      await this.pop();
    }
  }

  testNFC(BuildContext context, bool isTest) async {
    var info = '';
    late StateSetter st;
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return StatefulBuilder(
            builder: (BuildContext context, StateSetter stateSetter) {
              st = stateSetter;
              return Container(
                color: Colors.white,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(info),
                  ],
                ),
              );
            },
          );
        });
    NFC.getNFCInfo().then((tag) async {
      if (tag != null) {
        st(() {
          info = tag;
        });
      }
    });
  }
}
