import 'package:dio/dio.dart';
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/material.dart';

import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import '../statistics/StatisticsHomePage.dart';

class StatisticsTile extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => StatisticsTileState();
}

class StatisticsTileState extends State<StatisticsTile> {
  String id = '';

  String viewSubEntpriseFlag = '';

  @override
  void initState() {
    getData();
    super.initState();
  }

  Future getData() async {
    final result = await Request(
      RequestType.GET,
      '/qyaq/dp/leaderView/getDefaultTemplateAndViewSubEntFlag',
    ).execute();
    if (result is DioException) {
      return;
    }
    id = DU.safeString(result, ['id']);

    viewSubEntpriseFlag = DU.safeString(result, ['viewSubEntpriseFlag']);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    bool isHide = id.isEmpty || viewSubEntpriseFlag.isEmpty;
    return isHide
        ? SizedBox()
        : InkWell(
            child: Container(
              margin: EdgeInsets.all(8),
              height: 72,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(4)),
                image: DecorationImage(
                  image: AssetImage(
                      'assets/images/home/<USER>/home_statistics_home.png'),
                  fit: BoxFit.fitWidth, // 完全填充
                ),
              ),
            ),
            onTap: () {
              WU.nextPage(
                  context,
                  StatisticsHomePage(
                    id: id,
                    viewSubEntpriseFlag: viewSubEntpriseFlag,
                  ));
              // WU.nextPage(context, StatisticsPage());
            },
          );
  }
}
