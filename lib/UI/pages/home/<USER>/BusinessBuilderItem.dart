import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../../../../dnc/utils/WidgetUtils.dart';
import '../../../../dnc/widget/components/Card.dart';
import '../../../../viewmodel/home/<USER>';
import '../../../../viewmodel/home/<USER>';
import 'BusinessItemFactory.dart';

class BusinessBuilderItem implements BusinessBuilder {
  @override
  String getType() => "20";

  // @override
  // String getTitle() => "人员定位";

  @override
  Widget build(BuildContext context, String businessTypeLast, dynamic businessData,
      Function refresh,MessageListViewModel listModel) {
    final messageId = DU.safeString(businessData, ['id']);
    final state = DU.safeString(businessData, ['state']);
    final title = DU.safeString(businessData, ['title']);
    final subTitle = DU.safeString(businessData, ['receivingTime']);
    dynamic info = [
      {
        'name': '',
        'value': DU.formatDate(businessData, ['content'])
      },
      {
        'name': '业务类型',
        'value': DU.safeString(businessData, ['type'])
      },
      {
        'name': '处理时间',
        'value': DU.safeString(businessData, ['handleTime']),
        'single': false,
      },
    ];

    return
      Container(
      child: StyleCard1(
        title: title,
        subTitle : subTitle,
        info: info,
        margin: EdgeInsets.fromLTRB(0, 0, 0, 0),
        onTap: () async {
          print("图片被点击了！:${messageId} 99999: ${state}");
          if(state != 'read'){
            await HomeViewModel().messageRead(messageId);
            listModel.refresh();
          }
        },
      ),
    );
  }
}
