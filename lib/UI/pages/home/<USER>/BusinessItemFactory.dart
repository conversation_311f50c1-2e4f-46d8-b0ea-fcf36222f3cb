import 'package:ent_secutity_app/UI/pages/home/<USER>/BusinessBuilderItem.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../../../../dnc/widget/components/Card.dart';
import '../../../../viewmodel/home/<USER>';

class BusinessItemFactory {
  List<BusinessBuilder> items = [];

  BusinessItemFactory() {
    items.add(BusinessBuilderItem());


  }

  Widget build(BuildContext context, dynamic data, Function refresh,MessageListViewModel listModel) {
    final String businessTypeLast = DU.safeString(data, ["type"]);
    final Widget unKnowWidget = SimpleCard(
      body: Text('$data'),
    );
    String typeTitle = " $businessTypeLast";
    Widget businessWidget = unKnowWidget;
    // if (DU.isStrEmpty(businessTypeLast)) {
    //   typeTitle = "错误类型";
    //   businessWidget = unKnowWidget;
    // } else {
      for (BusinessBuilder builder in items) {
        // if (builder.getType() == businessType) {
        //   typeTitle = builder.getTitle();
          businessWidget = builder.build(
              context, businessTypeLast, data, refresh,listModel);
          break;
        // }
      }
    // }
    return
      Container(
        margin: EdgeInsets.fromLTRB(12, 8, 12, 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  child: Text(
                    typeTitle,
                    style: TextStyle(
                      color: Color(0xffA1A1A1),
                      fontSize: 13,
                    ),
                  ),
                ),
                data['state'] != 'read' // ，unread 未读，read 已读
                    ? Container(
                        margin: EdgeInsets.fromLTRB(8, 0, 8, 3),
                        child: Stack(
                          fit: StackFit.loose,
                          alignment: Alignment.bottomLeft,
                          children: [
                            Container(
                              color: Colors.red,
                              width: 6,
                              height: 6,
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 5, vertical: 1),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8.0)),
                              ),
                              child: Text(
                                "NEW",
                                style: TextStyle(
                                  fontSize: 8,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : SizedBox(),
              ],
            ),
            businessWidget,
          ],
        ),
    );
  }
}

abstract class BusinessBuilder {
  String getType();

  // String getTitle();

  Widget build(BuildContext context, String businessTypeLast,
      dynamic data, Function refresh,MessageListViewModel listModel);
}
