import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

class LoginHelper {
  Future<bool> getRememberPwd() async {
    return (await SharedPreferences.getInstance()).getBool('rememberPwd') ?? false;
  }

  Future<List> getLoginList() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    String? loginListInfo = prefs.getString('loginList') ?? null;
    if (loginListInfo == null) {
      return [];
    }
    return List.from(jsonDecode(loginListInfo));
  }

  Future<bool> saveLogin(String name, String password) async {
    final rememberPwd = await getRememberPwd();
    List loginList = await getLoginList();
    for (dynamic info in loginList) {
      if (info['name'] == name) {
        loginList.remove(info);
        break;
      }
    }
    if (rememberPwd) {
      loginList.insert(0,{'name': name, 'password': password});
    } else {
      loginList.insert(0,{'name': name});
    }

    try {
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.setString('loginList', jsonEncode(loginList));
    } catch (e) {
      return false;
    }

    return true;
  }
}
