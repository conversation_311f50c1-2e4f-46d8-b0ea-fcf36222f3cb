import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:ent_secutity_app/UI/pages/login/server_url_selector_widget.dart';
import 'package:ent_secutity_app/common/ServiceManager.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/plugs/SimpleLocation.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:ent_secutity_app/dnc/widget/advance/PasswordTextFormField.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:ent_secutity_app/dnc/widget/base/DefaultThemeData.dart';
import 'package:ent_secutity_app/dnc/widget/components/File.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:ent_secutity_app/viewmodel/APPModel.dart';
import 'package:ent_secutity_app/viewmodel/LoginViewModel.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../main.dart';
import 'LoginHelper.dart';
import 'UpgradeDialog.dart';

class LoginPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => LoginPageState();
}

class LoginPageState extends BaseStatefulWidgetState<LoginPage> {
  GlobalKey _formKey = new GlobalKey<FormState>();
  List loginList = [];
  TextEditingController _unameController = new TextEditingController();
  TextEditingController _pwdController = new TextEditingController();
  bool passwordVisible = false;
  bool _rememberPwd = false;
  bool devModelOpen = false;
  int locationLogin = -1; // -1 不需要， 0，定位中，1，范围外，2 范围内
  ServiceManager serviceManager = ServiceManager();
  int versionState = 0; // -1 错误 , 0,未检查,1, 匹配, 2,有差异

  @override
  void initState() {
    appInit();
    super.initState();
  }

  void appInit() async {
    // FilePreviewManager.init();
    getLoginInfo();
    await serviceManager.init();
    await changedService();
    // checkService();
  }

  @override
  Widget body(BuildContext context) {
    return Scaffold(
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
          statusBarColor: Color(0xff2A80FF),
        ),
        child: SafeArea(
          child: Container(
            width: double.infinity,
            child: Stack(
              alignment: Alignment.center,
              children: [
                Positioned.fill(
                  child: Image.asset(
                    'assets/images/login/bg.png',
                    fit: BoxFit.cover,
                  ),
                ),
                entBuilder(context),
                // versionBuilder(context),
                madeBuilder(context),
                Positioned(
                  top: 88,
                  child: Container(
                    alignment: Alignment.center,
                    width: WU.getScreenWidth(context) * 0.9,
                    height: WU.getScreenHeight(context) * 0.8,
                    child: Column(
                      children: [
                        titleBuilder(context),
                        SizedBox(
                          height: 32,
                        ),
                        formBuilder(context),
                      ],
                    ),
                  ),
                ),
                // if (ServiceManager.isDevMode) devBuilder(context)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget titleBuilder(BuildContext context) {
    return InkWell(
      child: Image.asset(
        "assets/images/login/title.png",
        width: 250,
      ),
      onLongPress: () {
        // devModelOpen = true;
      },
      onDoubleTap: () {
        // if (!devModelOpen) {
        //   return;
        // }
        // devModelOpen = false;
        // SDialog.showInput(context, title: '请输入企业编码',
        //     onCommand: (String value) async {
        //   SDialog.dismiss(tag: "input");
        //   final result = await serviceManager.setChannel(value);
        //   if (result) {
        //     Navigator.of(context).pushReplacementNamed('/login');
        //   }
        // });
      },
    );
  }

  Widget formBuilder(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      constraints: BoxConstraints(
        minHeight: WU.getScreenHeight(context) * 0.6,
      ),
      decoration: BoxDecoration(
          shape: BoxShape.rectangle,
          borderRadius: BorderRadius.all(Radius.circular(8)),
          color: Colors.white),
      padding: EdgeInsets.symmetric(horizontal: 24),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownSearch<dynamic>(
              items: loginList,
              selectedItem: null,
              compareFn: (dynamic item, dynamic selectedItem) {
                return DU.safeString(item, ['name']) ==
                    DU.safeString(selectedItem, ['name']);
              },
              dropdownDecoratorProps: DropDownDecoratorProps(
                baseStyle: TextStyle(fontSize: 16, color: Colors.blue),
                textAlign: TextAlign.center,
                dropdownSearchDecoration: InputDecoration(
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              onChanged: (data) {
                _unameController.text = DU.safeString(data, ['name']);
                _pwdController.text = DU.safeString(data, ['password']);
                setState(() {});
              },
              dropdownBuilder: (BuildContext context, dynamic item) {
                return TextFormField(
                    controller: _unameController,
                    style: TextStyle(color: Colors.black, fontSize: 16),
                    decoration: InputDecoration(
                      hintText: "输入账号",
                      contentPadding: EdgeInsets.fromLTRB(10, 19, 10, 15),
                      hintStyle:
                          DefaultThemeData.get.primaryTextTheme.bodyText1,
                    ),
                    // 校验用户名
                    validator: (v) {
                      return v!.trim().length > 0 ? null : "账号不能为空";
                    });
              },
              popupProps: PopupProps.menu(
                fit: FlexFit.loose,
                itemBuilder:
                    (BuildContext context, dynamic item, bool isSelected) {
                  // print('popupItemBuilder $item');
                  return Container(
                    margin: EdgeInsets.fromLTRB(8, 8, 8, 8),
                    padding: EdgeInsets.fromLTRB(12, 4, 12, 4),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                      color: isSelected ? Color(0xffEFEFEF) : Colors.white,
                    ),
                    child: Row(children: [
                      Text(
                        DU.safeString(item, ['name']),
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xff343134),
                        ),
                      ),
                      Spacer(),
                      if (item['password'] != null) Text('******')
                    ]),
                  );
                },
                emptyBuilder: (BuildContext context, String searchEntry) =>
                    Container(),
                errorBuilder: (
                  BuildContext context,
                  String searchEntry,
                  dynamic exception,
                ) =>
                    Container(),
              ),
            ),
            Divider(
              color: DefaultThemeData.get.dividerColor,
              height: 1,
              thickness: 1,
            ),
            PasswordTextFormField(controller: _pwdController),
            SizedBox(height: 6),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Checkbox(
                  value: _rememberPwd,
                  activeColor: Color(0xff2A80FF), //选中时的颜色
                  onChanged: (value) {
                    setState(() {
                      _rememberPwd = value!;
                    });
                    SharedPreferences.getInstance().then(
                        (value) => value.setBool("rememberPwd", _rememberPwd));
                  },
                ),
                Text(
                  "记住密码",
                  style: TextStyle(
                    color: Color(0xff9C9C9C),
                    fontSize: 15,
                  ),
                ),
              ],
            ),
            SizedBox(height: 48),
            loginBuilder(context),
          ],
        ),
      ),
    );
  }

  Widget entBuilder(BuildContext context) {
    var currentLogo = serviceManager.currentServerLogo;
    // var currentLogo = 'assets/images/ent_logo/beiyang.png';
    // currentLogo = 'assets/images/ent_logo/sanjiao.jpg';
    if (currentLogo.isNotEmpty) {
      return Positioned(
        child: Image.asset(currentLogo),
        left: 16,
        top: 16,
        height: 36,
      );
    } else {
      return Positioned(
        child: Text(
          '${serviceManager.currentServerName} ',
          style: TextStyle(
              color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
        ),
        left: 16,
        top: 16,
        height: 36,
      );
    }
  }

  // Widget versionBuilder(BuildContext context) {
  //   Color versionColor = Colors.transparent;
  //   switch (versionState) {
  //     case -1:
  //       versionColor = Colors.red;
  //       break;
  //     case 1:
  //       versionColor = Colors.green;
  //       break;
  //     case 2:
  //       versionColor = Colors.amber;
  //       break;
  //   }
  //   return Positioned(
  //     left: 12,
  //     bottom: 12,
  //     child: InkWell(
  //       child: Container(
  //         color: Colors.transparent,
  //         child: Row(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             Container(
  //               margin: EdgeInsets.symmetric(horizontal: 4),
  //               width: 10,
  //               height: 10,
  //               decoration: BoxDecoration(
  //                 color: versionColor,
  //                 borderRadius: BorderRadius.circular(8),
  //               ),
  //             ),
  //             Text(
  //               '${DU.showVersionValue(Global.appVersion, Global.appVersionCode, full: true)}',
  //               style: TextStyle(
  //                   color: Colors.white,
  //                   fontSize: 11,
  //                   fontWeight: FontWeight.w400),
  //             ),
  //           ],
  //         ),
  //       ),
  //       onTap: () {
  //         SDialog.showInfo(context,
  //             title: '提示',
  //             info:
  //                 '当前协议版本:V$ProtocolVersions\n后端协议版本:${Global.serviceVersion}');
  //       },
  //     ),
  //   );
  // }

  Widget madeBuilder(BuildContext context) {
    return Positioned(
      right: 12,
      bottom: 12,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            ' power by ',
            style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontStyle: FontStyle.italic,
                fontWeight: FontWeight.w400),
          ),
          Image.asset('lib/assets/images/logo.png', width: 20, height: 20),
        ],
      ),
    );
  }

  Widget loginBuilder(BuildContext context) {
    // if (locationLogin) {
    //   return ButtonProgressIndicator(
    //     color: Colors.blue,
    //   );
    // }

    // switch (locationLogin) {
      //-1 不需要， 0，定位中，1，范围外，2 范围内
      // case 0:
      //   return Container(
      //     width: WU.getScreenWidth(context) * 0.8,
      //     height: 38,
      //     alignment: Alignment.center,
      //     decoration: BoxDecoration(
      //       color: ColorsI.dividerColor,
      //       borderRadius: BorderRadius.all(Radius.circular(20)),
      //     ),
      //     child: Row(
      //       crossAxisAlignment: CrossAxisAlignment.center,
      //       mainAxisSize: MainAxisSize.min,
      //       children: [
      //         SizedBox(
      //           width: 12,
      //           height: 12,
      //           child: CircularProgressIndicator(
      //             strokeWidth: 1,
      //             valueColor: AlwaysStoppedAnimation(ColorsI.blue66),
      //           ),
      //         ),
      //         SizedBox(width: 15),
      //         Text(
      //           '定位中',
      //           style: TextStyle(
      //             color: ColorsI.blue66,
      //             fontSize: 16,
      //           ),
      //         )
      //       ],
      //     ),
      //   );
      // case 1:
      //   return InkWell(
      //     child: Container(
      //       width: WU.getScreenWidth(context) * 0.8,
      //       height: 38,
      //       alignment: Alignment.center,
      //       decoration: BoxDecoration(
      //         color: Color(0xffF39826),
      //         borderRadius: BorderRadius.all(Radius.circular(16)),
      //       ),
      //       child: Row(
      //         mainAxisSize: MainAxisSize.min,
      //         children: [
      //           Icon(
      //             Icons.location_on_outlined,
      //             size: 16,
      //             color: ColorsI.white,
      //           ),
      //           SizedBox(width: 16),
      //           Text(
      //             '请在指定范围内登录',
      //             style: TextStyle(
      //               color: ColorsI.white,
      //               fontSize: 15,
      //             ),
      //           ),
      //         ],
      //       ),
      //     ),
      //     onTap: () {
      //       // location();
      //     },
      //   );
      // case 2:
      //   return InkWell(
      //     child: Container(
      //       width: WU.getScreenWidth(context) * 0.8,
      //       height: 38,
      //       alignment: Alignment.center,
      //       decoration: BoxDecoration(
      //         color: ColorsI.blue,
      //         borderRadius: BorderRadius.all(Radius.circular(16)),
      //       ),
      //       child: Row(
      //         mainAxisSize: MainAxisSize.min,
      //         children: [
      //           Icon(
      //             Icons.location_on_outlined,
      //             size: 16,
      //             color: ColorsI.white,
      //           ),
      //           SizedBox(width: 16),
      //           Text(
      //             '登 录',
      //             style: TextStyle(
      //               color: ColorsI.white,
      //               fontSize: 15,
      //             ),
      //           ),
      //         ],
      //       ),
      //     ),
      //     onTap: () {
      //       if ((_formKey.currentState as FormState).validate()) {
      //         LoginViewModel().login(
      //           _unameController.text,
      //           _pwdController.text,
      //           loginSuccess: () async {
      //             final routeName = '/home';
      //             Navigator.pushNamedAndRemoveUntil(
      //                 context, routeName, (route) => false);
      //           },
      //         );
      //       }
      //     },
      //   );
      // case -1:
      // default:
        return InkWell(
          child: Container(
            width: WU.getScreenWidth(context) * 0.8,
            height: 38,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: ColorsI.blue,
              borderRadius: BorderRadius.all(Radius.circular(16)),
            ),
            child: Text(
              '登 录',
              style: TextStyle(
                color: ColorsI.white,
                fontSize: 14,
              ),
            ),
          ),
          onTap: () {
            if ((_formKey.currentState as FormState).validate()) {
              final name = _unameController.text;
              final password = _pwdController.text;
                // Global.loginIsEncrypt = DU.safeString(result, ['cypher']) == '1';
                Global.loginIsEncrypt = false;
                print('checkLoginType: ${Global.loginIsEncrypt}');
              LoginViewModel().login(
                name,
                password,
                loginSuccess: () async {
                  print('--home----:');
                  LoginHelper().saveLogin(name, password);
                  final routeName = '/home';
                  Navigator.pushNamedAndRemoveUntil(
                      context, routeName, (route) => false);
                },
              );
            }
          },
        );
    // }
  }

  Widget devBuilder(BuildContext context) {
    return Positioned(
      bottom: 12,
      child: InkWell(
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.white70,
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          child: Row(
            children: [
              Icon(Icons.train, color: Colors.black54),
              SizedBox(width: 4),
              Text(
                serviceManager.currentServerName,
                style: TextStyle(color: Colors.black54, fontSize: 14),
              )
            ],
          ),
        ),
        onTap: () {
          DebugUrlSelectorWidget(
            host: ServiceManager.currentHost,
            testList: serviceManager.testList,
            serverList: serviceManager.serverList,
          ).show(context).then((host) async {
            if (host != null) {
              await serviceManager.setHost(host);
              // changedService();
            }
          });
        },
      ),
    );
  }

  Future<void> getLoginInfo() async {
    LoginHelper helper = LoginHelper();
    _rememberPwd = await helper.getRememberPwd();

    loginList = await helper.getLoginList();
    if (loginList.isEmpty) {
      return;
    }
    final loginData = loginList.first;
    _unameController.text = loginData['name'];
    if (_rememberPwd) {
      _pwdController.text = loginData['password'];
    }
    setState(() {});
  }

  // Future initAccountPassword() async {
  //   final SharedPreferences prefs = await SharedPreferences.getInstance();
  //   var username = prefs.getString("username") ?? "";
  //   _rememberPwd = prefs.getBool("rememberPwd") ?? false;
  //   var password = "";
  //   if (_rememberPwd) {
  //     password = prefs.getString('password') ?? "";
  //     // SmartDialog.showToast('Password: $username / $password', displayType: SmartToastType.multi,displayTime:Duration(seconds: 10));
  //   }
  //
  //   if (username.isNotEmpty) {
  //     _unameController.text = username;
  //     _pwdController.text = password;
  //   }
  //   setState(() {});
  // }

  changedService() async {
    // await initVersionInfo();
    // await compareProtocolVersions();

    // await checkLoginType();
    // positionLoginType();
  }

  // Future compareProtocolVersions() async {
  //   final result =
  //       await Request(RequestType.GET, '/qb/report/versionManage').execute();
  //   if (result is DioException) {
  //     versionState = -1;
  //     Global.serviceVersion = '未知版本';
  //     setState(() {});
  //     return;
  //   }
  //   if (result is! List || result.isEmpty) {
  //     versionState = -1;
  //     Global.serviceVersion = '未知版本';
  //     setState(() {});
  //     return;
  //   }
  //   Global.serviceVersion = DU.safeString(result.first, ['versionCode']);
  //   versionState = Global.serviceVersion.contains(ProtocolVersions) ? 1 : 2;
  //   setState(() {});
  // }

  // Future<void> checkLoginType() async {
  //   final result =
  //       await Request(RequestType.GET, '/qyaq/login/cypher').execute();
  //   if (result is DioException) {
  //     Global.loginIsEncrypt = false;
  //     return;
  //   }
  //   Global.loginIsEncrypt = DU.safeString(result, ['cypher']) == '1';
  //   print('checkLoginType: ${Global.loginIsEncrypt}');
  // }

  // Future positionLoginType() async {
  //   final result = await Request(
  //     RequestType.GET,
  //     '/qyaq/systemSetting/getPositionSettingForApp',
  //   ).execute(showLoading: false);
  //   if (result is DioException) {
  //     return;
  //   }
  //   locationLogin = result == '1' ? 0 : -1;
  //   if (locationLogin != -1) {
  //     location();
  //   }
  //   setState(() {});
  // }

  // Future location() async {
  //   if (kDebugMode) {
  //     setState(() {
  //       locationLogin = 2;
  //     });
  //     return;
  //   }
  //   setState(() {
  //     locationLogin = 0;
  //   });
  //   LocationData? data = await SimpleLocation(level: 2).getLocation(context);
  //   if (data == null) {
  //     Toast.show('未获取到定位信息');
  //     locationLogin = 1;
  //     if (mounted) {
  //       setState(() {});
  //     }
  //     return;
  //   }
  //   final result = await Request(
  //     RequestType.GET,
  //     '/qyaq/app/inPolygon',
  //     params: () => {
  //       'lon': data.longitude,
  //       'lat': data.latitude,
  //     },
  //   ).execute(showLoading: false);
  //   if (result is DioException) {
  //     locationLogin = 1;
  //     if (mounted) {
  //       setState(() {});
  //     }
  //     return;
  //   }
  //   locationLogin = result == '1' ? 2 : 1;
  //   if (mounted) {
  //     setState(() {});
  //   }
  // }

  // initVersionInfo() async {
  //   final packageInfo = await PackageInfo.fromPlatform();
  //   setState(() {
  //     Global.appVersion = packageInfo.version;
  //     Global.appVersionCode = int.parse(packageInfo.buildNumber);
  //   });
  //   checkVersion();
  // }

  // checkVersion() {
  //   APPModel().getAppVersion(subSuccess: (json) {
  //     final newVersion = DU.safeString(json, ["versionName"]);
  //     if (DU.compareVersion(newVersion, Global.appVersion) &&
  //         Platform.isAndroid) {
  //       showDialog(
  //           context: context,
  //           barrierDismissible: false,
  //           builder: (context) {
  //             return UpgradeDialog(
  //               description: json["updateInfo"],
  //               apkUrl: json["apkPath"],
  //             );
  //           });
  //     }
  //   });
  // }

  // Future checkService() async {
  //   if (ServiceManager.currentHost.isEmpty) {
  //     WidgetsBinding.instance.addPostFrameCallback(
  //       (_) {
  //         SDialog.showInput(
  //           context,
  //           title: '请输入企业编码',
  //           isMandatory: true,
  //           onCommand: (String value) async {
  //             final result = await serviceManager.setChannel(value);
  //             if (result) {
  //               changedService();
  //               SDialog.dismiss(tag: "input");
  //             } else {
  //               Toast.show("企业编码输入错误");
  //             }
  //           },
  //         );
  //       },
  //     );
  //   }
  // }

  @override
  void dispose() {
    FilePreviewManager.cleanCache();
    super.dispose();
  }
}
