import 'dart:io';

import 'package:ent_secutity_app/widget/flat_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:r_upgrade/r_upgrade.dart';

import '../../../dnc/widget/components/Pop.dart';

class UpgradeDialog extends StatefulWidget {
  final description, apkUrl;

  const UpgradeDialog({Key? key, this.description, this.apkUrl})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return UpgradeDialogState();
  }
}

class UpgradeDialogState extends State<UpgradeDialog> {
  bool _isDownload = false;
  DownloadInfo? downloadInfo;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0.0,
      backgroundColor: Colors.transparent,
      child: buildCard(context),
    );
  }

  Widget buildCard(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(8.0)),
        width: 280.0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
                height: 140.0,
                width: 280.0,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                      topLeft: const Radius.circular(8.0),
                      topRight: const Radius.circular(8.0)),
                  image: DecorationImage(
                      image: AssetImage("lib/assets/images/app_update.png"),
                      fit: BoxFit.cover),
                )),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.0),
              child: Text("新版本更新",
                  style: TextStyle(fontSize: 16, color: Colors.black)),
            ),
            Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 15.0, vertical: 10.0),
                child: Html(
                  // 渲染的数据
                  data: widget.description,
                )),
            Padding(
              padding: const EdgeInsets.only(
                  bottom: 15.0, left: 15.0, right: 15.0, top: 15.0),
              child: _isDownload
                  ? progressWidget(context)
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Container(
                          width: 110.0,
                          height: 36.0,
                          child: FlatButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            textColor: Colors.lightBlue,
                            color: Colors.white,
                            disabledTextColor: Colors.white,
                            disabledColor: Colors.grey,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(18.0),
                                side:
                                    BorderSide(color: Colors.blue, width: 0.8)),
                            child: Text("下次再说",
                                style: TextStyle(
                                    fontSize: 16, color: Colors.blue)),
                          ),
                        ),
                        Container(
                          width: 110.0,
                          height: 36.0,
                          child: FlatButton(
                            onPressed: () {
                              if (Platform.isAndroid) {
                                upgrade();
                              } else {
                                Toast.show('请至已购买项目中更新.');
                                // var url = 'https://itunes.apple.com/cn/app/%E5%86%8D%E6%83%A0%E5%90%88%E4%BC%99%E4%BA%BA/id1375433239?l=zh&ls=1&mt=8';
                                // InstallPlugin.gotoAppStore(url);
                              }
                            },
                            textColor: Colors.white,
                            color: Colors.blue,
                            disabledTextColor: Colors.white,
                            disabledColor: Colors.grey,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18.0),
                            ),
                            child: Text("立即更新", style: TextStyle(fontSize: 16)),
                          ),
                        )
                      ],
                    ),
            )
          ],
        ));
  }

  Widget progressWidget(BuildContext context) {
    var percent = 0.0;
    var current = '', total = '', speed = '';
    if (downloadInfo != null) {
      percent = double.parse((downloadInfo!.percent! / 100).toStringAsFixed(2));
      current =
          (downloadInfo!.currentLength! / (1024 * 1024)).toStringAsFixed(2);
      total = (downloadInfo!.maxLength! / (1024 * 1024)).toStringAsFixed(2);
      speed = (downloadInfo!.speed! / 1024).toStringAsFixed(2);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        downloadInfo != null
            ? Flexible(
                child: Row(
                children: [
                  Text(speed + 'Mb/s',
                      style: TextStyle(
                          color: Color(0x8a000000),
                          fontSize: 12,
                          fontWeight: FontWeight.w400)),
                  Spacer(),
                  Text('$current/$total' + 'Mb',
                      style: TextStyle(
                          color: Color(0x8a000000),
                          fontSize: 12,
                          fontWeight: FontWeight.w400))
                ],
              ))
            : Container(),
        LinearProgressIndicator(
            backgroundColor: Colors.grey,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.lightBlue),
            value: percent),
      ],
    );
  }

  void upgrade() async {
    setState(() {
      _isDownload = true;
    });
    final result = await RUpgrade.upgradeFromUrl(widget.apkUrl);
    RUpgrade.stream.listen((DownloadInfo info) {
      if (this.mounted) {
        setState(() {
          this.downloadInfo = info;
        });
      }
    }, onDone: () {
      Navigator.pop(context);
    }, onError: (e) {
      Toast.show('请重新下载安装');
      Navigator.pop(context);
    }, cancelOnError: false);
  }

  void cancel() async {
    if (downloadInfo != null && downloadInfo!.id != null) {
      await RUpgrade.cancel(downloadInfo!.id!);
    }
  }

  @override
  void dispose() {
    cancel();
    super.dispose();
  }
}
