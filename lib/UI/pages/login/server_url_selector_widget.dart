import 'package:flutter/material.dart';

import '../../../dnc/widget/components/Pop.dart';

class DebugUrlSelectorWidget extends StatefulWidget with SheetMixin {
  final String host;
  final List testList;
  final List serverList;
  final data = {};

  DebugUrlSelectorWidget({required this.host, required this.testList, required this.serverList});

  @override
  State<StatefulWidget> createState() {
    final state = _DebugUrlSelectorWidgetState(host);
    data['state'] = state;
    return state;
  }

  @override
  onCommand() {
    return data['state'].selected;
  }

  @override
  get title => "请选择服务器";

  show(BuildContext context) async {
    return await showWidget(context, this);
  }
}

class _DebugUrlSelectorWidgetState extends State<DebugUrlSelectorWidget> {
  final String host;
  final services = [];
  dynamic _selected;
  TextEditingController apiC = TextEditingController();

  String get selected {
    if (_selected != null) {
      return _selected['host'];
    } else {
      return apiC.text;
    }
  }

  _DebugUrlSelectorWidgetState(this.host);

  @override
  void initState() {
    services.addAll(widget.testList);
    services.addAll(widget.serverList);
    for (var service in services) {
      if (service['host'] == host) {
        _selected = service;
        break;
      }
    }
    //http://*************:9541
    if (_selected == null) {
      apiC.text = host;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(12.0, 12.0, 0, 0),
            child: Text(
              '测试服务',
              style: TextStyle(fontSize: 16),
            ),
          ),
          ...widget.testList.map<Widget>((data) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile(
                    title: Text(
                      data["name"] as String,
                      style: Theme.of(context).textTheme.subtitle1,
                    ),
                    value: data,
                    groupValue: _selected,
                    selected: _selected != null && _selected['host'] == data['host'],
                    onChanged: onChanged),
                Divider(
                  indent: 16,
                  endIndent: 16,
                  color: Color(0xffe7e7e7),
                  height: 1.0,
                  thickness: 1,
                ),
              ],
            );
          }).toList(),
          Container(
            padding: EdgeInsets.fromLTRB(12.0, 12.0, 0, 0),
            child: Text(
              '正式服务',
              style: TextStyle(fontSize: 16),
            ),
          ),
          ...widget.serverList.map<Widget>((data) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RadioListTile(
                    title: Text(
                      data["name"] as String,
                      style: Theme.of(context).textTheme.subtitle1,
                    ),
                    value: data,
                    groupValue: _selected,
                    selected: _selected != null && _selected['host'] == data['host'],
                    onChanged: onChanged),
                Divider(
                  indent: 16,
                  endIndent: 16,
                  color: Color(0xffe7e7e7),
                  height: 1.0,
                  thickness: 1,
                ),
              ],
            );
          }).toList(),
          Container(
            child: Text('自定义'),
          ),
          Container(
            padding: EdgeInsets.fromLTRB(12, 0, 20, 12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Radio(
                      // autofocus: true,
                        value: null,
                        groupValue: _selected,
                        onChanged: onChanged),
                    Expanded(
                      child: TextFormField(
                        controller: apiC,
                        decoration: InputDecoration(
                          filled: true,
                          hintText: '注意只填写到端口号!',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  onChanged(data) {
    setState(() {
      _selected = data;
    });
  }
}
