import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/network/Http.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
// import 'package:ent_secutity_app/dnc/widget/business/DepartmentSelector.dart';
// import 'package:ent_secutity_app/dnc/widget/business/UserSelector.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/File.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';
import 'package:ent_secutity_app/viewmodel/DictModel.dart';
import 'package:ent_secutity_app/viewmodel/meetings/MeetingsViewModel.dart';
import 'package:flutter/material.dart';

// import '../../../dnc/widget/advance/PhotoPicker.dart';
import '../../../dnc/widget/components/Pop.dart';
import '../../../dnc/widget/components/Widget.dart';

class MeetingsEditPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return MeetingsEditPageState();
  }
}

class MeetingsEditPageState extends BasePageState<MeetingsEditPage> {
  WidgetData<String> meetingName = WidgetData<String>();
  WidgetData<String> meetingAddress = WidgetData<String>();
  WidgetData meetingPeriod = WidgetData();
  WidgetData<DateTime> meetingStartTime = WidgetData<DateTime>();
  WidgetData<DateTime> meetingEndTime = WidgetData<DateTime>();
  WidgetData meetingType = WidgetData();
  WidgetData meetingDepartment = WidgetData();
  WidgetList meetingUsers = WidgetList();
  WidgetData<String> meetingUserStatus = WidgetData<String>();
  WidgetData<String> meetingTask = WidgetData<String>();
  WidgetData<String> meetingEducate = WidgetData<String>();
  WidgetData<String> meetingAttention = WidgetData<String>();
  WidgetData<String> meetingData = WidgetData<String>();
  WidgetData<String> meetingResume = WidgetData<String>();
  WidgetList<FileData> meetingAttachment = WidgetList<FileData>();

  int pageType = 0;

  @override
  String strTitle() => '新增会议';

  @override
  void initState() {
    super.initState();
  }

  onTypeChange(var info) {
    print('onTypeChange: ${info['code']}');
    setState(() {
      if (info['code'] == "1") {
        pageType = 1;
      } else {
        pageType = 2;
      }
    });
  }

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(
            children: [
              InputTile(title: '会议名称', hint: '请输入会议名称', wd: meetingName),
              InputTile(title: '会议地点', hint: '请输入会议地点', wd: meetingAddress),
              SingleSelectorTile(
                title: '会议周期',
                wd: meetingPeriod,
                data: () async => await DictModel().getDict('grda_metting_time'),
              ),
              DateTimeSelectorTile(title: '会议开始时间', wd: meetingStartTime),
              DateTimeSelectorTile(title: '会议结束时间', wd: meetingEndTime),
              SingleSelectorTile(
                title: '会议类型',
                wd: meetingType,
                data: () async => await DictModel().getDict('grda_metting_type'),
                onChange: onTypeChange,
              ),
              // DepartmentSelectorTile(title: '组织部门', wd: meetingDepartment),
              // MultipleUserSelectorTile(title: '参会人员', bottomLine: false, wl: meetingUsers),
            ],
            isSingle: false,
          ),
          pageType != 0
              ? TileCard(
                  isSingle: true,
                  children: [
                    pageType == 1
                        ? Column(
                            children: [
                              ExtensibleInputTile(
                                  title: '与会人员状态',
                                  tip: '通过观察和询问，了解上岗人员身体、心理和情绪状况，排查安全生产不放心人员。对排查出的不放心人要有管理措施，安排帮扶人员和顶岗人员。',
                                  icon: Icons.help,
                                  wd: meetingUserStatus),
                              ExtensibleInputTile(
                                  title: '部署任务',
                                  tip: '传达贯彻上级、企业有关文件、会议精神和安全生产安排，布置当班工作任务。',
                                  icon: Icons.help,
                                  wd: meetingTask),
                              ExtensibleInputTile(
                                  title: '教育培训',
                                  tip: '结合工作现场实际和天气情况，针对每个环节、每道工序、每个作业点强调安全生产注意事项，点评、解析存在的隐患或可能出现的问题，提出应急措施和整改办法。',
                                  icon: Icons.help,
                                  wd: meetingEducate),
                              ExtensibleInputTile(
                                  title: '注意事项',
                                  tip:
                                      '结合当班工作任务，针对当前安全生产重点工作、岗位注意事项、应知应会知识、工作任务、工艺参数、防护装备配戴、应急装备使用等重点内容抽查2至3人，结合同类企业事故案例开展警示教育。',
                                  icon: Icons.help,
                                  wd: meetingAttention),
                              ExtensibleInputTile(title: '会议资料', tip: '主要内容、员工签到等资料应齐全并存档备查', wd: meetingData),
                            ],
                          )
                        : ExtensibleInputTile(title: '会议概要', wd: meetingResume),
                    // PhotoPicker(
                    //   title: '现场情况',
                    //   read: false,
                    //   wl: meetingAttachment,
                    //   bottomLine: false,
                    // ),
                  ],
                )
              : Container(),
          SizedBox(height: 20),
          pageType != 0 ? TileButton(name: "提交", onPressed: onSubmit) : Container(),
          SizedBox(height: 12)
        ],
      ),
    );
  }

  onSubmit() async {
    if (meetingName.isEmpty) {
      Toast.show('请填写会议名称');
      return;
    }
    if (meetingAddress.isEmpty) {
      Toast.show('请填写会议地址');
      return;
    }
    if (meetingPeriod.isEmpty) {
      Toast.show('请选择会议周期');
      return;
    }
    if (meetingStartTime.isEmpty) {
      Toast.show('请选择会议开始时间');
      return;
    }
    if (meetingEndTime.isEmpty) {
      Toast.show('请选择会议结束时间');
      return;
    }
    if (meetingType.isEmpty) {
      Toast.show('请选择会议类型');
      return;
    }
    if (meetingDepartment.isEmpty) {
      Toast.show('请选择组织部门');
      return;
    }
    if (meetingUsers.isEmpty) {
      Toast.show('请选择参会人员');
      return;
    }
    bool result = await Http().uploadWidgetList(wl: meetingAttachment, tag: 'meetingAttachment');
    if (!result) {
      return;
    }
    final file = DU.mergeObject<FileData>(meetingAttachment.list, (FileData data) {
      return data.url;
    });
    var params = {
      "name": meetingName.data,
      "address": meetingAddress.data,
      "mettingPeriod": meetingPeriod.data["code"],
      "meetingTime": [meetingStartTime.data!.millisecondsSinceEpoch,meetingEndTime.data!.millisecondsSinceEpoch],
      "mettingType": meetingType.data["code"],
      "deptId": meetingDepartment.data['id'],
      "userList": meetingUsers.list,
      "file": file,
    };
    if (pageType == 1) {
      params["userType"] = meetingUserStatus.data;
      params["task"] = meetingTask.data;
      params["educate"] = meetingEducate.data;
      params["shixiang"] = meetingAttention.data;
      params["meetingData"] = meetingData.data;
    } else {
      params["meetingResume"] = meetingResume.data;
    }
    // L.d("params: $params");
    MeetingsViewModel().add(params, success: () {
      Navigator.pop(context, 'refresh');
    });
  }
}
