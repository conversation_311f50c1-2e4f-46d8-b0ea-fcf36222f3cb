import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/File.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:ent_secutity_app/dnc/widget/components/FormInput.dart';
import 'package:ent_secutity_app/viewmodel/quality_testing/QualityTestingViewModel.dart';
import 'package:flutter/material.dart';

import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/components/StateTile.dart';
import '../../../dnc/widget/components/Widget.dart';
import '../../../viewmodel/DictModel.dart';

class MeetingsDetailPage extends StatefulWidget {
  final item;

  MeetingsDetailPage(this.item, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MeetingsDetailPageState();
  }
}

class MeetingsDetailPageState extends BasePageState<MeetingsDetailPage> {
  MeetingsViewModel _model = MeetingsViewModel();
  WidgetData checkType = WidgetData();
  WidgetData samplePoint = WidgetData();

  @override
  String strTitle() => '采购质检';

  @override
  void initState() {
    initData();
    super.initState();
  }

  initData() async {
      setData(widget.item, "meetingData");
  }

  @override
  Widget body(BuildContext context) {
    final mdata = getData("meetingData");
    if (mdata == null) return WU.emptyWidget(context);
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(children: [
            TextTile(title: '运单编号', info: DU.safeString(mdata, ['billNo'])),
            TextTile(title: '运单日期', info: DU.safeString(mdata, ['billDate'])),
            TextTile(title: '客户名称', info: DU.safeString(mdata, ['customerNm'])),
            TextTile(title: '车牌号', info: DU.safeString(mdata, ['carNo'])),
            TextTile(title: '司机', info: DU.safeString(mdata, ['driverNm'])),
            TextTile(title: '物料名称', info: DU.safeString(mdata, ['materielNm'])),
            TextTile(title: '质检结果', info: DU.safeString(mdata, ['checkResultLabel'])),
            SingleSelectorTile(
              title: '检验类别',
              wd: checkType,
              data: () async => await DictModel().getDict('dictCheckType'),
            ),
            SingleSelectorTile(
              title: '取样地点',
              wd: samplePoint,
              data: () async => await DictModel().getDict('dictSamplePoint'),
            ),
          ], isSingle: false),
          SizedBox(height: 30),
          TileButton(name: "提交", onPressed: onSubmit),
          SizedBox(height: 12)
        ],
      ),
    );
  }
  onSubmit() async {
    var params = {
      "id": widget.item['id'],
      "billNo": widget.item['billNo'],
      "checkType": DU.safeString(checkType.data, ['code']),
      "samplePoint": DU.safeString(samplePoint.data, ['code']),
    };
    MeetingsViewModel().purchaseAdd(params, success: (json) {
      Navigator.pop(context, 'refresh');
    });
  }
}
