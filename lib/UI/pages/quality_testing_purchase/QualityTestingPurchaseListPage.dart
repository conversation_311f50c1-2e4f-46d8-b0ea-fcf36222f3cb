import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/viewmodel/DictModel.dart';
import 'package:ent_secutity_app/viewmodel/quality_testing/QualityTestingPurchaseListViewModel.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';

import '../../../dnc/utils/WidgetUtils.dart';
import 'QualityTestingPurchaseDetailPage.dart';
import 'QualityTestingPurchaseSearchPage.dart';

class QualityTestingPurchaseListPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return QualityTestingPurchaseListPageState();
  }
}

class QualityTestingPurchaseListPageState extends BasePageState<QualityTestingPurchaseListPage> {
  MeetingsListViewModel _listModel = MeetingsListViewModel();

  @override
  String strTitle() => '采购质检';

  @override
  void initState() {
    super.initState();
  }

  static const platform = MethodChannel('com.xinpai.printer/print');

  String _printResult = '未打印';

  Future<void> _printLabel(String dataToPrint) async {
    String printResult;
    try {
      final String result = await platform.invokeMethod('printLabel', {'data': dataToPrint});
      printResult = '打印成功: $result';
    } on PlatformException catch (e) {
      printResult = "打印失败: '${e.message}'.";
    }

    setState(() {
      _printResult = printResult;
    });
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          child: Container(
            color: Colors.white,
            padding: EdgeInsets.fromLTRB(16, 16, 16, 16),
            child: Row(
              children: [
                Icon(Icons.search, size: 20, color: Theme.of(context).hintColor),
                SizedBox(width: 20),
                Text('点击按条件查询', style: Theme.of(context).textTheme.headline3),
              ],
            ),
          ),
          onTap: () {
            toSearch(context);
          },
        ),
        Flexible(
            child: CustomItemListWidget(
          model: _listModel,
          itemBuilder: itemBuilder,
          enablePullDown: true,
          enablePullUp: true,
          showSeparator: false,
        )),
      ],
    );
  }

  Widget itemBuilder(BuildContext context, item, int index, bool isEnd) {
    var text;
    var textStyle;
    return SimpleCard(
      isSingle: false,
      tip: text,
      showShadow: false,
      style: textStyle,
      body: Container(
        padding: EdgeInsets.all(8),
        child: Column(
          children: [
            infoLabel(context, '运单编号:', item['billNo']?? ''),
            infoLabel(context, '运单日期:', item['billDate']?? ''),
            infoLabel(context, '客户名称:', item['customerNm']?? ''),
            infoLabel(context, '车牌号:', item['carNo']?? ''),
            infoLabel(context, '司机:', item['driverNm']?? ''),
            infoLabel(context, '物料名称:', item['materielNm']?? ''),
            infoLabel(context, '质检结果:', item['checkResultLabel']?? ''),
            Column(
              children: [
                Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
                Container(
                  padding: EdgeInsets.fromLTRB(20, 12, 20, 6),
                  child: ContentButton(
                    name:  item['checkResult'] == null
                        ? "提交质检"
                        : item['checkResult'] == '2'
                        ? "重新质检"
                        : "打印条码",
                    minSize: Size(150, 40),
                    onPressed: () => toDetail(context, item),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
      // onTap: () => toDetail(context, item),
    );
  }

  infoLabel(BuildContext context, String name, String value) {
    return Container(
      padding: EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          Container(
            constraints: BoxConstraints(minWidth: 100),
            child: Text(
              name,
              style: Theme.of(context).primaryTextTheme.headline2,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: Theme.of(context).primaryTextTheme.headline2,
            ),
          ),
        ],
      ),
    );
  }

  toSearch(BuildContext context) async {
    final result = await WU.nextPage(context, QualityTestingPurchaseSearchPage());
    if (result == null) return;
    Map data = result;
    _listModel.carNo = data['carNo'];
    _listModel.driverPhone = data['driverPhone'];
    _listModel.driverIdCard = data['driverIdCard'];
    _listModel.refresh();
  }

  toDetail(BuildContext context, dynamic item) async {
    if(DU.safeString(item,['checkResult']).isEmpty || DU.safeString(item,['checkResult']) == '2'){
      //提交质检和重新质检
      final result =  await WU.nextPage(context, MeetingsDetailPage(item));
      if (result != null) {
        _listModel.refresh();
      }
    }else{
      //打印条码
      print("---打印--submit:${item}");
      _printLabel(item['sampleNo']);
    }
  }
  // toAdd(BuildContext context) async {
  //   final result = await WU.nextPage(context, MeetingsEditPage());
  //   if (result != null) {
  //     _listModel.refresh();
  //   }
  // }
}
