import 'package:flutter/material.dart';
import 'package:ent_secutity_app/model/question_info.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';

class QuestionDetailPage extends StatefulWidget {
  final QuestionInfo question;

  const QuestionDetailPage({Key? key, required this.question})
      : super(key: key);

  @override
  State<QuestionDetailPage> createState() => _QuestionDetailPageState();
}

class _QuestionDetailPageState extends BasePageState<QuestionDetailPage> {
  @override
  String strTitle() => '题目详情';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TileCard(
            children: [
              TextTile(title: '题目', info: widget.question.questionName),
              TextTile(title: '答案', info: widget.question.questionAnswer),
              TextTile(title: '提示信息', info: widget.question.questionTip),
              TextTile(title: '答案解析', info: widget.question.analysis),
              TextTile(title: '选项A', info: widget.question.questionA),
              TextTile(title: '选项B', info: widget.question.questionB),
              TextTile(title: '选项C', info: widget.question.questionC),
              TextTile(title: '选项D', info: widget.question.questionD),
            ],
            isSingle: true,
          ),
        ],
      ),
    );
  }
}
