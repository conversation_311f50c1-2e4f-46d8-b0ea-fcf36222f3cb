import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ent_secutity_app/model/question_info.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';
import 'package:ent_secutity_app/viewmodel/question/QuestionListViewModel.dart';

class QuestionEditPage extends StatefulWidget {
  final QuestionInfo? question;

  const QuestionEditPage({Key? key, this.question}) : super(key: key);

  @override
  State<QuestionEditPage> createState() => _QuestionEditPageState();
}

class _QuestionEditPageState extends BasePageState<QuestionEditPage> {
  late QuestionInfo question;
  final QuestionListViewModel _viewModel = QuestionListViewModel();

  WidgetData<String> questionNameWd = WidgetData<String>();
  WidgetData<String> questionAnswerWd = WidgetData<String>();
  WidgetData<String> questionTipWd = WidgetData<String>();
  WidgetData<String> analysisWd = WidgetData<String>();
  WidgetData<String> questionAWd = WidgetData<String>();
  WidgetData<String> questionBWd = WidgetData<String>();
  WidgetData<String> questionCWd = WidgetData<String>();
  WidgetData<String> questionDWd = WidgetData<String>();

  @override
  void initState() {
    super.initState();
    question = widget.question ?? QuestionInfo();

    questionNameWd.data = question.questionName;
    questionAnswerWd.data = question.questionAnswer;
    questionTipWd.data = question.questionTip;
    analysisWd.data = question.analysis;
    questionAWd.data = question.questionA;
    questionBWd.data = question.questionB;
    questionCWd.data = question.questionC;
    questionDWd.data = question.questionD;
  }

  @override
  String strTitle() => widget.question == null ? '添加题目' : '编辑题目';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          TileCard(
            children: [
              InputTile(
                  title: '题目',
                  hint: '请输入题目',
                  wd: questionNameWd,
                  important: true),
              InputTile(
                  title: '答案',
                  hint: '请输入答案（A/B/C/D）',
                  wd: questionAnswerWd,
                  important: true),
              InputTile(title: '提示信息', hint: '请输入提示信息', wd: questionTipWd),
              ExtensibleInputTile(
                  title: '答案解析', hint: '请输入答案解析', wd: analysisWd),
              InputTile(title: '选项A', hint: '请输入选项A', wd: questionAWd),
              InputTile(title: '选项B', hint: '请输入选项B', wd: questionBWd),
              InputTile(title: '选项C', hint: '请输入选项C', wd: questionCWd),
              InputTile(title: '选项D', hint: '请输入选项D', wd: questionDWd),
            ],
            isSingle: true,
          ),
          SizedBox(height: 20),
          TileButton(name: "保存", onPressed: onSubmit),
        ],
      ),
    );
  }

  void onSubmit() {
    if (questionNameWd.data == null || questionNameWd.data!.isEmpty) {
      Toast.show('请输入题目');
      return;
    }

    if (questionAnswerWd.data == null || questionAnswerWd.data!.isEmpty) {
      Toast.show('请输入答案');
      return;
    }

    // 验证答案格式
    String answer = questionAnswerWd.data!.toUpperCase();
    if (!['A', 'B', 'C', 'D'].contains(answer)) {
      Toast.show('答案必须是A、B、C、D中的一个');
      return;
    }

    question.questionName = questionNameWd.data!;
    question.questionAnswer = answer;
    question.questionTip = questionTipWd.data ?? '';
    question.analysis = analysisWd.data ?? '';
    question.questionA = questionAWd.data ?? '';
    question.questionB = questionBWd.data ?? '';
    question.questionC = questionCWd.data ?? '';
    question.questionD = questionDWd.data ?? '';

    // 保存题目
    _viewModel.saveQuestion(question).then((success) {
      if (success) {
        Toast.show(widget.question == null ? '添加成功' : '修改成功');
        Navigator.pop(context, 'refresh');
      } else {
        Toast.show('操作失败，请重试');
      }
    });
  }
}
