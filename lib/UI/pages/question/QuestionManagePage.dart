import 'package:flutter/material.dart';
import 'package:ent_secutity_app/model/question_info.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:ent_secutity_app/viewmodel/question/QuestionListViewModel.dart';
import 'QuestionSearchPage.dart';
import 'QuestionDetailPage.dart';
import 'QuestionEditPage.dart';

class QuestionManagePage extends StatefulWidget {
  QuestionManagePage({Key? key}) : super(key: key);

  @override
  State<QuestionManagePage> createState() => _QuestionManagePageState();
}

class _QuestionManagePageState extends BasePageState<QuestionManagePage> {
  QuestionListViewModel _listModel = QuestionListViewModel();
  String? searchQuestionName;

  @override
  void initState() {
    super.initState();
    _listModel = QuestionListViewModel();
  }

  @override
  String strTitle() => '题目管理';

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
            child: CustomItemListWidget(
          model: _listModel,
          itemBuilder: itemBuilder,
          enablePullDown: true,
          enablePullUp: true,
          showSeparator: false,
        )),
      ],
    );
  }

  @override
  List<Widget> createActions(BuildContext context) => [
        IconButton(
          icon: Icon(Icons.search),
          tooltip: '题目搜索',
          onPressed: () async {
            final result = await WU.nextPage(context, QuestionSearchPage());
            if (result == null) return;
            Map data = result;
            searchQuestionName = data['questionName'];
            _listModel.questionName = searchQuestionName;
            _listModel.refresh();
          },
        ),
      ];

  @override
  Widget? createFloatingActionButton(BuildContext context) =>
      FloatingActionButton(
        onPressed: () async {
          final result = await WU.nextPage(context, QuestionEditPage());
          if (result == 'refresh') {
            _listModel.refresh();
          }
        },
        child: Icon(Icons.add),
        tooltip: '添加题目',
      );

  Widget itemBuilder(BuildContext context, item, int index, bool isEnd) {
    final question = QuestionInfo.fromJson(item);

    return CustomCard(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题目名称
          Text(
            question.questionName,
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: 8),
          // 选项 A-D
          ...[
            {'label': 'A', 'value': question.questionA},
            {'label': 'B', 'value': question.questionB},
            {'label': 'C', 'value': question.questionC},
            {'label': 'D', 'value': question.questionD}
          ]
              .where((e) => (e['value'] ?? '').toString().trim().isNotEmpty)
              .map<Widget>((e) {
            return Padding(
              padding: EdgeInsets.only(bottom: 4),
              child: Text(
                '${e['label']}. ${e['value']}',
                style: TextStyle(fontSize: 14, color: Colors.black87),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          SizedBox(height: 8),
          Divider(height: 1),
          SizedBox(height: 8),
          // 操作按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ContentButton(
                name: '编辑',
                minSize: Size(72, 32),
                onPressed: () async {
                  final result = await WU.nextPage(
                      context, QuestionEditPage(question: question));
                  if (result == 'refresh') {
                    _listModel.refresh();
                  }
                },
              ),
              SizedBox(width: 24),
              ContentButton(
                name: '删除',
                minSize: Size(72, 32),
                type: 'grey',
                onPressed: () {
                  SDialog.beSure(
                    context,
                    content: '确定要删除该题目吗？',
                    onBeSure: () async {
                      final success =
                          await _listModel.deleteQuestion(question.id);
                      if (success) {
                        Toast.show('删除成功');
                        _listModel.refresh();
                      } else {
                        Toast.show('删除失败，请重试');
                      }
                    },
                  );
                },
              ),
            ],
          ),
        ],
      ),
      onTap: () {
        WU.nextPage(context, QuestionDetailPage(question: question));
      },
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      radius: 10,
      shadow: CustomCard.defShadow,
    );
  }
}
