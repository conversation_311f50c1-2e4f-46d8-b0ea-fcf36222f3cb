import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';

class QuestionSearchPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return QuestionSearchPageState();
  }
}

class QuestionSearchPageState extends BasePageState<QuestionSearchPage> {
  WidgetData<String> questionName = WidgetData<String>();

  @override
  String strTitle() => '搜索';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(
            children: [
              InputTile(title: '题目名称', hint: '请输入题目名称', wd: questionName),
            ],
            isSingle: true,
          ),
          SizedBox(height: 10),
          TileButton(name: "搜索", onPressed: onSubmit),
          SizedBox(height: 12)
        ],
      ),
    );
  }

  onSubmit() {
    var info = {
      "questionName": questionName.data != null ? questionName.data : "",
    };
    Navigator.pop(context, info);
  }
}
