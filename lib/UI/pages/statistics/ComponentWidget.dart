import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:ent_secutity_app/dnc/widget/base/RequestStateProvider.dart';
import 'package:flutter/material.dart';

abstract class ComponentWidgetState<T extends StatefulWidget>
    extends BaseStatefulWidgetState<T> with RequestStateProvider {
  String get title;

  List<String> get commands => [];

  String? currentCommand;

  Widget component(
      BuildContext context, dynamic contentData, String? commandId);

  @override
  void initState() {
    print('commands: $commands');
    if (commands.isNotEmpty) {
      currentCommand = commands.first;
    }
    super.initState();
  }

  @override
  Widget content(BuildContext context, dynamic contentData) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          Container(
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 20,
                  color: Color(0xff2880FC),
                ),
                SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                      fontSize: 16,
                      color: Color(0xff2880FC),
                      fontWeight: FontWeight.w600),
                ),
                Spacer(),
                commands.isNotEmpty
                    ? Row(
                        children: commands.map((command) {
                          return InkWell(
                            child: Container(
                              margin: EdgeInsets.fromLTRB(8, 0, 0, 0),
                              height: 24,
                              width: 56,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(4)),
                                  color: currentCommand == command
                                      ? Color(0xff3B90FD)
                                      : Colors.white,
                                  border: Border.all(
                                      color: Color(0xff3B90FD),
                                      width: 1.0,
                                      style: BorderStyle.solid)),
                              child: Text(
                                command,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: currentCommand == command
                                      ? Colors.white
                                      : Color(0xff3B90FD),
                                ),
                              ),
                            ),
                            onTap: () {
                              setState(() {
                                currentCommand = command;
                              });
                            },
                          );
                        }).toList(),
                      )
                    : SizedBox(),
              ],
            ),
          ),
          SizedBox(height: 8),
          component(context, contentData, currentCommand),
        ],
      ),
    );
  }
}
