import 'package:ent_secutity_app/UI/pages/statistics/components/RiskControlHazardInvestigationWidget.dart';
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/base/RequestStateProvider.dart';
import 'package:flutter/material.dart';

import 'components/ContingencyManagementWidget.dart';
import 'components/DualSystemScoreWidget.dart';
import 'components/EducateTrainListWidget.dart';
import 'components/EducateTrainWidget.dart';
import 'components/MonthDataWidget.dart';
import 'components/MonthTotalDataWidget.dart';
import 'components/QuarterDataWidget.dart';
import 'components/SafetyHazardWidget.dart';
import 'components/SafetyInputWidget.dart';
import 'components/SafetyMeetingWidget.dart';
import 'components/SafetyOrganizationAndEquipmentWidget.dart';
import 'components/SafetyProductionInsuranceWidget.dart';
import 'components/TodayDataWidget.dart';
import 'components/WeekDataWidget.dart';
import 'components/WeekHazardDataWidget.dart';

class StatisticsHomePage extends StatefulWidget {
  final String id;
  final String viewSubEntpriseFlag;

  const StatisticsHomePage({
    super.key,
    required this.id,
    required this.viewSubEntpriseFlag,
  });

  @override
  State<StatefulWidget> createState() => StatisticsHomePageState();
}

class StatisticsHomePageState extends BasePageState<StatisticsHomePage>
    with RequestStateProvider {
  @override
  String strTitle() => '统计分析';

  List<dynamic> componentDataList = [];

  @override
  List<Request>? onCreateRequest() {
    return [
      Request(RequestType.GET, '/qyaq/dp/leaderView/get',
          params: () => {
                'id': widget.id,
              },
          interceptor: (result) {
            componentDataList = DU.safeList(result, ['stencilJson']);
            setState(() {});
          }),
    ];
  }

  @override
  Widget content(BuildContext context, contentData) {
    return SingleChildScrollView(
      child: Column(
        children: components(),
      ),
    );
  }

  List<Widget> components() {
    List<Widget> components = [];
    for (dynamic component in componentDataList) {
      final widget = getComponent(component);
      if (widget != null) {
        components.add(widget);
      }
    }
    print(components);
    return components;
  }

  /*
  不支持组件如下:
  本周隐患整改 weekDangerReorganizes
  本周危险作业 weekHazardousOperations
  动态库存 dynamicInventory (烟花爆竹专用)
   */
  Widget? getComponent(dynamic component) {
    switch (DU.safeString(component, ['componentCode'])) {
      case 'safetyHazard':
        return SafetyHazardWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'riskControlHazardInvestigation':
        return RiskControlHazardInvestigationWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'safetyProductionInsurance':
        return SafetyProductionInsuranceWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );

      case 'weekHazardData':
        return WeekHazardDataWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'weekData':
        return WeekDataWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );

      case 'safetyMeeting':
        return SafetyMeetingWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'safetyOrganizationAndEquipment':
        return SafetyOrganizationAndEquipmentWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'todayData':
        return TodayDataWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'dualSystemScore':
        return DualSystemScoreWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'safetyInput':
        return SafetyInputWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'contingencyManagement':
        return ContingencyManagementWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'monthTotalData':
        return MonthTotalDataWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'monthData':
        return MonthDataWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'quarterData':
        return QuarterDataWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'educateTrain':
        return EducateTrainWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );
      case 'educateTrainList':
        return EducateTrainListWidget(
          viewSubEntpriseFlag: widget.viewSubEntpriseFlag,
          title: DU.safeString(component, ['name']),
        );

      default:
        return null;
    }
  }
}
