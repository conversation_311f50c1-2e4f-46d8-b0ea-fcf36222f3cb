import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../common/global.dart';
import '../../../dnc/utils/DataUtils.dart';

class StatisticsUtils {
  Request request({
    required String tag,
    required String url,
    required String flag,
    Map<String, dynamic>? params,
    dynamic Function(dynamic result)? interceptor,
  }) {
    return Request(
      RequestType.GET,
      url,
      tag: tag,
      needEntId: false,
      params: () {
        if (params != null) {
          if (flag == '1') {
            params['leftEntId'] = Global.entId;
          } else {
            params['entId'] = Global.entId;
          }
          return params;
        }
        final data = {
          'startTime': DU.formatDate(DU.getMonthStart(offside: 0), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        };
        if (flag == '1') {
          data['leftEntId'] = Global.entId;
        } else {
          data['entId'] = Global.entId;
        }
        return data;
      },
      interceptor: interceptor,
    );
  }

  Widget cartesianChartBuilder(
    BuildContext context, {
    required List data,
    required String yName,
    required ChartValueMapper<dynamic, String> xValueMapper,
    required ChartValueMapper<dynamic, num> yValueMapper,
    double? interval,
    double? maximum,
    bool lineStyle = true,
  }) {
    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(4),
            child: Text(
              yName,
              style: TextStyle(
                fontSize: 12,
                color: Color(0xff4f8bca),
              ),
            ),
          ),
          SizedBox(
            height: 228,
            child: SfCartesianChart(
              primaryXAxis: CategoryAxis(
                isVisible: true,
                opposedPosition: false,
                isInversed: false,
                majorGridLines: MajorGridLines(color: Colors.transparent),
                majorTickLines: MajorTickLines(size: 0),
              ),
              primaryYAxis: NumericAxis(maximum: maximum, interval: interval),
              selectionType: SelectionType.point,
              isTransposed: false,
              selectionGesture: ActivationMode.singleTap,
              trackballBehavior: TrackballBehavior(
                lineColor: Color(0xff4f8bca),
                lineType: TrackballLineType.horizontal,
                activationMode: ActivationMode.singleTap,
                enable: true,
                tooltipAlignment: ChartAlignment.near,
                tooltipSettings: InteractiveTooltip(
                  color: Color(0x203B93F5),
                  borderColor: Color(0xff4f8bca),
                  borderRadius: 4,
                  borderWidth: 1,
                  textStyle: TextStyle(
                    fontSize: 12,
                    color: Color(0xff4F8BCA),
                  ),
                ),
                shouldAlwaysShow: true,
                tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
              ),
              series: lineStyle
                  ? <ChartSeries<dynamic, String>>[
                      AreaSeries<dynamic, String>(
                        isVisibleInLegend: false,
                        dataSource: data,
                        xValueMapper: xValueMapper,
                        yValueMapper: yValueMapper,
                        borderWidth: 1,
                        borderColor: Color(0xff3B93F5),
                        markerSettings: MarkerSettings(
                          isVisible: true,
                          height: 8,
                          width: 8,
                          color: Color(0xff3B93F5),
                          shape: DataMarkerType.circle,
                        ),
                        color: Color(0x203B93F5),
                      ),
                    ]
                  : <ChartSeries<dynamic, String>>[
                      ColumnSeries<dynamic, String>(
                        isVisibleInLegend: false,
                        dataSource: data,
                        xValueMapper: xValueMapper,
                        yValueMapper: yValueMapper,
                        borderWidth: 1,
                        borderColor: Color(0xff3B93F5),
                        markerSettings: MarkerSettings(
                          isVisible: true,
                          height: 8,
                          width: 8,
                          color: Color(0xff3B93F5),
                          shape: DataMarkerType.circle,
                        ),
                        color: Color(0x203B93F5),
                      ),
                    ],
            ),
          ),
        ],
      ),
    );
  }

  Widget circularChartBuilder(
    BuildContext context, {
    required List data,
    required ChartValueMapper<dynamic, String> xValueMapper,
    required ChartValueMapper<dynamic, num> yValueMapper,
    double? interval,
    double? maximum,
    bool lineStyle = true,
  }) {
    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 228,
            child: SfCircularChart(
              enableMultiSelection: true,
              margin: EdgeInsets.zero,
              // 显示图例，position显示所在位置
              legend: Legend(
                isVisible: true,
                position: LegendPosition.right,
                alignment: ChartAlignment.far,
                width: '${WU.getScreenWidth(context) / 3}',
              ),
              series: <CircularSeries>[
                DoughnutSeries<dynamic, String>(
                  explode: true,
                  explodeOffset: '8',
                  dataSource: data,
                  // pointColorMapper: (data, _) {
                  //   return data['color'];
                  // },
                  xValueMapper: (data, _) {
                    return data['name'];
                  },
                  yValueMapper: (data, _) {
                    return data['value'];
                  },

                  dataLabelSettings: DataLabelSettings(
                      isVisible: true, margin: EdgeInsets.zero),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget rankBuilder(
    BuildContext context, {
    required List data,
    required List<int> flexes,
    required List<String> titles,
    required String Function(dynamic item, int raw) rawValue,
  }) {
    return Container(
      height: 288,
      padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                color: Color(0xff82AFFE),
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(4), topRight: Radius.circular(4)),
              ),
              child: Row(
                children: flexes.asMap().keys.map((index) {
                  return Flexible(
                    flex: flexes[index],
                    child: Container(
                      alignment: Alignment.center,
                      child: Text(
                        titles[index],
                        style: TextStyle(
                          fontSize: 15,
                          color: ColorsI.white,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
            ...data.asMap().keys.map((columnIndex) {
              return Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        ...flexes.asMap().keys.map((rawIndex) {
                          if (rawIndex == 0) {
                            return Flexible(
                              flex: flexes[rawIndex],
                              child: Container(
                                alignment: Alignment.center,
                                child: columnIndex < 3
                                    ? Builder(builder: (context) {
                                        String icon =
                                            'assets/images/home/<USER>/home_statistics_rank_1.png';
                                        switch (columnIndex) {
                                          case 1:
                                            icon =
                                                'assets/images/home/<USER>/home_statistics_rank_2.png';
                                            break;
                                          case 2:
                                            icon =
                                                'assets/images/home/<USER>/home_statistics_rank_3.png';
                                            break;
                                        }
                                        return Image.asset(
                                          icon,
                                          scale: 2,
                                        );
                                      })
                                    : Text(
                                        '${rawIndex + 1}',
                                        style: TextStyle(
                                          fontSize: 16,
                                          color: ColorsI.blue,
                                        ),
                                      ),
                              ),
                            );
                          } else {
                            return Flexible(
                              flex: flexes[rawIndex],
                              child: Container(
                                alignment: Alignment.center,
                                child: Text(
                                  rawValue(data[columnIndex], rawIndex),
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: ColorsI.black66,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                            );
                          }
                        }),
                      ],
                    ),
                  ),
                  Divider(
                    color: Color(0xffE8E8E8),
                    thickness: 1,
                    height: 1,
                    indent: 8,
                    endIndent: 8,
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget cardWidgetStyle1(
    BuildContext context, {
    required List<Map> row,
  }) {
    return Container(
      height: 128,
      child: Row(
        children: row.map((data) {
          return Flexible(
            flex: 1,
            child: Container(
              margin: EdgeInsets.all(2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(4)),
                color: ColorsI.blue66,
              ),
              alignment: Alignment.center,
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Flexible(
                    fit: FlexFit.tight,
                    flex: 5,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          DU.safeString(data, ['value']),
                          style: TextStyle(
                              fontSize: 16,
                              color: ColorsI.white,
                              fontWeight: FontWeight.w600),
                        ),
                        Text(
                          DU.safeString(data, ['unit']),
                          style: TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                  Flexible(
                    fit: FlexFit.tight,
                    flex: 3,
                    child: Text(
                      DU.safeString(data, ['name']),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      style: TextStyle(
                        fontSize: 12,
                        color: ColorsI.white,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget itemCard1({required List<Map> info}) {
    return Container(
      margin: EdgeInsets.only(top: 8),
      height: 64,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4)),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1), //阴影在X轴和Y轴上的偏移
            color: Color(0xffE8E8E8), //阴影颜色
            blurRadius: 1, //阴影程度
            spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
          ),
        ],
      ),
      alignment: Alignment.center,
      child: Row(
        children: [
          Flexible(
            child: card1Item(itemInfo: info[0]),
          ),
          VerticalDivider(
            color: Color(0xffE8E8E8),
            width: 1,
            indent: 8,
            endIndent: 8,
          ),
          Flexible(
            child: card1Item(itemInfo: info[1]),
          ),
          VerticalDivider(
            color: Color(0xffE8E8E8),
            width: 1,
            indent: 8,
            endIndent: 8,
          ),
          Flexible(
            child: card1Item(itemInfo: info[2]),
          ),
        ],
      ),
    );
  }

  Widget card1Item({required Map itemInfo}) {
    return Container(
      alignment: Alignment.center,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            child: Text(
              DU.safeString(itemInfo, ['title']),
              style: TextStyle(
                  fontSize: 14,
                  color: DU.safeValue<Color>(
                      itemInfo, ['titleColor'], Colors.black87),
                  fontWeight: FontWeight.w400),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  DU.safeString(itemInfo, ['info']),
                  style: TextStyle(
                      fontSize: 16,
                      color: DU.safeValue<Color>(
                          itemInfo, ['infoColor'], Colors.black87),
                      fontWeight: FontWeight.w600),
                ),
                Text(
                  DU.safeString(itemInfo, ['unit']),
                  style: TextStyle(
                      fontSize: 12,
                      color: DU.safeValue<Color>(
                          itemInfo, ['infoColor'], Colors.black87),
                      fontWeight: FontWeight.w400),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget itemCard2({required List<Map> info}) {
    return Container(
      child: Row(
        children: [
          Flexible(child: card2Item(itemInfo: info[0])),
          SizedBox(width: 8),
          Flexible(child: card2Item(itemInfo: info[1])),
        ],
      ),
    );
  }

  Widget card2Item({required Map itemInfo}) {
    return Container(
      margin: EdgeInsets.only(top: 8),
      height: 64,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4)),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1), //阴影在X轴和Y轴上的偏移
            color: Color(0xffE8E8E8), //阴影颜色
            blurRadius: 1, //阴影程度
            spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
          ),
        ],
      ),
      child: Stack(
        children: [
          Positioned(
            child: Container(
              padding: EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    child: Row(
                      children: [
                        Image.asset(
                          DU.safeString(itemInfo, ['icon']),
                          width: 18,
                          height: 18,
                        ),
                        SizedBox(width: 8),
                        Text(
                          DU.safeString(itemInfo, ['title']),
                          style: TextStyle(
                              fontSize: 14,
                              color: DU.safeValue<Color>(
                                  itemInfo, ['titleColor'], Colors.black87),
                              fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          DU.safeString(itemInfo, ['info']),
                          style: TextStyle(
                              fontSize: 16,
                              color: DU.safeValue<Color>(
                                  itemInfo, ['infoColor'], Colors.black87),
                              fontWeight: FontWeight.w600),
                        ),
                        Text(
                          DU.safeString(itemInfo, ['unit']),
                          style: TextStyle(
                              fontSize: 12,
                              color: DU.safeValue<Color>(
                                  itemInfo, ['infoColor'], Colors.black87),
                              fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(
            bottom: -16,
            right: -16,
            child: Container(
              width: 64,
              height: 64,
              child: Opacity(
                opacity: 0.1,
                child: Image.asset(
                  DU.safeString(itemInfo, ['icon']),
                  fit: BoxFit.fill,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
