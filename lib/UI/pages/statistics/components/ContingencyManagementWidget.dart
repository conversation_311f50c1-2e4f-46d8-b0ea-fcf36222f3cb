import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///应急管理
class ContingencyManagementWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const ContingencyManagementWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => ContingencyManagementWidgetState();
}

class ContingencyManagementWidgetState
    extends ComponentWidgetState<ContingencyManagementWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'emergencyCount',
        url: '/qb/report/emergencyCount',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getYearStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        },
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final data = contentData['emergencyCount'];
    print(data);
    return SizedBox(
      height: 128,
      width: 360,
      child: Stack(
        children: [
          Image.asset(
            "assets/images/home/<USER>/home_statistics_emergency.png",
            fit: BoxFit.fitWidth,
            height: 128,
            width: 360,
          ),
          Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Container(),
                    ),
                    Expanded(
                      flex: 2,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '应急预案',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            DU.safeString(data, ['planCount']),
                            style: TextStyle(
                                fontSize: 16,
                                color: ColorsI.blue66,
                                fontWeight: FontWeight.w600),
                          ),
                          Text(
                            '项',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Container(),
                    ),
                    Expanded(
                      flex: 2,
                      child:  Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '应急物资',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            DU.safeString(data, ['resourceCount'], '0'),
                            style: TextStyle(
                                fontSize: 16,
                                color: ColorsI.blue66,
                                fontWeight: FontWeight.w600),
                          ),
                          Text(
                            '项',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(),
              ),
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      flex: 2,
                      child: Container(),
                    ),
                    Expanded(
                      flex: 2,
                      child:Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '应急演练',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            DU.safeString(data, ['drillCount']),
                            style: TextStyle(
                                fontSize: 16,
                                color: ColorsI.blue66,
                                fontWeight: FontWeight.w600),
                          ),
                          Text(
                            '次',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Container(),
                    ),
                    Expanded(
                      flex: 2,
                      child:  Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '事故数量',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            DU.safeString(data, ['sgCount']),
                            style: TextStyle(
                                fontSize: 16,
                                color: ColorsI.blue66,
                                fontWeight: FontWeight.w600),
                          ),
                          Text(
                            '件',
                            style: TextStyle(
                                fontSize: 12,
                                color: ColorsI.black66,
                                fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Positioned(
          //   top: 29,
          //   left: 6,
          //   child: Container(
          //     color: Colors.teal,
          //     width: 110,
          //     height: 26,
          //     alignment: Alignment.center,
          //     child: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         Text(
          //           '应急预案',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //         Text(
          //           DU.safeString(data, ['planCount']),
          //           style: TextStyle(
          //               fontSize: 16,
          //               color: ColorsI.blue66,
          //               fontWeight: FontWeight.w600),
          //         ),
          //         Text(
          //           '项',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
          // Positioned(
          //   top: 29,
          //   right: 6,
          //   child: Container(
          //     width: 110,
          //     height: 26,
          //     alignment: Alignment.center,
          //     child: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         Text(
          //           '应急演练',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //         Text(
          //           DU.safeString(data, ['drillCount']),
          //           style: TextStyle(
          //               fontSize: 16,
          //               color: ColorsI.blue66,
          //               fontWeight: FontWeight.w600),
          //         ),
          //         Text(
          //           '次',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
          // Positioned(
          //   bottom: 29,
          //   left: 6,
          //   child: Container(
          //     width: 110,
          //     height: 26,
          //     alignment: Alignment.center,
          //     child: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         Text(
          //           '应急物资',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //         Text(
          //           DU.safeString(data, ['resourceCount'], '0'),
          //           style: TextStyle(
          //               fontSize: 16,
          //               color: ColorsI.blue66,
          //               fontWeight: FontWeight.w600),
          //         ),
          //         Text(
          //           '项',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
          // Positioned(
          //   bottom: 29,
          //   right: 6,
          //   child: Container(
          //     width: 110,
          //     height: 26,
          //     alignment: Alignment.center,
          //     child: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         Text(
          //           '事故数量',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //         Text(
          //           DU.safeString(data, ['sgCount']),
          //           style: TextStyle(
          //               fontSize: 16,
          //               color: ColorsI.blue66,
          //               fontWeight: FontWeight.w600),
          //         ),
          //         Text(
          //           '件',
          //           style: TextStyle(
          //               fontSize: 12,
          //               color: ColorsI.black66,
          //               fontWeight: FontWeight.w400),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
