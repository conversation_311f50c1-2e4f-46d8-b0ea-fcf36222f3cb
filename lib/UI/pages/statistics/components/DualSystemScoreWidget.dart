import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///双体系得分
class DualSystemScoreWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const DualSystemScoreWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => DualSystemScoreWidgetState();
}

class DualSystemScoreWidgetState
    extends ComponentWidgetState<DualSystemScoreWidget> {
  @override
  String get title => widget.title;

  List<String> get commands =>
      widget.viewSubEntpriseFlag == '0' ? ['年度'] : ['年度', '公司排名'];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'group_single',
        url: '/qb/report/group_single',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getYearStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        },
      ),
      StatisticsUtils().request(
        tag: 'group_singleTable',
        url: '/qb/report/group_singleTable',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getMonthStart(offside: -1), null),
          'endTime': DU.formatDate(DU.getMonthEnd(offside: -1), null),
        },
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    switch (name) {
      case '年度':
        return StatisticsUtils().cartesianChartBuilder(
          context,
          data: DU.safeList(contentData, ['group_single']),
          yName: '单位:分',
          xValueMapper: (data, _) {
            final month = DU.safeString(data, ['month']);
            return month;
          },
          yValueMapper: (data, _) {
            return DU.safeDouble(data, ['score'], 0);
          },
          interval: 5,
        );
      case '公司排名':
        return StatisticsUtils().rankBuilder(context,
            data: DU.safeList(contentData, ['group_singleTable']),
            flexes: [1, 3, 2],
            titles: ['排名', '公司名称', '双体系得分'], rawValue: (dynamic item, int raw) {
          switch (raw) {
            case 1:
              return DU.safeString(item, ['name']);
            case 2:
              return DU.safeString(item, ['score']);
          }
          return '';
        });
      default:
        return Container();
    }
  }
}
