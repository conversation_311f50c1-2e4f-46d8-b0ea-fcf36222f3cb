import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/Colors.dart';
import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///教育培训-列表
class EducateTrainListWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const EducateTrainListWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => EducateTrainListWidgetState();
}

class EducateTrainListWidgetState
    extends ComponentWidgetState<EducateTrainListWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'getEduAndTrainRecord',
        url: '/qb/report/getEduAndTrainRecord',
        params: {
          'startTime': DU.formatDate(DU.getYearStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        },
        flag: widget.viewSubEntpriseFlag,
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final data = contentData['getEduAndTrainRecord'];
    return SizedBox(
      height: 228,
      child: Column(
        children: [
          Flexible(
            fit: FlexFit.tight,
            flex: 1,
            child: Row(
              children: [
                recordCardBuilder(
                  context,
                  1,
                  '教育培训计划',
                  DU.safeString(data, ['planCount']),
                  '项',
                ),
                recordCardBuilder(
                  context,
                  2,
                  '课程数量',
                  DU.safeString(data, ['classCount']),
                  '项',
                ),
              ],
            ),
          ),
          Flexible(
            fit: FlexFit.tight,
            flex: 1,
            child: Row(
              children: [
                recordCardBuilder(
                  context,
                  3,
                  '培训人次',
                  DU.safeString(data, ['trainPersonCount']),
                  '人次',
                ),
                recordCardBuilder(
                  context,
                  4,
                  '培训时长',
                  DU.safeString(data, ['trainDuration']),
                  '小时',
                ),
              ],
            ),
          ),
          Flexible(
            fit: FlexFit.tight,
            flex: 1,
            child: Row(
              children: [
                recordCardBuilder(
                  context,
                  5,
                  '安全题库',
                  DU.safeString(data, ['safeQuestionCount']),
                  '题',
                ),
                recordCardBuilder(
                  context,
                  6,
                  '考试人次',
                  DU.safeString(data, ['examCount']),
                  '人次',
                ),
              ],
            ),
          ),
          Flexible(
            fit: FlexFit.tight,
            flex: 1,
            child: Row(
              children: [
                recordCardBuilder(
                  context,
                  7,
                  '安全资格证',
                  DU.safeString(data, ['safeCertificateCount']),
                  '个',
                ),
                recordCardBuilder(
                  context,
                  8,
                  '特种作业资格证',
                  DU.safeString(data, ['specialOperCount']),
                  '个',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget recordCardBuilder(BuildContext context, int iconIndex, String name,
      dynamic value, String unit) {
    final color = Color(0xffffffff);
    return Flexible(
      fit: FlexFit.tight,
      flex: 1,
      child: Container(
        margin: EdgeInsets.all(2),
        padding: EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4)),
          color: ColorsI.blue66,
        ),
        alignment: Alignment.center,
        child: Row(
          children: [
            Image.asset(
              'assets/images/home/<USER>/home_statistics_educate_$iconIndex.png',
              width: 20,
              height: 20,
            ),
            SizedBox(width: 4),
            Expanded(
              child: FittedBox(
                alignment: Alignment.centerLeft,
                fit: BoxFit.scaleDown,
                child: Row(
                  children: [
                    Text(
                      name,
                      style: TextStyle(
                        fontSize: 12,
                        color: color,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 4),
                    Text(
                      value.toString(),
                      style: TextStyle(
                          fontSize: 16,
                          color: ColorsI.white,
                          fontWeight: FontWeight.w600),
                    ),
                    Text(
                      unit,
                      style: TextStyle(
                          fontSize: 12,
                          color: color,
                          fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
