import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///教育培训
class EducateTrainWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const EducateTrainWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => EducateTrainWidgetState();
}

class EducateTrainWidgetState extends ComponentWidgetState<EducateTrainWidget> {
  @override
  String get title => widget.title;
  int currentEducationIndex = -1;
  StateSetter? _educationAreaChartSetter;

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'ksCount',
        url: '/qb/report/ksCount',
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
        flag: widget.viewSubEntpriseFlag,
      ),
      StatisticsUtils().request(
          tag: 'answerPeopleCount',
          url: '/qb/report/answerPeopleCount',
          interceptor: (result) =>
              result is List && result.isNotEmpty ? result.first : {},
          flag: widget.viewSubEntpriseFlag),
      StatisticsUtils().request(
          tag: 'aqTrainPolyline',
          url: '/qb/report/aqTrainPolyline',
          params: {
            'startTime': DU.formatDate(DU.getYearStart(), null),
            'endTime': DU.formatDate(DateTime.now(), null),
          },
          flag: widget.viewSubEntpriseFlag),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final ksCount = contentData['ksCount'];
    final answerPeopleCount = contentData['answerPeopleCount'];
    final ksrsCount = DU.safeInt(answerPeopleCount, ['ksrsCount'], 0);
    final hegeCount = DU.safeInt(answerPeopleCount, ['hegeCount'], 0);
    final ratio =
        (ksrsCount != 0 ? hegeCount * 100.0 / ksrsCount : 0).toStringAsFixed(2);
    return Column(
      children: [
        StatisticsUtils().itemCard1(info: [
          {
            'title': '本月考试',
            'info': DU.safeString(ksCount, ['ks'], '0'),
            'unit': '次'
          },
          {
            'title': '答题人次',
            'info': '$ksrsCount',
            'unit': '人',
          },
          {
            'title': '答题合格率',
            'info': ratio,
            'unit': '%',
            'infoColor': Color(0xff287DF2),
          },
        ]),
        educationLineChart(contentData['aqTrainPolyline']),
        educationAreaChart(contentData['aqTrainPolyline']),
      ],
    );
  }

  Widget educationLineChart(dynamic educationData3) {
    return Container(
      padding: EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.fromLTRB(0, 4, 0, 4),
            child: Text(
              '答题人数',
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.all(4),
            child: Text(
              '单位:人',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xff4f8bca),
              ),
            ),
          ),
          SizedBox(
            height: 196,
            child: SfCartesianChart(
              primaryXAxis: CategoryAxis(
                isVisible: true,
                opposedPosition: false,
                isInversed: false,
                majorGridLines: MajorGridLines(color: Colors.transparent),
                majorTickLines: MajorTickLines(size: 0),
              ),
              selectionType: SelectionType.point,
              isTransposed: false,
              selectionGesture: ActivationMode.singleTap,
              trackballBehavior: TrackballBehavior(
                lineColor: Color(0xff4f8bca),
                lineType: TrackballLineType.horizontal,
                activationMode: ActivationMode.singleTap,
                enable: true,
                tooltipAlignment: ChartAlignment.near,
                tooltipSettings: InteractiveTooltip(
                  color: Color(0x203B93F5),
                  borderColor: Color(0xff4f8bca),
                  borderRadius: 4,
                  borderWidth: 1,
                  textStyle: TextStyle(
                    fontSize: 12,
                    color: Color(0xff4F8BCA),
                  ),
                ),
                shouldAlwaysShow: true,
                tooltipDisplayMode: TrackballDisplayMode.groupAllPoints,
              ),
              series: <ChartSeries<dynamic, String>>[
                AreaSeries<dynamic, String>(
                  isVisibleInLegend: false,
                  dataSource: educationData3,
                  xValueMapper: (data, _) {
                    final monthCount = DU.safeString(data, ['monthCount'], '').split('-');
                    final month = '${monthCount.length == 2 ? monthCount[1] : 00}月';
                    return month;
                  },
                  yValueMapper: (data, _) {
                    // var rng = new Random();
                    // print(rng.nextInt(100));//
                    // return rng.nextInt(100);
                    return DU.safeValue<int>(data, ['count'], 0);
                  },
                  borderWidth: 1,
                  borderColor: Color(0xff3B93F5),
                  markerSettings: MarkerSettings(
                    isVisible: true,
                    height: 8,
                    width: 8,
                    color: Color(0xff3B93F5),
                    shape: DataMarkerType.circle,
                  ),
                  color: Color(0x203B93F5),
                ),
              ],
              onTrackballPositionChanging: (TrackballArgs arg) {
                // print('arg: ${arg.chartPointInfo.dataPointIndex}  $currentEducationIndex');
                final int index = arg.chartPointInfo.dataPointIndex ?? 0;
                if (_educationAreaChartSetter != null && currentEducationIndex != index) {
                  _educationAreaChartSetter!(() {
                    currentEducationIndex = index;
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }



  Widget educationAreaChart(dynamic educationData3) {
    return StatefulBuilder(
      builder: (BuildContext context, void Function(void Function()) setState) {
        _educationAreaChartSetter = setState;
        if (currentEducationIndex < 0) {
          return Container();
        }
        final data = educationData3[currentEducationIndex];

        var total = DU.safeValue<int>(data, ['mfCount'], 0) +
            DU.safeValue<int>(data, ['yxCount'], 0) +
            DU.safeValue<int>(data, ['lhCount'], 0) +
            DU.safeValue<int>(data, ['jgCount'], 0) +
            DU.safeValue<int>(data, ['bjgCount'], 0);
        total = total == 0 ? 1 : total;
        final pieData = [
          {
            'name': '满分',
            'value': DU.safeValue<int>(data, ['mfCount'], 0),
            'rate': DU.safeValue<int>(data, ['mfCount'], 0) / total * 1.0,
            'color': Color(0xff71DBB5),
          },
          {
            'name': '优秀',
            'value': DU.safeValue<int>(data, ['yxCount'], 0),
            'rate': DU.safeValue<int>(data, ['yxCount'], 0) / total * 1.0,
            'color': Color(0xff3593EB),
          },
          {
            'name': '良好',
            'value': DU.safeValue<int>(data, ['lhCount'], 0),
            'rate': DU.safeValue<int>(data, ['lhCount'], 0) / total * 1.0,
            'color': Color(0xff3565ED),
          },
          {
            'name': '及格',
            'value': DU.safeValue<int>(data, ['jgCount'], 0),
            'rate': DU.safeValue<int>(data, ['jgCount'], 0) / total * 1.0,
            'color': Color(0xff862FEA),
          },
          {
            'name': '不及格',
            'value': DU.safeValue<int>(data, ['bjgCount'], 0),
            'rate': DU.safeValue<int>(data, ['bjgCount'], 0) / total * 1.0,
            'color': Color(0xffFFC837),
          },
        ];
        return Container(
          padding: EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.fromLTRB(0, 4, 0, 4),
                child: Text(
                  '答题成绩',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              SizedBox(
                height: 196,
                child: SfCircularChart(
                  enableMultiSelection: true,
                  margin: EdgeInsets.zero,
                  // 显示图例，position显示所在位置
                  legend: Legend(
                      isVisible: true,
                      position: LegendPosition.right,
                      alignment: ChartAlignment.far,
                      legendItemBuilder: (String legendText, dynamic series, dynamic point, int seriesIndex) {
                        Color color = Color(0xff71DBB5);
                        switch (legendText) {
                          case '满分':
                            color = Color(0xff71DBB5);
                            break;
                          case '优秀':
                            color = Color(0xff3593EB);
                            break;
                          case '良好':
                            color = Color(0xff3565ED);
                            break;
                          case '及格':
                            color = Color(0xff862FEA);
                            break;
                          case '不及格':
                            color = Color(0xffFFC837);
                            break;
                        }
                        final count = '${pieData[seriesIndex]['value']}人';
                        return Container(
                          padding: EdgeInsets.all(4),
                          width: 104,
                          child: Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: new BoxDecoration(
                                  color: color,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              Text(
                                legendText,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: color,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                              Spacer(),
                              Text(
                                count,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: color,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                  series: <CircularSeries>[
                    DoughnutSeries<dynamic, String>(
                      explode: true,
                      explodeOffset: '8',
                      dataSource: pieData,
                      pointColorMapper: (data, _) {
                        return data['color'];
                      },
                      xValueMapper: (data, _) {
                        return data['name'];
                      },
                      yValueMapper: (data, _) {
                        return data['value'];
                      },
                      dataLabelMapper: (data, _) {
                        return '${(data['rate'] * 100).toStringAsFixed(1)}%';
                      },
                      dataLabelSettings: DataLabelSettings(isVisible: true, margin: EdgeInsets.zero),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
