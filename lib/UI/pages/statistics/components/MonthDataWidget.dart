import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/material.dart';
import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///本月数据-统计
class MonthDataWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const MonthDataWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => MonthDataWidgetState();
}

class MonthDataWidgetState
    extends ComponentWidgetState<MonthDataWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'weekYH',
        url: '/qb/report/weekYH',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getWeekStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        },
        interceptor: (result) =>
        result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final weekData = contentData['weekYH'];
    final count = DU.safeValue<int>(weekData, ['count'], 0);
    final overdueCount = DU.safeValue<int>(weekData, ['overdueCount'], 0);
    final onTimeCount = DU.safeValue<int>(weekData, ['onTimeCount'], 0);
    final ratio =
    (count != 0 && onTimeCount != 0 ? (onTimeCount * 100.0 / count) : 0)
        .toStringAsFixed(2);
    return Column(
      children: [
        StatisticsUtils().itemCard1(info: [
          {'title': '隐患整改', 'info': '$count', 'unit': '条'},
          {
            'title': '已整改',
            'info': DU.safeString(weekData, ['finishCount'], '0'),
            'unit': '条',
            'titleColor': Color(0xff537BC0)
          },
          {
            'title': '未整改',
            'info': DU.safeString(weekData, ['undoneCount'], '0'),
            'unit': '条',
            'titleColor': Color(0xffEB7A7C)
          },
        ]),
        StatisticsUtils().itemCard2(info: [
          {
            'title': '逾期未整改',
            'info': '$overdueCount',
            'unit': '项',
            'icon': 'assets/images/home/<USER>/home_statistics_week_1.png'
          },
          {
            'title': '按时整改率',
            'info': ratio,
            'unit': '%',
            'icon': 'assets/images/home/<USER>/home_statistics_week_2.png'
          },
        ]),
      ],
    );
  }

}
