import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///本月数据-统计
class MonthTotalDataWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const MonthTotalDataWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => MonthTotalDataWidgetState();
}

class MonthTotalDataWidgetState
    extends ComponentWidgetState<MonthTotalDataWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'monthData',
        url: '/qb/report/monthData',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getMonthStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
          'monthNums': DateTime.now().month,
        },
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final monthData = contentData['monthData'];
    final yearBudget = DU.safeDouble(monthData, ['yearBudget']);
    final value = (yearBudget != 0
            ? (DU.safeDouble(monthData, ['jdBudget']) /
                DU.safeDouble(monthData, ['yearBudget']) *
                100)
            : 0)
        .toStringAsFixed(2);
    print(value);
    return Column(
      children: [
        monthItem(
            icon: 'assets/images/home/<USER>/home_statistics_month_1.png',
            name: '安全检查',
            value: DU.safeString(monthData, ['wcCount'], '0'),
            unit: '次'),
        monthItem(
            icon: 'assets/images/home/<USER>/home_statistics_month_2.png',
            name: '检查问题点',
            value: DU.safeString(monthData, ['wtdCount'], '0'),
            unit: '次'),
        monthItem(
            icon: 'assets/images/home/<USER>/home_statistics_month_3.png',
            name: '累计安全投入',
            value: DU.safeString(monthData, ['jdExpAmount'], '0'),
            unit: '元'),
        monthItem(
            icon: 'assets/images/home/<USER>/home_statistics_month_4.png',
            name: '全年预算占比',
            value: value,
            unit: '%'),
      ],
    );
  }

  Widget monthItem(
      {required String icon,
      required String name,
      required String value,
      required String unit}) {
    return Container(
      margin: EdgeInsets.only(top: 8),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4)),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: Offset(0, 1), //阴影在X轴和Y轴上的偏移
            color: Color(0xffE8E8E8), //阴影颜色
            blurRadius: 1, //阴影程度
            spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
          ),
        ],
      ),
      child: Row(
        children: [
          Image.asset(
            icon,
            width: 32,
            height: 32,
          ),
          SizedBox(width: 8),
          Text(
            name,
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w400,
            ),
          ),
          Spacer(),
          Text(
            value,
            style: TextStyle(
                fontSize: 16,
                color: Colors.black87,
                fontWeight: FontWeight.w600),
          ),
          Text(
            unit,
            style: TextStyle(
                fontSize: 12,
                color: Colors.black87,
                fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }
}
