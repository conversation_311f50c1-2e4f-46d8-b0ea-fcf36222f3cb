import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///风险管控与隐患排查
class RiskControlHazardInvestigationWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const RiskControlHazardInvestigationWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() =>
      RiskControlHazardInvestigationWidgetState();
}

class RiskControlHazardInvestigationWidgetState
    extends ComponentWidgetState<RiskControlHazardInvestigationWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'preventionCount',
        url: '/qb/report/preventionCount',
        flag: widget.viewSubEntpriseFlag,
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final data = contentData['preventionCount'];
    return StatisticsUtils().cardWidgetStyle1(
      context,
      row: [
        {
          'name': '风险点',
          'value': DU.safeString(data, ['fxdyCount'], '0'),
          'unit':'次',
        },
        {
          'name': '检查项目',
          'value': DU.safeString(data, ['fxdxCount'], '0'),
          'unit':'次',
        },
        {
          'name': '危险源',
          'value': DU.safeString(data, ['objectCount'], '0'),
          'unit':'次',
        },
        {
          'name': '管控措施',
          'value': DU.safeString(data, ['gkcsCount'], '0'),
          'unit':'次',
        },
        {
          'name': '隐患排查任务',
          'value': DU.safeString(data, ['xjtmCount'], '0'),
          'unit':'次',
        },
      ],
    );
  }
}
