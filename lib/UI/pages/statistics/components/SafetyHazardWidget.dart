import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///安全隐患
class SafetyHazardWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const SafetyHazardWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => SafetyHazardWidgetState();
}

class SafetyHazardWidgetState extends ComponentWidgetState<SafetyHazardWidget> {
  @override
  String get title => widget.title;

  List<String> get commands =>
      widget.viewSubEntpriseFlag == '0' ? ['本月', '年度'] : ['本月', '年度', '公司排名'];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
          tag: 'aqyhRank',
          url: '/qb/report/aqyhRank',
          flag: widget.viewSubEntpriseFlag),
      StatisticsUtils().request(
        tag: 'aqyhMonth',
        url: '/qb/report/aqyhMonth',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getYearStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        },
      ),
      StatisticsUtils().request(
          tag: 'getReformTaskCount',
          url: '/qyaq/yhzg/zg/getReformTaskCount',
          flag: widget.viewSubEntpriseFlag),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    switch (name) {
      case '本月':
        return StatisticsUtils().cartesianChartBuilder(
          context,
          data: DU.safeList(contentData, ['getReformTaskCount']),
          yName: '隐患个数',
          xValueMapper: (data, _) {
            final week = '第' + DU.safeString(data, ['weekNo']) + '周';
            return week;
          },
          yValueMapper: (data, _) {
            return DU.safeInt(data, ['count'], 0);
          },
          interval: 1,
        );
      case '年度':
        return StatisticsUtils().cartesianChartBuilder(
          context,
          data: DU.safeList(contentData, ['aqyhMonth']),
          yName: '隐患个数',
          xValueMapper: (data, _) {
            final week = DU.safeString(data, ['monthNo']);
            return week;
          },
          yValueMapper: (data, _) {
            return DU.safeInt(data, ['count'], 0);
          },
        );
      case '公司排名':
        return StatisticsUtils().rankBuilder(context,
            data: DU.safeList(contentData, ['aqyhRank']),
            flexes: [1, 3, 2],
            titles: ['排名', '公司名称', '隐患排查数量'],
            rawValue: (dynamic item, int raw) {
          switch (raw) {
            case 1:
              return DU.safeString(item, ['name']);
            case 2:
              return DU.safeString(item, ['count']);
          }
          return '';
        });
      default:
        return Container();
    }
  }
}
