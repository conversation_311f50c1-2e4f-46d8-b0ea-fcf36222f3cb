import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///安全投入
class SafetyInputWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const SafetyInputWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => SSafetyInputWidgetState();
}

class SSafetyInputWidgetState extends ComponentWidgetState<SafetyInputWidget> {
  @override
  String get title => widget.title;

  List<String> get commands =>
      widget.viewSubEntpriseFlag == '0' ? ['年度投入', '年度占比'] : ['年度投入', '年度占比', '公司排名'];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
          tag: 'aqtrEntRank',
          url: '/qb/report/aqtrEntRank',
          params: {},
          flag: widget.viewSubEntpriseFlag),
      StatisticsUtils().request(
          tag: 'getExpAmountByMouth',
          url: '/qb/report/getExpAmountByMouth',
          params: {},
          flag: widget.viewSubEntpriseFlag),
      StatisticsUtils().request(
          tag: 'getAqtrPieChart',
          url: '/qb/report/getAqtrPieChart',
          params: {'businessType': 2},
          flag: widget.viewSubEntpriseFlag),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    switch (name) {
      case '年度投入':
        return StatisticsUtils().cartesianChartBuilder(
          context,
          data: DU.safeList(contentData, ['getExpAmountByMouth']),
          yName: '金额(元)',
          xValueMapper: (data, _) {
            final week = DU.safeString(data, ['mouthName']);
            return week;
          },
          yValueMapper: (data, _) {
            return data['expAmount'];
          },
          lineStyle: false,
          // interval: 1000,
        );
      case '年度占比':
        return StatisticsUtils().circularChartBuilder(
          context,
          data: DU.safeList(contentData, ['getAqtrPieChart']),
          xValueMapper: (data, _) {
            final week = DU.safeString(data, ['monthNo']);
            return week;
          },
          yValueMapper: (data, _) {
            return DU.safeInt(data, ['count'], 0);
          },
        );
      case '公司排名':
        return StatisticsUtils().rankBuilder(context,
            data: DU.safeList(contentData, ['aqtrEntRank']),
            flexes: [1, 3, 2],
            titles: ['排名', '公司名称', '金额(元)'], rawValue: (dynamic item, int raw) {
          print(item);
          switch (raw) {
            case 1:
              return DU.safeString(item, ['entName']);
            case 2:
              return DU.safeString(item, ['expAmount']);
          }
          return '';
        });
      default:
        return Container();
    }
  }
}
