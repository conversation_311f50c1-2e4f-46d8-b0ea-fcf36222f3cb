import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///安全会议
class SafetyMeetingWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const SafetyMeetingWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => SafetyMeetingWidgetState();
}

class SafetyMeetingWidgetState
    extends ComponentWidgetState<SafetyMeetingWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'getGrdaMeetingCount',
        url: '/qb/report/getGrdaMeetingCount',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getYearStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        },
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final data = contentData['getGrdaMeetingCount'];
    return StatisticsUtils().cardWidgetStyle1(
      context,
      row: [
        {
          'name': '会议总次数',
          'value': DU.safeString(data, ['total'], '0'),
          'unit': '次',
        },
        {
          'name': '开工第一课',
          'value': DU.safeString(data, ['diyikeCount'], '0'),
          'unit': '次',
        },
        {
          'name': '安全生产\n专题会议',
          'value': DU.safeString(data, ['aqscztCount'], '0'),
          'unit': '次',
        },
        {
          'name': '安全生产\n月度会议',
          'value': DU.safeString(data, ['aqscydCount'], '0'),
          'unit': '次',
        },
        {
          'name': '安全生产\n季度会议',
          'value': DU.safeString(data, ['aqscjdCount'], '0'),
          'unit': '次',
        },
      ],
    );
  }
}
