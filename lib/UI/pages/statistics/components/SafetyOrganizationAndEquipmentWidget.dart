import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///安全机构
class SafetyOrganizationAndEquipmentWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const SafetyOrganizationAndEquipmentWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() =>
      SafetyOrganizationAndEquipmentWidgetState();
}

class SafetyOrganizationAndEquipmentWidgetState
    extends ComponentWidgetState<SafetyOrganizationAndEquipmentWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'qajgzdCount',
        url: '/qb/report/qajgzdCount',
        params: {},
        flag: widget.viewSubEntpriseFlag,
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
      StatisticsUtils().request(
        tag: 'sbCount',
        url: '/qb/report/sbCount',
        flag: widget.viewSubEntpriseFlag,
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final data1 = contentData['qajgzdCount'];
    final data2 = contentData['sbCount'];
    return Row(
      children: [
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(4)),
              color: ColorsI.blue16,
            ),
            padding: EdgeInsets.all(2),
            child: Column(
              children: [
                Container(
                  child: Text(
                    '安全机构及制度',
                    style: TextStyle(
                        fontSize: 14,
                        color: ColorsI.blue66,
                        fontWeight: FontWeight.w400),
                  ),
                ),
                StatisticsUtils().cardWidgetStyle1(
                  context,
                  row: [
                    {
                      'name': '安全管理机构数',
                      'value': DU.safeString(data1, ['aqgljgsCount'], '0'),
                      'unit': '个',
                    },
                    {
                      'name': '机构人员总数',
                      'value': DU.safeString(data1, ['jgryzsCount'], '0'),
                      'unit': '人',
                    },
                    {
                      'name': '安全管理制度',
                      'value': DU.safeString(data1, ['zdCount'], '0'),
                      'unit': '项',
                    },
                  ],
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: 4),
        Flexible(
          flex: 1,
          fit: FlexFit.tight,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(4)),
              color: ColorsI.blue16,
            ),
            padding: EdgeInsets.all(2),
            child: Column(
              children: [
                Container(
                  child: Text(
                    '设备管理',
                    style: TextStyle(
                        fontSize: 14,
                        color: ColorsI.blue66,
                        fontWeight: FontWeight.w400),
                  ),
                ),
                StatisticsUtils().cardWidgetStyle1(
                  context,
                  row: [
                    {
                      'name': '设备数量',
                      'value': DU.safeString(data2, ['sbCount'], '0'),
                      'unit': '台',
                    },
                    {
                      'name': '保养任务',
                      'value': DU.safeString(data2, ['rwCount'], '0'),
                      'unit': '条',
                    },
                    {
                      'name': '超期未保养',
                      'value': DU.safeString(data2, ['cqCount'], '0'),
                      'unit': '条',
                    },
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
