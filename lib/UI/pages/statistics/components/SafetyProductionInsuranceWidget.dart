import 'dart:convert';

import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/widget/components/File.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/Colors.dart';
import '../../../../dnc/utils/DataUtils.dart';
import '../../../../dnc/widget/components/Image.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///安全生产责任保险
class SafetyProductionInsuranceWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const SafetyProductionInsuranceWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => SafetyProductionInsuranceWidgetState();
}

class SafetyProductionInsuranceWidgetState
    extends ComponentWidgetState<SafetyProductionInsuranceWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  bool isOpen = false;

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'getSafeProductInsuraRecords',
        url: '/qb/report/getSafeProductInsuraRecords',
        flag: widget.viewSubEntpriseFlag,
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final data = contentData['getSafeProductInsuraRecords'];
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: Color(0xff82AFFE),
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(4), topRight: Radius.circular(4)),
            ),
            child: Row(children: [
              Flexible(
                flex: 1,
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    '实际费用用途',
                    style: TextStyle(
                      fontSize: 15,
                      color: ColorsI.white,
                    ),
                  ),
                ),
              ),
              Flexible(
                flex: 1,
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    '支出金额(元)',
                    style: TextStyle(
                      fontSize: 15,
                      color: ColorsI.white,
                    ),
                  ),
                ),
              ),
            ]),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 8),
            color: ColorsI.black6,
            child: Row(
              children: [
                Flexible(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.center,
                    child: Text(
                      DU.safeString(data, ['expenseMatters']),
                      style: TextStyle(
                        fontSize: 14,
                        color: ColorsI.black66,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
                Flexible(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.center,
                    child: Text(
                      DU.safeString(data, ['expAmount']),
                      style: TextStyle(
                        fontSize: 14,
                        color: ColorsI.black66,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (data is Map && data.isNotEmpty)
            InkWell(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Text(
                          '详情',
                          style: TextStyle(
                            fontSize: 14,
                            color: ColorsI.blue,
                          ),
                        ),
                      ),
                      Icon(
                        isOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                        color: ColorsI.blue,
                      ),
                    ],
                  ),
                  if (isOpen)
                    Container(
                      child: Column(
                        children: [
                          infoBuilder('支出事项', DU.safeString(data, ['purpose'])),
                          infoBuilder(
                              '支出日期', DU.safeString(data, ['expenseDate'])),
                          infoBuilder(
                              '支出金额(元)', DU.safeString(data, ['expAmount'])),
                          Container(
                            margin: EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                Flexible(
                                  flex: 2,
                                  fit: FlexFit.tight,
                                  child: Text(
                                    '支出凭证: ',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: ColorsI.black66,
                                    ),
                                  ),
                                ),
                                Builder(builder: (_) {
                                  final temp =
                                      DU.safeList(data, ['expenseFile']);
                                  if (temp.isEmpty) {
                                    return SizedBox();
                                  }
                                  final url = DU.safeString(temp.first, ['url']);
                                  return Flexible(
                                    flex: 4,
                                    fit: FlexFit.tight,
                                    child: OperableImage(
                                      FileData.byUrl(url),
                                      height: 64,
                                    ),
                                  );
                                }),
                              ],
                            ),
                          ),
                          infoBuilder('备注', DU.safeString(data, ['remarks'])),
                        ],
                      ),
                    ),
                ],
              ),
              onTap: () {
                setState(() {
                  isOpen = !isOpen;
                });
              },
            ),
        ],
      ),
    );
  }

  Widget infoBuilder(String name, String value) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Flexible(
            flex: 2,
            fit: FlexFit.tight,
            child: Text(
              '$name: ',
              style: TextStyle(
                fontSize: 14,
                color: ColorsI.black66,
              ),
            ),
          ),
          Flexible(
            flex: 4,
            fit: FlexFit.tight,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: ColorsI.black66,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
