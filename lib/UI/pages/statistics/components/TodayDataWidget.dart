
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../dnc/utils/DataUtils.dart';
import '../ComponentWidget.dart';
import '../StatisticsUtils.dart';

///今日数据
class TodayDataWidget extends StatefulWidget {
  final String viewSubEntpriseFlag;
  final String title;

  const TodayDataWidget(
      {super.key, required this.viewSubEntpriseFlag, required this.title});

  @override
  State<StatefulWidget> createState() => TodayDataWidgetState();
}

class TodayDataWidgetState extends ComponentWidgetState<TodayDataWidget> {
  @override
  String get title => widget.title;

  List<String> get commands => [];

  @override
  List<Request>? onCreateRequest() {
    return [
      StatisticsUtils().request(
        tag: 'todayData',
        url: '/qb/report/todayData',
        flag: widget.viewSubEntpriseFlag,
        params: {
          'startTime': DU.formatDate(DU.getDayStart(), null),
          'endTime': DU.formatDate(DateTime.now(), null),
        },
        interceptor: (result) =>
            result is List && result.isNotEmpty ? result.first : {},
      ),
    ];
  }

  @override
  Widget component(BuildContext context, dynamic contentData, String? name) {
    final todayData = contentData['todayData'];
    return Column(
      children: [
        StatisticsUtils().itemCard1(info: [
          {
            'title': '计划巡检',
            'info': DU.safeString(todayData, ['pointCount'], '0'),
            'unit': '项'
          },
          {
            'title': '已完成',
            'info': DU.safeString(todayData, ['wcCount'], '0'),
            'unit': '项',
            'titleColor': Color(0xff537BC0)
          },
          {
            'title': '未完成',
            'info': DU.safeString(todayData, ['wwcCount'], '0'),
            'unit': '项',
            'titleColor': Color(0xffEB7A7C)
          },
        ]),
        StatisticsUtils().itemCard2(info: [
          {
            'title': '安全隐患',
            'info': DU.safeString(todayData, ['yhCount'], '0'),
            'unit': '项',
            'icon': 'assets/images/home/<USER>/home_statistics_today_1.png'
          },
          {
            'title': '危险作业',
            'info': DU.safeString(todayData, ['pgCount'], '0'),
            'unit': '项',
            'icon': 'assets/images/home/<USER>/home_statistics_today_2.png'
          },
        ]),
      ],
    );
  }
}
