import 'dart:async';
import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../dnc/network/Request.dart';
import '../../../dnc/utils/PlusUtils.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/components/Pop.dart';
import 'VerifyNFCPage.dart';

class VerifyCheckType {
  Future<bool> verify({
    required BuildContext context,
    required String checkType,
    String? qrCode,
    String? nfcCode,
    String? rydwCode,
  }) async {
    if (kDebugMode) {
      if (checkType.trim().isEmpty) {
        Toast.show("未找到支持的检查方式.");
        return false;
      } else {
        return true;
      }
    }
    if (checkType.contains('rydw')) {
      return await verifyRydw(
        context: context,
        checkType: checkType,
        qrCode: qrCode,
        nfcCode: nfcCode,
        rydwCode: rydwCode,
      );
    } else if (checkType.contains('nfc')) {
      return await verifyNFC(
        context: context,
        checkType: checkType,
        qrCode: qrCode,
        nfcCode: nfcCode,
      );
    } else if (checkType.contains('qrCode')) {
      return await verifyQRCode(
        context: context,
        qrCode: qrCode,
      );
    } else {
      Toast.show("未找到支持的检查方式.");
      return false;
    }
  }

  Future<bool> verifyRydw({
    required BuildContext context,
    required String checkType,
    String? qrCode,
    String? nfcCode,
    String? rydwCode,
  }) async {
    Toast.show('暂不支持定位检查.');
    return await verifyOther(
      context: context,
      checkType: checkType,
      qrCode: qrCode,
      nfcCode: nfcCode,
    );
    // 以上的return 为暂不支持人员定位时的情况,支持后删除上面代码即可.
    if (DU.isStrEmpty(rydwCode)) {
      Toast.show("未找到待校验的定位信息");
      return await verifyOther(
        context: context,
        checkType: checkType,
        qrCode: qrCode,
        nfcCode: nfcCode,
      );
    }
    final result = await Request(
      RequestType.GET,
      '/qyaq/scyftx/pcRecord/isWithinScope',
      params: () => {
        'unitId': rydwCode,
      },
    ).execute(showLoading: true);
    if (result is DioException || DU.safeString(result, ['isWithin']) != '1') {
      Toast.show('您不在定位检查范围内');
      return await verifyOther(
        context: context,
        checkType: checkType,
        qrCode: qrCode,
        nfcCode: nfcCode,
      );
    }
    return true;
  }

  Future<bool> verifyNFC({
    required BuildContext context,
    required String checkType,
    String? nfcCode,
    String? qrCode,
  }) async {
    if (DU.isStrEmpty(nfcCode)) {
      Toast.show("未找到待校验的NFC信息");
      if (checkType.contains('qrCode')) {
        return await verifyOther(
          context: context,
          checkType: 'qrCode',
          qrCode: qrCode,
        );
      } else {
        return false;
      }
    }
    final result = await WU.nextPage(
        context,
        VerifyNFCPage(
          checkType: checkType,
          nfcCode: nfcCode!,
          qrCode: qrCode,
        ));
    if (result == null) {
      Toast.show('NFC校验失败');
      return false;
    }
    return true;
  }

  Future<bool> verifyQRCode({
    required BuildContext context,
    String? qrCode,
  }) async {
    if (DU.isStrEmpty(qrCode)) {
      Toast.show("未找到待校验的二维码信息");
      return false;
    }

    final code = await getQRCode(context: context);
    if (code == null) {
      return false;
    }

    if (qrCode == code) {
      Toast.show("二维码校验成功");
      return true;
    } else {
      Toast.show("二维码不匹配");
      return false;
    }
  }

  Future<String?> getQRCode({
    required BuildContext context,
  }) async {
    final result = await ScanQR.scan();
    if (result.isEmpty) {
      return null;
    }

    // SDialog.showInfo(context, title: '扫描信息', info: 'result: $result \n '
    //     'resultId: ${DU.safeString(result, ['id'])}'
    //     'qrCode: $qrCode\n'
    //     'match: ${qrCode == DU.safeString(result, ['id'])}');

    String code;
    try {
      final obj = json.decode(result);
      code = DU.safeString(result, ['id']);
    } on FormatException {
      Toast.show('您使用了旧格式的二维码,建议更新二维码');
      code = result;
    }
    return code;
  }

  Future<bool> verifyOther({
    required BuildContext context,
    required String checkType,
    String? qrCode,
    String? nfcCode,
  }) async {
    final bool hasQRCode = checkType.contains('qrCode');
    final bool hasNFC = checkType.contains('nfc');
    if (!hasQRCode && !hasNFC) {
      return false;
    }
    Completer<bool> completer = Completer();
    await await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10), topRight: Radius.circular(10))),
      builder: (context) {
        return Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                child: Text(
                  '其他排查方式',
                  style: TextStyle(fontSize: 16, color: Colors.black54),
                ),
              ),
              SizedBox(
                height: 16,
              ),
              if (hasNFC) ...[
                InkWell(
                  child: Container(
                    alignment: Alignment.center,
                    margin: EdgeInsets.symmetric(horizontal: 32),
                    padding: EdgeInsets.symmetric(vertical: 16),
                    color: Color(0xff3A86DF),
                    child: Container(
                      child: Text(
                        'NFC',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  onTap: () async {
                    Navigator.of(context).pop();
                    final result = await verifyNFC(
                      context: context,
                      checkType: checkType,
                      nfcCode: nfcCode,
                      qrCode: qrCode,
                    );
                    completer.complete(result);
                  },
                ),
                SizedBox(
                  height: 16,
                ),
              ],
              if (hasQRCode)
                InkWell(
                  child: Container(
                    alignment: Alignment.center,
                    margin: EdgeInsets.symmetric(horizontal: 32),
                    padding: EdgeInsets.symmetric(vertical: 16),
                    color: Color(0xff3A86DF),
                    child: Container(
                      child: Text(
                        '二维码',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  onTap: () async {
                    Navigator.of(context).pop();
                    final result = verifyQRCode(
                      context: context,
                      qrCode: qrCode,
                    );
                    completer.complete(result);
                  },
                ),
            ],
          ),
        );
      },
    );

    return completer.future;
  }
}
