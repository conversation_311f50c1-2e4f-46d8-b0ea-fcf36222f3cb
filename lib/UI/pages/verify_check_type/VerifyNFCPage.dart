import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:flutter/material.dart';

import '../../../dnc/utils/DataUtils.dart';
import '../../../dnc/utils/PlusUtils.dart';
import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/components/Button.dart';
import '../../../dnc/widget/components/Pop.dart';

class VerifyNFCPage extends StatefulWidget {
  final String checkType;
  final String nfcCode;
  final String? qrCode;

  const VerifyNFCPage({
    super.key,
    required this.checkType,
    required this.nfcCode,
    this.qrCode,
  });

  @override
  State<StatefulWidget> createState() => VerifyNFCPageState();
}

class VerifyNFCPageState extends BasePageState<VerifyNFCPage> with TickerProviderStateMixin {
  @override
  String strTitle() => 'NFC校验';
  int status = 1; // 1扫描中，-1扫描失败；2扫描成功 0等待；
  late AnimationController _controller;

  @override
  void initState() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _controller.forward();
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _controller.reset();
        _controller.forward();
      }
    });
    getNFCInfo();
    super.initState();
  }

  Future<void> getNFCInfo() async {
    setState(() {
      status = 1;
    });
    var nfcCode = await NFC.getNFCInfo();
    if (nfcCode == widget.nfcCode) {
      Toast.show('校验成功');
      WU.closePage(context, arguments: 'true');
      return;
    } else {
      status = -1;
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget body(BuildContext context) {
    bool hasQR = widget.checkType.contains('qrCode');
    String info = '';
    switch (status) {
      case 1:
        info = '检测中,请将NFC卡片靠近手机背面';
        break;
      case -1:
        info = 'NFC检测失败,请点击上面的图标重新检测';
        break;
    }
    return Container(
      child: Column(
        children: [
          InkWell(
            child: Container(
              padding: EdgeInsets.all(64),
              child: Stack(
                children: [
                  Image.asset('assets/images/nfc/nfc_btn.png'),
                  if (status == 1)
                    RotationTransition(
                      alignment: Alignment.center,
                      turns: _controller,
                      child: Image.asset('assets/images/nfc/nfc_scaning.png'),
                    ),
                ],
              ),
            ),
            onTap: getNFCInfo,
          ),
          Text(
            info,
            style: TextStyle(color: Colors.black54),
          ),
          if (hasQR && status == -1) ...[
            Text(
              '或点击下方,使用二维码扫描',
              style: TextStyle(color: Colors.black54),
            ),
            Spacer(),
            Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: TileButton(
                name: '扫描二维码',
                onPressed: () async {
                  if(DU.isStrEmpty(widget.qrCode)){
                    Toast.show("未找到待校验的二维码信息");
                    return;
                  }
                  final qrCode = await ScanQR.scan();
                  if (qrCode != widget.qrCode) {
                    Toast.show("二维码不匹配");
                    return;
                  }
                  WU.closePage(context, arguments: 'true');
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
