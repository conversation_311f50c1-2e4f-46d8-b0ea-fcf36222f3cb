import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:ent_secutity_app/viewmodel/DictModel.dart';
import 'package:ent_secutity_app/viewmodel/warehouse_keeper/WarehouseKeeperPurchaseListViewModel.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';

import '../../../dnc/utils/WidgetUtils.dart';
import 'WarehouseKeeperPurchaseSearchPage.dart';
import 'WarehouseKeeperPurchaseDetailPage.dart';

class WarehouseKeeperPurchaseListPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return WarehouseKeeperPurchaseListPageState();
  }
}

class WarehouseKeeperPurchaseListPageState extends BasePageState<WarehouseKeeperPurchaseListPage> {
  MeetingsListViewModel _listModel = MeetingsListViewModel();

  @override
  String strTitle() => '采购入库';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          child: Container(
            color: Colors.white,
            padding: EdgeInsets.fromLTRB(16, 16, 16, 16),
            child: Row(
              children: [
                Icon(Icons.search, size: 20, color: Theme.of(context).hintColor),
                SizedBox(width: 20),
                Text('点击按条件查询', style: Theme.of(context).textTheme.headline3),
              ],
            ),
          ),
          onTap: () {
            toSearch(context);
          },
        ),
        Flexible(
            child: CustomItemListWidget(
          model: _listModel,
          itemBuilder: itemBuilder,
          enablePullDown: true,
          enablePullUp: true,
          showSeparator: false,
        )),
      ],
    );
  }

  Widget itemBuilder(BuildContext context, item, int index, bool isEnd) {
    var text;
    var textStyle;
    bool readOnly = item['status'] == "3";
    // if (!readOnly) {
    //   text = '未检查';
    //   textStyle = TextStyle(
    //       fontSize: 14,
    //       fontWeight: FontWeight.w300,
    //       color: Colors.white,
    //       backgroundColor: Color(0xffFE6173));
    // } else {
    //   text = '已检查';
    //   textStyle = TextStyle(
    //       fontSize: 14,
    //       fontWeight: FontWeight.w300,
    //       color: Colors.white,
    //       backgroundColor: Color(0xff65BF22));
    // }
    return SimpleCard(
      isSingle: false,
      tip: text,
      showShadow: false,
      style: textStyle,
      body: Container(
        padding: EdgeInsets.all(8),
        child: Column(
          children: [
            infoLabel(context, '运单日期:', item['billDate'] ?? ''),
            infoLabel(context, '运单编号:', item['billNo'] ?? ''),
            // infoLabel(context, '客户名称:', DU.findValueByDict(typeDict, item['checkType'])),
            infoLabel(context, '客户名称:',  item['customerNm'] ?? ''),
            infoLabel(context, '车牌号:', item['carNo'] ?? ''),
            infoLabel(context, '司机:', item['driverNm'] ?? ''),
            infoLabel(context, '司机身份证:', item['driverIdCard'] ?? ''),
            infoLabel(context, '质检结果:', item['checkResult'] ?? ''),
            infoLabel(context, '物料名称:', item['materielNm'] ?? ''),
            infoLabel(context, '计划数量:', (item['planAmount'] ?? 0).toString() + item['unit'] ),
            infoLabel(context, '毛重:', (item['inWeight'] ?? 0).toString() + item['unit']),
            infoLabel(context, '实际发货数量:', (item['realityAmount'] ?? 0).toString() + item['unit']),
           //  Container(
           //    padding: EdgeInsets.only(bottom: 6),
           //    child: Row(
           //      children: [
           //        Container(
           //          child: Text(
           //            '计划数量  ',
           //            style: Theme.of(context).primaryTextTheme.headline1,
           //          ),
           //        ),
           //        Container(
           //          child: Text(
           //            item['planAmount'].toStringAsFixed(2),
           //            style: Theme.of(context).primaryTextTheme.headline1,
           //          ),
           //        ),
           //      ],
           //    ),
           //  ),
            Column(
              children: [
                Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
                Container(
                  padding: EdgeInsets.fromLTRB(20, 12, 20, 6),
                  child: ContentButton(
                    name: "录入实际发货数量",
                    minSize: Size(150, 40),
                    onPressed: () => toDetail(context, item),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
      // onTap: () => toDetail(context, item),
    );
  }

  infoLabel(BuildContext context, String name, String value) {
    return Container(
      padding: EdgeInsets.only(bottom: 6),
      child: Row(
        children: [
          Container(
            constraints: BoxConstraints(minWidth: 100),
            child: Text(
              name,
              style: Theme.of(context).primaryTextTheme.headline2,
            ),
          ),
          Flexible(
            child: Text(
              value ?? '',
              style: Theme.of(context).primaryTextTheme.headline2,
            ),
          ),
        ],
      ),
    );
  }
  toSearch(BuildContext context) async {
    final result = await WU.nextPage(context, WarehouseKeeperPurchaseSearchPage());
    if (result == null) return;
    Map data = result;
    _listModel.carNo = data['carNo'];
    _listModel.driverPhone = data['driverPhone'];
    _listModel.driverIdCard = data['driverIdCard'];
    _listModel.refresh();
  }

  toDetail(BuildContext context, var item) async {
    final result =  await WU.nextPage(context, WarehouseKeeperPurchaseDetailPage(item));
      if (result != null) {
        _listModel.refresh();
      }
  }
  // toAdd(BuildContext context) async {
  //   final result = await WU.nextPage(context, MeetingsEditPage());
  //   if (result != null) {
  //     _listModel.refresh();
  //   }
  // }
}
