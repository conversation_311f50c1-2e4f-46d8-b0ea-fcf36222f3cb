import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/StateTile.dart';
import 'package:ent_secutity_app/viewmodel/DictModel.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter/src/widgets/framework.dart';

import '../../../dnc/widget/components/Pop.dart';
import '../../../dnc/widget/components/Widget.dart';
import '../../../utils/utils.dart';

class WarehouseKeeperPurchaseSearchPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return WarehouseKeeperPurchaseSearchPageState();
  }
}

class WarehouseKeeperPurchaseSearchPageState extends BasePageState<WarehouseKeeperPurchaseSearchPage> {
  WidgetData<String> carNo = WidgetData<String>();
  WidgetData<String> driverPhone = WidgetData();
  WidgetData<String> driverIdCard  = WidgetData<String>();
  @override
  String strTitle() => '搜索';

  @override
  Widget body(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(
            children: [
              InputTile(title: '车牌号', hint: '请输入车牌号', wd: carNo),
              InputTile(
                  title: '司机电话',
                  wd: driverPhone,
                  hint: '请输入司机电话',
                  limit: 11,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp("[0-9]"),
                    ),
                    FilteringTextInputFormatter.deny(
                      RegExp("[\\s]"),
                    )
                  ],
                  keyboardType: TextInputType.phone,
                  important: false,
                  readOnly: false),
              InputTile(
                  title: '司机身份证',
                  hint: '请输入司机身份证',
                  wd: driverIdCard,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[a-zA-Z0-9]')), // 允许字母和数字
                  ],
                  keyboardType: TextInputType.text),
            ],
            isSingle: true,
          ),
          SizedBox(height: 10),
          TileButton(name: "搜索", onPressed: onSubmit),
          SizedBox(height: 12)
        ],
      ),
    );
  }

  onSubmit() {
    if(carNo.data ?.isNotEmpty ?? false ){
      if(!Utils.isPlateNumber(carNo.data.toString())){
        Toast.show("车牌号格式输入不正确");
        return;
      }
    }
    if(driverPhone.data ?.isNotEmpty ?? false ){
      if(!Utils.isPhone(driverPhone.data.toString())){
        Toast.show("手机号格式输入不正确");
        return;
      }
    }
    if(driverIdCard.data ?.isNotEmpty ?? false ){
      if(!Utils.isIdentityCard(driverIdCard.data.toString())){
        Toast.show("身份证号格式输入不正确");
        return;
      }
    }
    var info = {
      "carNo": carNo.data != null ? carNo.data : "",
      "driverPhone": driverPhone.data != null ? driverPhone.data : "",
      "driverIdCard": driverIdCard.data != null ? driverIdCard.data : ""
    };
    Navigator.pop(context, info);
  }
}
