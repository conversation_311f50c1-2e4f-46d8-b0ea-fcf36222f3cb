import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:ent_secutity_app/dnc/widget/components/Card.dart';
import 'package:ent_secutity_app/dnc/widget/components/File.dart';
import 'package:ent_secutity_app/dnc/widget/components/Tile.dart';
import 'package:ent_secutity_app/dnc/widget/components/FormInput.dart';
import 'package:ent_secutity_app/viewmodel/warehouse_keeper/WarehouseKeeperViewModel.dart';
import 'package:flutter/material.dart';

import '../../../dnc/utils/WidgetUtils.dart';
import '../../../dnc/widget/components/Widget.dart';

class MeetingsDetailPage extends StatefulWidget {
  final item;

  MeetingsDetailPage(this.item, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MeetingsDetailPageState();
  }
}

class MeetingsDetailPageState extends BasePageState<MeetingsDetailPage> {
  MeetingsViewModel _model = MeetingsViewModel();
  TextEditingController _moneyController = TextEditingController();

  @override
  String strTitle() => '出库';

  @override
  void initState() {
    initData();
    super.initState();
  }

  initData() async {
      setData(widget.item, "meetingData");
      _moneyController.text = DU.safeString(widget.item, ['realityAmount']);
  }

  @override
  Widget body(BuildContext context) {
    final mdata = getData("meetingData");
    if (mdata == null) return WU.emptyWidget(context);
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TileCard(children: [
            TextTile(title: '运单日期', info: mdata['billDate']),
            TextTile(title: '运单编号', info: mdata['billNo']),
            TextTile(title: '客户名称', info: mdata['customerNm']),
            TextTile(title: '车牌号', info: mdata['carNo']),
            TextTile(title: '司机', info: mdata['driverNm']),
            TextTile(title: '司机身份证', info: mdata['driverIdCard']),
            TextTile(title: '物料名称', info: mdata['materielNm']),
            TextTile(title: '包装方式', info: mdata['packagNm']),
            TextTile(title: '质检结果', info: mdata['checkResult']),
            TextTile(title: '计划数量', info: mdata['planAmount'].toString()),
            TextTile(title: '皮重', info: mdata['inWeight'].toString()),
            FormInput(
              controller: _moneyController,
              labelText: "实际发货数量",
              hintText: "请输入",
              isDecimal: true,
            ),
            // Row(
            //   children: [
            //     Text('计划数量: '),
            //     SizedBox(width: 10),
            //     Expanded(
            //       child: TextField(
            //         controller: _planAmountController, // 绑定输入框的控制器
            //         keyboardType: TextInputType.number, // 设置键盘类型为数字
            //         decoration: InputDecoration(
            //           hintText: '请输入计划数量',
            //           border: OutlineInputBorder(),
            //         ),
            //       ),
            //     ),
            //   ],
            // ),
          ], isSingle: false),
          SizedBox(height: 10),
          TileButton(name: "提交", onPressed: onSubmit),
          SizedBox(height: 12)
        ],
      ),
    );
  }
  onSubmit() async {

    var params = {
      "id": widget.item['id'],
      "type": "XS",
      "realityAmount":  _moneyController.text,
    };
    MeetingsViewModel().add(params, success: () {
      Navigator.pop(context, 'refresh');
    });
  }
}
