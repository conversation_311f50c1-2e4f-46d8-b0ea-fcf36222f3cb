// import 'dart:io';
//
// import 'package:dio/dio.dart';
// import 'package:ent_secutity_app/common/colors.dart';
// import 'package:ent_secutity_app/widget/flat_button.dart';

// import 'package:flutter_html/flutter_html.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:permission_handler/permission_handler.dart';
//
// //app版本更新  对话框
// class AppUpdateDialog extends StatefulWidget {
//   final description, apkUrl;
//
//   AppUpdateDialog({
//     this.description,
//     this.apkUrl
//   });
//
//   @override
//   _AppUpdateDialogState createState() => _AppUpdateDialogState();
// }
//
// class _AppUpdateDialogState extends State<AppUpdateDialog> {
//
//   var description;
//   var apkUrl;
//   bool _isDownload = false;
//   double _value = 0;
//
//   @override
//   void initState() {
//     super.initState();
//
//     description = widget.description;
//     apkUrl = widget.apkUrl;
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Dialog(
//       shape: RoundedRectangleBorder(
//         borderRadius: BorderRadius.circular(Consts.padding),
//       ),
//       elevation: 0.0,
//       backgroundColor: Colors.transparent,
//       child: dialogContent(context),
//     );
//   }
//
//   dialogContent(BuildContext context) {
//     return Stack(
//       children: <Widget>[
//         buildCard(context),
//       ],
//     );
//   }
//
//   Widget buildCard(BuildContext context) {
//     return Container(
//         decoration: BoxDecoration(
//             color: Colors.white,
//             borderRadius: BorderRadius.circular(8.0)),
//         width: 280.0,
//         child: Column(
//           crossAxisAlignment: CrossAxisAlignment.start,
//           mainAxisSize: MainAxisSize.min,
//           children: <Widget>[
//             Container(
//                 height: 140.0,
//                 width: 280.0,
//                 decoration: BoxDecoration(
//                   borderRadius: const BorderRadius.only(
//                       topLeft: const Radius.circular(8.0),
//                       topRight: const Radius.circular(8.0)),
//                   image: DecorationImage(
//                       image: AssetImage("lib/assets/images/app_update.png"),
//                       fit: BoxFit.cover),
//                 )),
//             Padding(
//               padding: EdgeInsets.symmetric(horizontal: 15.0),
//               child: Text("新版本更新",
//                   style: TextStyle(fontSize: 16, color: Colors.black)),
//             ),
//             Padding(
//                 padding: const EdgeInsets.symmetric(
//                     horizontal: 15.0, vertical: 10.0),
//                 child: Html(
//                   // 渲染的数据
//                   data: description,
//                 )
//             ),
//             Padding(
//               padding: const EdgeInsets.only(
//                   bottom: 15.0, left: 15.0, right: 15.0, top: 15.0),
//               child: _isDownload
//                   ? LinearProgressIndicator(
//                   backgroundColor: Colors.grey,
//                   valueColor: AlwaysStoppedAnimation<Color>(
//                       Colors.lightBlue),
//                   value: _value)
//                   : Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: <Widget>[
//                   Container(
//                     width: 110.0,
//                     height: 36.0,
//                     child: FlatButton(
//                       onPressed: () {
//                         Navigator.pop(context);
//                       },
//                       textColor: Colors.lightBlue,
//                       color: Colors.transparent,
//                       disabledTextColor: Colors.white,
//                       disabledColor: Colors.grey,
//                       shape: RoundedRectangleBorder(
//                           borderRadius:
//                           BorderRadius.circular(18.0),
//                           side: BorderSide(
//                               color: MyColors.theme,
//                               width: 0.8)),
//                       child: Text("下次再说",
//                           style: TextStyle(
//                               fontSize: 16, color: MyColors.theme)),
//                     ),
//                   ),
//                   Container(
//                     width: 110.0,
//                     height: 36.0,
//                     child: FlatButton(
//                       onPressed: () {
//                         if (Platform.isAndroid) {
//                           isDownload();
//                         } else {
//                           //TODO  ios 直接跳转到url
//                           var url = 'https://itunes.apple.com/cn/app/%E5%86%8D%E6%83%A0%E5%90%88%E4%BC%99%E4%BA%BA/id1375433239?l=zh&ls=1&mt=8'
//                           ;
//                           InstallPlugin.gotoAppStore(url);
//                         }
//                       },
//                       textColor: Colors.white,
//                       color: MyColors.theme,
//                       disabledTextColor: Colors.white,
//                       disabledColor: Colors.grey,
//                       shape: RoundedRectangleBorder(
//                         borderRadius: BorderRadius.circular(18.0),
//                       ),
//                       child: Text("立即更新",
//                           style: TextStyle(fontSize: 16)),
//                     ),
//                   )
//                 ],
//               ),
//             )
//           ],
//         )
//     );
//   }
//
//   /// 安装apk
//   installApk() async {
//     File _apkFile = await downloadAndroid();
//     String _apkFilePath = _apkFile.path;
//
//     print('----_apkFilePath-----');
//     print(_apkFilePath);
//     if (_apkFilePath.isEmpty) {
//       print('make sure the apk file is set');
//       return;
//     }
//
//     InstallPlugin.installApk(_apkFilePath, 'com.xinpai.zhihuiwuliu')
//         .then((result) {
//       print('install apk $result');
//       Navigator.pop(context);
//     }).catchError((error) {
//       print('install apk error: $error');
//       Navigator.pop(context);
//     });
//   }
//
//   /// 下载安卓更新包
//   Future<File> downloadAndroid() async {
//     print('----downloadAndroid-----');
//
//     /// 创建存储文件
//     Directory storageDir = await getExternalStorageDirectory();
//     String storagePath = storageDir.path;
//     File file = new File('$storagePath/ent_secutity_app.apk');
//
//     print('----storagePath-----');
//     print(storagePath);
//     if (!file.existsSync()) {
//       file.createSync();
//     }
//
//     print('----apkUrl-----');
//     print(apkUrl);
//     try {
//       /// 发起下载请求
//       Response response = await Dio().get(apkUrl,
//           onReceiveProgress: (received, total) {
//             if (total != -1) {
//               ///当前下载的百分比例
//               print((received / total * 100).toStringAsFixed(0) + "%");
//               // CircularProgressIndicator(value: currentProgress,) 进度 0-1
//
//               print((received / total) * 100);
//               setState(() {
//                 _isDownload = true;
//                 _value = (received / total);
//               });
//             }
//           },
//           options: Options(
//             responseType: ResponseType.bytes,
//             followRedirects: false,
//           ));
//       file.writeAsBytesSync(response.data);
//       print('----writeAsBytesSync-----');
//       print(file);
//       return file;
//     } catch (e) {
//       print(e);
//     }
//   }
//
//   /// 检查是否有权限，用于安卓
//   isDownload() async {
//     var bool = false;
//     if (await Permission.storage
//         .request()
//         .isGranted) {
//       bool = true;
//     } else {
// // You can request multiple permissions at once.
//       Map<Permission, PermissionStatus> statuses = await [
//         Permission.storage,
//       ].request();
//       if (statuses[Permission.storage] == PermissionStatus.granted) {
//         bool = true;
//       }
//     }
//
//     print('----isDownload-----');
//     print(bool);
//     if (bool) {
//       installApk();
//     }
//   }
//
//   /// 展示下载进度
//   void showDownloadProgress(num received, num total) {
//     if (total != -1) {
//       double _progress =
//       double.parse('${(received / total).toStringAsFixed(2)}');
//     }
//   }
//
// }
//
// class Consts {
//   Consts._();
//
//   static const double padding = 16.0;
//   static const double avatarRadius = 16.0;
// }
