// import 'dart:io';
// import 'dart:typed_data';
//
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:dio/dio.dart';
// import 'package:ent_secutity_app/common/Urls.dart';
// import 'package:ent_secutity_app/common/colors.dart';
// import 'package:ent_secutity_app/common/uploadDio.dart';
// import 'package:ent_secutity_app/main.dart';
// import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:multi_image_picker/multi_image_picker.dart';
//
// import '../../dnc/widget/components/Pop.dart';
// import 'PhotoViewSimpleScreen.dart';
//
// const double itemSpace = 10.0;
// const double space = 5.0; //上下左右间距
// const double deleBtnWH = 20.0;
// const Color bgColor = Colors.white;
// List imgData = []; //图片list
//
// typedef CallBack = void Function(String type);
// // typedef CallBack2 = void Function(List data);
//
// @Deprecated('')
// //拍照 相册上传工具类  带照片数量
// class PhotoPickerCountTool extends StatefulWidget {
//   final double? lfPaddingSpace; //外部设置的左右间距
//   final CallBack? callBack;
//   final String readonly;
//   final List initImage;
//   final maxNum;
//
//   PhotoPickerCountTool(
//       {this.lfPaddingSpace, this.callBack, required this.readonly, required this.initImage, this.maxNum});
//
//   @override
//   _PhotoPickerCountToolState createState() => _PhotoPickerCountToolState();
// }
//
// class _PhotoPickerCountToolState extends State<PhotoPickerCountTool> {
//   var initFlag = '';
//   var contextSelf;
//   var showPic = false;
//   var imgData = [];
//   var maxNum = 9;
//
//   @override
//   void initState() {
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     imgData = widget.initImage;
//     if (widget.maxNum != null) {
//       maxNum = widget.maxNum;
//     }
//     return Container(
//         color: bgColor,
//         width: widget.readonly == 'no' ? 110 : 60,
//         height: 30,
//         child: Row(
//           children: [
//             widget.readonly == 'no'
//                 ? countStyle(context, setState, widget, imgData)
//                 : countStyle(context, setState, widget, imgData),
//             widget.readonly == 'no' ? addBtn(imgData, context, setState, widget) : Container(),
//           ],
//         ));
//   }
//
//   countStyle(context, setState, widget, imgData) {
//     var countStr = imgData.length.toString();
//
//     // if (widget.readonly == 'yes') {
//     //   countStr = widget.initImage.length.toString();
//     // }
//     // print('countStyle-----------');
//     // print(imgData);
// //  print(widget.initImage);
// //  print(countStr);
// //     setState(() {
// //       countStr = countStr;
// //     });
//
//     return GestureDetector(
//         onTap: () {
//           if (imgData.length == 0) {
//             Toast.show('无图片预览');
//             return;
//           }
//           var kScreenWidth = MediaQuery.of(context).size.width;
//           var lfPadding = 20;
//           var ninePictureW = (kScreenWidth - space * 2 - 2 * itemSpace - lfPadding);
//           var itemWH = ninePictureW / 3;
//           int columnCount = imgData.length > 6
//               ? 3
//               : imgData.length <= 3
//                   ? 1
//                   : 2;
//           Navigator.of(context).push(
//             MaterialPageRoute(builder: (BuildContext context) {
//               return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
//                 return Scaffold(
//                     appBar: AppBar(
//                         centerTitle: true, // 标题居中
//                         backgroundColor: MyColors.theme,
//                         title: Text(
//                           "预览",
//                         )),
//                     body: Container(
//                         color: bgColor,
//                         width: kScreenWidth - lfPadding,
//                         height: columnCount * itemWH + space * 2 + (columnCount - 1) * itemSpace,
//                         child: GridView.builder(
//                             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                               //可以直接指定每行（列）显示多少个Item
//                               //一行的Widget数量
//                               crossAxisCount: 3,
//                               crossAxisSpacing: itemSpace, //水平间距
//                               mainAxisSpacing: itemSpace, //垂直间距
//                               childAspectRatio: 1.0, //子Widget宽高比例
//                             ),
//                             physics: NeverScrollableScrollPhysics(),
//                             padding: EdgeInsets.all(space),
//                             //GridView内边距
//                             itemCount: imgData.length,
//                             itemBuilder: (context, index) {
//                               return imgItem(index, setState, imgData, widget);
//                             })));
//               });
//             }),
//           );
//         },
//         child: Container(
//             width: 50,
//             height: 40,
//             padding: EdgeInsets.fromLTRB(3, 6, 3, 3),
//             margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(18),
//               color: Color(0xffE4E4E4),
//             ),
//             child: Text(countStr + "/$maxNum",
//                 textAlign: TextAlign.center,
//                 style: TextStyle(
//                   color: Color(0xff9D9D9D),
//                   fontSize: 15,
//                 ))));
//   }
//
//   /** 添加按钮 */
//   Widget addBtn(imgData, context, setState, [PhotoPickerCountTool? widget]) {
//     return GestureDetector(
//         child: Container(
//             width: 30,
//             height: 30,
//             margin: EdgeInsets.fromLTRB(10, 0, 0, 0),
//             child: Image(image: AssetImage("lib/assets/images/camera_small.png"))),
//         onTap: () {
//           if (imgData.length == maxNum) {
//             Toast.show('最多支持上传$maxNum张照片');
//             return;
//           }
//           showDialog<Null>(
//             context: context,
//             builder: (BuildContext context) {
//               return new SimpleDialog(
//                 title: new Text('选择'),
//                 children: <Widget>[
//                   new SimpleDialogOption(
//                     child: new Text('拍照'),
//                     onPressed: () {
//                       takePhoto(context, setState, imgData, widget, 1);
//                       Navigator.of(context).pop();
//                     },
//                   ),
//                   new SimpleDialogOption(
//                     child: new Text('相册'),
//                     onPressed: () {
// //                    var index = 1;
//                       _openGallerySystem(setState, imgData, widget!);
//                       Navigator.of(context).pop();
//                     },
//                   ),
//                 ],
//               );
//             },
//           ).then((val) {
//             // print(val);
//           });
//         });
//   }
//
// //选择文件上传
//   void _openGallerySystem(setState, List imgData, PhotoPickerCountTool widget) async {
//     List<Asset> resultList = <Asset>[];
//     var index = imgData.length;
//     resultList = await MultiImagePicker.pickImages(
//       //最多选择几张照片
//       maxImages: maxNum - imgData.length,
//       //是否可以拍照
//       enableCamera: false,
//       selectedAssets: [],
//       materialOptions: MaterialOptions(
//           startInAllView: true,
//           allViewTitle: '所有照片',
//           actionBarColor: '#2196F3',
//           //未选择图片时提示
//           textOnNothingSelected: '没有选择照片',
//           //选择图片超过限制弹出提示
//           selectionLimitReachedText: "最多上传$maxNum张照片"),
//     );
//
//     for (var i = 0; i < resultList.length; i++) {
//       // print(resultList[i]);
//       ByteData byteData = await resultList[i].getByteData();
//       List<int> imageData = byteData.buffer.asUint8List();
//       UploadDio().post(Urls.upload, {"file": MultipartFile.fromBytes(imageData, filename: 'live_sure.jpg')},
//           success: (json) async {
//         imgData.add(json);
//         // print("*&* 2 $imgData");
//         setState(() {
//           //更新图片
//           imgData = imgData;
//         });
//         // print("--------add1---------");
//         // print(imgData);
//         if (widget.callBack != null) {
//           //接口回传图片数据
//           widget.callBack!('add');
//         }
//       }, fail: (reason, code) {
//         // print('uploadFile------fail:' + reason);
//         Toast.fail(reason);
//       });
//     }
//   }
//
//   Future takePhoto(context, setState, imgData, PhotoPickerCountTool? widget, index) async {
//     //index   0:相册  1:拍照
//     var source = ImageSource.camera;
// //  if (index == 0) {
// //    source = ImageSource.gallery;
// //  }
//     var image = await ImagePicker().getImage(source: source);
//     handlePic(image, setState, widget); //压缩处理图片
//   }
//
// //压缩处理图片
//   handlePic(image, setState, PhotoPickerCountTool? widget) async {
//     if (null != image) {
//       int quality = 70; //压缩质量比
//       if (Platform.isIOS) {
//         quality = 60;
//       }
//       //图片压缩
//       // File compressedFile = await FlutterNativeImage.compressImage(image.path, quality: quality, percentage: quality);
//       //图片上传
//       uploadFile(setState, imgData, image, widget);
//     }
//   }
//
//   uploadFile(setState, imgData, image, [PhotoPickerCountTool? widget]) async {
//     String path = image.path;
//     var index = imgData.length;
//     var name = path.substring(path.lastIndexOf("/") + 1, path.length);
//     UploadDio().post(Urls.upload, {"file": await MultipartFile.fromFile(path, filename: name)}, success: (json) async {
//       imgData.add(json);
//       // print("*&* 1 $imgData");
//       setState(() {
//         //更新图片
//         imgData = imgData;
//       });
//
//       // print("--------add2---------");
//       // print(imgData);
//       if (widget!.callBack != null) {
//         //接口回传图片数据
//         widget.callBack!('add');
//       }
//     }, fail: (reason, code) {
//       // print('uploadFile------fail:' + reason);
//       Toast.fail(reason);
//     });
//   }
//
//   /** 图片和删除按钮 */
//   Widget imgItem(index, setState, imgData, [PhotoPickerCountTool? widget]) {
//     var chooseIndex = index;
//     print(chooseIndex);
// //  var type = '';
//     var img = imgData[index];
// //  if (imgData[index] is String) {
// //    type = 'String'; //类型是string  表明是网络图片
// //  }
// //  print('----imgItem---');
// //  print(imgData[index]);
//     return GestureDetector(
//       child: Container(
//         color: Colors.transparent,
//         child: Stack(alignment: Alignment.topRight, children: <Widget>[
//           ConstrainedBox(
//             //类型是string  表明是网络图片  设置网络加载图片方式
// //                child: PhotoView(
// //                  imageProvider: AssetImage("lib/assets/images/login_bg.png"),
// //                ),
//             child: CachedNetworkImage(
//               imageUrl: imgData[index],
//               fit: BoxFit.cover,
//               placeholder: (context, url) => Container(
//                 padding: const EdgeInsets.fromLTRB(40.0, 40, 40.0, 40.0),
//                 child: CircularProgressIndicator(),
//                 height: 100.0,
//                 width: 100.0,
//               ),
//               errorWidget: (context, url, error) => Icon(Icons.error),
//             ),
//             constraints: BoxConstraints.expand(),
//           ),
//           (widget?.readonly != 'yes')
//               ? GestureDetector(
//                   child: Image(
//                       image: AssetImage('lib/assets/images/img_del_icon.png'), width: deleBtnWH, height: deleBtnWH),
//                   onTap: () {
//                     // print("--------del---------");
//                     // print(imgData);
//                     //点击删除按钮
//                     if (imgData.length > 0) {
//                       // print(chooseIndex);
//                       if (widget?.callBack != null) {
//                         //接口回传图片数据
//                         widget?.callBack!('remove');
//                       }
//                       imgData.removeAt(index);
// //                    widget.initImage.removeAt(index);
// //                       print("--------del-2--------");
// //                       print(imgData);
//                       setState(() {
//                         imgData = imgData;
//                       });
//                     }
//                   },
//                 )
//               : Container()
//         ]),
//       ),
//       onTap: () {
//         Navigator.push(navigatorKey.currentState!.overlay!.context,
//             new MaterialPageRoute(builder: (BuildContext context) {
//           return new PhotoViewSimpleScreen(
//             imageProvider: NetworkImage(img),
//             heroTag: 'simple',
//           );
//         }));
//       },
//     );
//   }
// }
