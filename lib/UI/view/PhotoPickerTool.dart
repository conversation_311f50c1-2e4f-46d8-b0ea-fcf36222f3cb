/*
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/uploadDio.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/main.dart';
import 'package:ent_secutity_app/widget/flat_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_native_image/flutter_native_image.dart';
import 'package:image_picker/image_picker.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../dnc/widget/components/Pop.dart';
import 'PhotoViewSimpleScreen.dart';

const double itemSpace = 10.0;
const double space = 5.0; //上下左右间距
const double deleBtnWH = 20.0;
const Color bgColor = Colors.white;

typedef CallBack = void Function(String url, int index, String type);

//拍照 相册上传工具类
class PhotoPickerTool extends StatefulWidget {
  final double? lfPaddingSpace; //外部设置的左右间距
  final CallBack? callBack;
  final String readonly;
  final List initImage;

  PhotoPickerTool(
      {this.lfPaddingSpace, this.callBack, required this.readonly, required this.initImage});

  @override
  _PhotoPickerToolState createState() => _PhotoPickerToolState();
}

class _PhotoPickerToolState extends State<PhotoPickerTool> {
  late bool readOnly;

  List imgData = []; //图片list
  var initFlag = '';
  var contextSelf;

  @override
  void initState() {
    // print("readonly: ${widget.readonly}");
    // imgData = [];
    // print(" print(widget.initImage)---------");
    // print(widget.initImage);
    readOnly = widget.readonly == null || widget.readonly == "yes";
//     if (null != widget.initImage) {
//       //初始化图片数据
//       imgData = widget.initImage;
// //      imgURL = widget.initImage;
//     }
    // if (widget.readonly == 'no') {
    //   imgData.add("lib/assets/images/add_img.png"); //先添加 加号按钮 的图片
    // }
    // print(" initState(==imgData)---------");
    // print("imgData: '$imgData");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    imgData = widget.initImage;
    var kScreenWidth = MediaQuery.of(context).size.width;
    var lfPadding = widget.lfPaddingSpace == null ? 0.0 : widget.lfPaddingSpace;
    var ninePictureW = (kScreenWidth - space * 2 - 2 * itemSpace - lfPadding!);
    var itemWH = ninePictureW / 3;
    final length = readOnly || imgData.length == 9 ? imgData.length : imgData.length + 1;
    int columnCount = length > 6
        ? 3
        : length > 3
            ? 2
            : 1;
    // print("readonly: ${widget.readonly}   'imgData: '$imgData");
    return !readOnly || length > 0
        ? Container(
            color: bgColor,
            width: kScreenWidth - lfPadding,
            height: columnCount * itemWH + space * 2 + (columnCount - 1) * itemSpace,
            child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  //可以直接指定每行（列）显示多少个Item
                  //一行的Widget数量
                  crossAxisCount: 3,
                  crossAxisSpacing: itemSpace, //水平间距
                  mainAxisSpacing: itemSpace, //垂直间距
                  childAspectRatio: 1.0, //子Widget宽高比例
                ),
                physics: NeverScrollableScrollPhysics(),
                padding: EdgeInsets.all(space),
                //GridView内边距
                itemCount: length,
                itemBuilder: (context, index) {
                  if (!readOnly && index == imgData.length) {
                    return addBtn(context, setState, imgData, widget);
                  } else {
                    return imgItem(index, setState, imgData, widget);
                  }
                }))
        : Container(
            padding: EdgeInsets.fromLTRB(8, 16, 8, 16),
            child: Text(
              "暂无信息",
              style: TextStyle(
                  color: Color(0xffAAAAAA),
                  fontSize: 15,
                  fontWeight: FontWeight.normal,
                  overflow: TextOverflow.ellipsis),
            ),
          );
  }

  */
/** 添加按钮 */ /*

  Widget addBtn(context, setState, imgData, [PhotoPickerTool? widget]) {
    return GestureDetector(
        child: Image(image: AssetImage("lib/assets/images/add_img.png")),
        onTap: () {
          //点击添加按钮

//        Utils.showPickerModal(
//            context, [ "相册","拍照"], (var index,
//            var name) {
//            takePhoto(context, setState, imgData, widget,index);
//        });
          var index = 1;
          takePhoto(context, setState, imgData, widget, index);
        });
  }

  Future takePhoto(context, setState, imgData, PhotoPickerTool? widget, index) async {
    //index   0:相册  1:拍照
    var source = ImageSource.camera;
    if (index == 0) {
      source = ImageSource.gallery;
    }
    var image = await ImagePicker().getImage(source: source);

    if (null != image) {
      int quality = 70; //压缩质量比
      if (Platform.isIOS) {
        quality = 60;
      }
      //图片压缩
      File compressedFile =
          await FlutterNativeImage.compressImage(image.path, quality: quality, percentage: quality);
      //图片上传
      uploadFile(setState, imgData, compressedFile, imgData.length - 1, widget);
    }
  }

  uploadFile(setState, List imgData, image, index, [PhotoPickerTool? widget]) async {
    String path = image.path;
    var name = path.substring(path.lastIndexOf("/") + 1, path.length);
    UploadDio().post(Urls.upload, {"file": await MultipartFile.fromFile(path, filename: name)},
        success: (json) async {
//        if (null != widget.initImage &&
//            widget.initImage.length == 0) { //初始化图片数据为空 展示文件图片  因为网络图片加载时间太长了
//          imgData.insert(index, image);
//        } else {
      imgData.add(json);
//        }
      setState(() {
        //更新图片
        // imgData = imgData;
      });

      if (widget?.callBack != null) {
        //接口回传图片数据
        widget!.callBack!(json as String, index, 'add');
      }
    }, fail: (reason, code) {
      // print('uploadFile------fail:' + reason);
      Toast.fail(reason);
    });
  }

  */
/** 图片和删除按钮 */ /*

  Widget imgItem(index, setState, imgData, [PhotoPickerTool? widget]) {
    final url = imgData[index];
    final ext = '';//DU.getFileExt(url);
    final isImage = true; //DU.isImageFile(ext);
    return GestureDetector(
      child: Container(
        color: Colors.transparent,
        child: Stack(alignment: Alignment.topRight, children: <Widget>[
          ConstrainedBox(
            //类型是string  表明是网络图片  设置网络加载图片方式
//                child: PhotoView(
//                  imageProvider: AssetImage("lib/assets/images/login_bg.png"),
//                ),

            child: isImage
                ? CachedNetworkImage(
                    imageUrl: url,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      padding: const EdgeInsets.fromLTRB(40.0, 40, 40.0, 40.0),
                      child: CircularProgressIndicator(),
                      height: 100.0,
                      width: 100.0,
                    ),
                    errorWidget: (context, url, error) => Icon(Icons.error),
                  )
                : Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Colors.grey, width: 1.0),
                        borderRadius: BorderRadius.circular(3.0)),
                    height: 100.0,
                    width: 100.0,
                    child: Center(
                      child: Text(ext.toUpperCase(),
                          style: TextStyle(
                            color: Color(0x8a000000),
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          )),
                    ),
                  ),
            constraints: BoxConstraints.expand(),
          ),
          (widget?.readonly == 'no')
              ? GestureDetector(
                  child: Image(
                      image: AssetImage('lib/assets/images/img_del_icon.png'),
                      width: deleBtnWH,
                      height: deleBtnWH),
                  onTap: () {
                    //点击删除按钮
                    imgData.removeAt(index);
                    setState(() {});
                    if (widget?.callBack != null) {
                      //接口回传图片数据
                      widget!.callBack!("", index, 'remove');
                    }
                  },
                )
              : Container()
        ]),
      ),
      onTap: () async {
        if (isImage) {
          Navigator.push(navigatorKey.currentState!.overlay!.context,
              new MaterialPageRoute(builder: (BuildContext context) {
            return new PhotoViewSimpleScreen(
              imageProvider: NetworkImage(url),
              heroTag: 'simple',
            );
          }));
        } else {
          if (await canLaunch(url)) {
            await launch(url);
          } else {
            // Utils.toastCenter("url不能进行访问，异常");
          }
        }
      },
    );

//  CachedNetworkImage(
//    imageUrl: imgData[index],
//    fit: BoxFit.cover,
//    placeholder: (context, url) =>
//        Container(
//          padding: const EdgeInsets.fromLTRB(
//              40.0, 40, 40.0, 40.0),
//          child: CircularProgressIndicator(),
//          height: 100.0,
//          width: 100.0,
//        ),
//    errorWidget: (context, url, error) => Icon(Icons.error),
//  ) : Image(
//  image: FileImage(imgData[index]),
//  width: 300,
//  height: 300,
//  fit: BoxFit.cover,
//  ),
  }
}
*/
