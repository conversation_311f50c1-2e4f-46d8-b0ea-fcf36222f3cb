import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class RadioBox extends StatelessWidget {
  int selectIndex;
  int index;

  RadioBox({required this.selectIndex, required this.index});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(right: 0),
      child: Stack(
        children: [
          Container(
            height: 18,
            width: 18,
            child: (selectIndex == index)
                ? Image(image: AssetImage("lib/assets/images/icon_select.png"))
                : Image(image: AssetImage("lib/assets/images/icon_no_select.png")),
          ),
        ],
      ),
    );
  }
}
