/*
import 'package:ent_secutity_app/base/MultiStateWidget.dart';
import 'package:ent_secutity_app/base/ProviderWidget.dart';
import 'package:ent_secutity_app/UI/view/selectDepartment.dart';
import 'package:ent_secutity_app/utils/utils.dart';
import 'package:ent_secutity_app/viewmodel/user/UserListViewModel.dart';
import 'package:ent_secutity_app/widget/flat_button.dart';
import 'package:flutter/material.dart'; //导入了Material UI组件库
import 'package:ent_secutity_app/common/colors.dart';
import 'package:flutter/services.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../dnc/widget/business/DepartmentSelector.dart';
import '../../dnc/widget/components/Widget.dart';
import 'RefreshFooter.dart';
import 'RefreshHeader.dart';

//部门用户选择页
@Deprecated("已被UserSelector替代")
class UserSelectPage extends StatefulWidget {

  final arguments;

  UserSelectPage({this.arguments});

  @override
  UserSelectState createState() => UserSelectState();
}


class UserSelectState extends State<UserSelectPage> {
  UserListViewModel listModel = UserListViewModel();
  FocusNode _contentFocusNode = FocusNode();
  TextEditingController depText = new TextEditingController();

//  List departmentTree = [];
//  List departmentNowList = [];
//  List departmentNowListLast = [];

  var isCheckBox = '';
  var departmentId = '';

  List<String> selName = [];
  List<String> selIds = [];

  var departmentController = WidgetData();

  @override
  void initState() {
    super.initState();
    isCheckBox = widget.arguments['isCheckBox']??'';
    departmentId = widget.arguments['departmentId']??'';
    listModel.departmentId = departmentId;
    print("listModel.departmentId:  $departmentId");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        //背景颜色
        backgroundColor: MyColors.theme,
        brightness: Brightness.dark,
        title: Center(
          child: Text(
            "人员选择",
          ),
        ),
        actions: <Widget>[
          isCheckBox == "yes" ? FlatButton(
            padding: EdgeInsets.zero,
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),
            onPressed: () => selectCheck(),
          ) : Container()
        ],
      ),
      body:
      Scaffold(
          body: Container(
            child: Column(
              children: [

//              Container( height: 50),
                (departmentId == '' || departmentId == null) ? Container(
                  color: Colors.white,
                  height: 50,
                  child: Row(
                    children: <Widget>[
                      Container(
                          width: 120,
                          padding: const EdgeInsets.fromLTRB(
                              18.0, 0, 0, 0),
                          child: Align(
                              alignment: Alignment.centerLeft,
                              child:
                              Text('所属部门:',
                                  textAlign: TextAlign.left,
                                  style: TextStyle(
                                    color: Colors.black,
                                    height: 1,
                                    fontSize: 16,
                                  )))),
                      Expanded(
                        child: TextFormField(
                            focusNode: _contentFocusNode,
                            controller: depText,
                            decoration: inputStyle("点击选择"),
                            onTap: () async {
                              _contentFocusNode.unfocus();

                              var item = await DepartmentSelector(wd: departmentController)
                                  .show(context: context);
                              depText.text = item["name"];
                              listModel.departmentId = item["id"];
                              listModel.refresh();
                            }
                        ),
                      ),
                    ],
                  ),
                ) : Container(),
                Divider(
                    color: MyColors.dividerColor,
                    height: 2,
                    thickness: 5.0),
                Expanded(
//                  height: MediaQuery
//                      .of(context)
//                      .size
//                      .height - 160,

                  child: Container(
                    child: ProviderWidget<UserListViewModel>(
                      model: listModel,
                      onReady: (model) {
                        model.initData();
                      },
                      builder: (context, model, child) {
                        return MultiStateWidget(
                          builder: (context) =>
                              buildSmartRefresher(model),
                          state: model.state,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          )),
    );
  }


  SmartRefresher buildSmartRefresher(UserListViewModel model) {
    return SmartRefresher(
      enablePullDown: false,
      enablePullUp: true,
      //是否允许上拉
      header: RefreshHeader(),
      footer: RefreshFooter(),
      controller: model.refreshController,
      onRefresh: model.refresh,
      //进入下拉刷新时的回调
      onLoading: model.loadMore,
      //进入上拉加载时的回调
      child: ListView.separated(
        //ListView 的item
        itemBuilder: (context, i) {
          var item = model.list[i];

          return
            Container(
                constraints: BoxConstraints(
                  minHeight: 30,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.all(Radius.circular(5.0)),
                  border: Border.all(width: 1, color: Colors.white),
                ),
                margin: const EdgeInsets.fromLTRB(
                    0.0, 0.0, 10.0, 0.0),
                padding: isCheckBox == 'yes' ? const EdgeInsets.fromLTRB(
                    0.0, 0, 0.0, 0.0) : const EdgeInsets.fromLTRB(
                    18.0, 10, 3.0, 13.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    isCheckBox == 'yes' ? CheckboxListTile(
                      // 必须要的属性
                      value: item['isSelected'] == null ||
                          item['isSelected'] == false ? false : true,
                      //是否选中发生变化时的回调， 回调的 bool 值 就是是否选中 , true 是 选中
                      onChanged: (isCheck) {
                        setState(() {
                          item['isSelected'] = isCheck;
                          //保存已选中的
                          if (item['isSelected']) {
                            if (!selIds.contains(item["id"])) {
                              selIds.add(item["id"]);
                              selName.add(item["name"]);
                            }
                          } //删除
                          else {
                            if (selIds != null && selIds.contains(item["id"])) {
                              selName.remove(item["name"]);
                              selIds.remove(item["id"]);
                            }
                          }
                        });
                      },
                      title: Text(item["name"],
                          textAlign: TextAlign.left,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                          )),
                      // 选中的文字是否跟着高亮
//                      selected:  item['isSelected'],
                      // 选中后对号的颜色
                      activeColor: MyColors.theme,
                    ) :
                    GestureDetector(child:
                    Container(
                      alignment: Alignment.centerLeft,
                      child:
                      Row(
                        children: [
                          Expanded(
                              child: Text(item["name"],
                                  textAlign: TextAlign.left,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 16,
                                  ))),
                        ],
                      ),
                    ), onTap: () => toSelect(item)),
                  ],
                )
            );
        },
        itemCount: model.list != [] ? model.list.length : 0,
        separatorBuilder: (BuildContext context, int index) {
          return Divider(
              color: MyColors.dividerColor,
              height: 2,
              thickness: 1.0);
        },
      ),
    );
  }


  //跳转详情页
  void toSelect(item) {
    listModel.departmentId = '';
    //单选
    Navigator.pop(context, item['id'] + "," + item["name"]);
  }


  //多选
  selectCheck() {
    //多选  selName     selIds
    var names = '',
        ids = '';

    for (var i = 0; i < selName.length; i++) {
      if (i == selName.length - 1) {
        names += selName[i];
        ids += selIds[i];
      } else {
        names += selName[i] + ",";
        ids += selIds[i] + ",";
      }
    }
    listModel.departmentId = '';

    if (selName.length > 0) {
      Navigator.pop(context, ids + "|" + names);
    } else {
      Navigator.pop(context);
    }
  }

  //input样式
  inputStyle(String text) {
    return InputDecoration(
      hintText: text,
      contentPadding: EdgeInsets.fromLTRB(0, 19, 10, 15),
      hintStyle: TextStyle(color: MyColors.inputHint, fontSize: 16),
      border: InputBorder.none,
    );
  }
}
*/
