import 'package:ent_secutity_app/common/colors.dart';
import 'package:ent_secutity_app/viewmodel/user/UserListViewModel.dart';
import 'package:flutter/material.dart';

//部门选择sheet
@Deprecated("已被DepartmentSelector替代")
class SelectDepartment {
  static List departmentTree = [];
  static List departmentNowList = [];
  static List departmentNowListLast = [];

  static getDepartment(UserListViewModel listModel, context, TextEditingController depText,
      {ValueChanged<String>? cb}) async {
    List result = await listModel.loadDepartment();

    departmentTree = result;
    departmentNowList = result;
    departmentNowListLast = [];

    showModalBottomSheet(
        context: context,
        builder: (context) => StatefulBuilder(
              // 嵌套一个StatefulBuilder 部件
              builder: (context, setState) => Container(
                height: 350.0,
                color: Colors.white,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Container(
                              padding: const EdgeInsets.fromLTRB(18.0, 0, 0, 0),
                              height: 45.0,
                              child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: GestureDetector(
                                      child: Text('返回上级',
                                          textAlign: TextAlign.left,
                                          style: TextStyle(
                                            color: MyColors.theme,
                                            height: 1,
                                            fontSize: 17,
                                          )),
                                      onTap: () => returnLevel(setState)))),
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(
                              height: 45.0,
                              child: Align(
                                  alignment: Alignment.center,
                                  child: Text('部门选择',
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Color(0xff323334),
                                        height: 1,
                                        fontSize: 18,
                                      )))),
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(
//                                  height: 45.0,
//                                  child: Icon(Icons.chevron_right)
                              ),
                        ),
                      ],
                    ),
                    Divider(color: MyColors.dividerColor, height: 2, thickness: 1.0),
                    Container(
                      height: 300.0,
                      child: ListView.builder(
                        itemCount: departmentNowList.length,
                        itemBuilder: (context, index) {
                          return Container(
                            child: ListTile(
                              title: Row(
                                children: [
                                  GestureDetector(
                                      child: Icon(Icons.radio_button_checked, color: Colors.grey),
                                      onTap: () => choose(
                                          listModel, departmentNowList[index], setState, context, depText,
                                          cb: cb)),
                                  GestureDetector(
                                      child: Container(
                                        padding: const EdgeInsets.fromLTRB(18.0, 0, 0, 0),
                                        child: Text(departmentNowList[index]["name"]),
                                      ),
                                      onTap: () => toDeepDepartment(
                                          listModel, departmentNowList[index], setState, context, depText,
                                          cb: cb)),
                                  departmentNowList[index]["children"].length != 0
                                      ? Icon(Icons.chevron_right)
                                      : Container()
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  //展示深层的数据
  static toDeepDepartment(listModel, item, setState, context, depText, {ValueChanged<String>? cb}) {
    if (item["children"].length != 0) {
      setState(() {
        departmentNowListLast.add(departmentNowList);
        departmentNowList = item["children"];
      });
    } else {
      choose(listModel, item, setState, context, depText, cb: cb);
    }
  }

  static choose(listModel, item, setState, context, depText, {ValueChanged<String>? cb}) {
    depText.text = item["name"];
    if (cb != null) {
      cb(item["id"]);
    }
    listModel.departmentId = item["id"];
//      if (listModel.list.length) {
    listModel.refresh();
//      }
    Navigator.pop(context, item["id"]);
  }

  //返回上一级
  static returnLevel(setState) {
    if (0 != departmentNowListLast.length) {
      setState(() {
        departmentNowList = departmentNowListLast[departmentNowListLast.length - 1];
        departmentNowListLast.removeLast();
      });
    }
  }
}
