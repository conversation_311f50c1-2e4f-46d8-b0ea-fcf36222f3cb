import 'package:ent_secutity_app/common/colors.dart';
import 'package:flutter/material.dart';

//选择sheet
class SelectTree {
  static List treeList = [];
  static List nowList = [];
  static List nowListLast = [];

  static getTree(List result, context, TextEditingController name, TextEditingController code, title) async {
    treeList = result;
    nowList = result;
    nowListLast = [];

    showModalBottomSheet(
        context: context,
        builder: (context) => StatefulBuilder(
              // 嵌套一个StatefulBuilder 部件
              builder: (context, setState) => Container(
                height: 350.0,
                color: Colors.white,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Expanded(
                          flex: 1,
                          child: Container(
                              padding: const EdgeInsets.fromLTRB(18.0, 0, 0, 0),
                              height: 45.0,
                              child: Align(
                                  alignment: Alignment.centerLeft,
                                  child: GestureDetector(
                                      child: Text('返回上级',
                                          textAlign: TextAlign.left,
                                          style: TextStyle(
                                            color: MyColors.theme,
                                            height: 1,
                                            fontSize: 17,
                                          )),
                                      onTap: () => returnLevel(setState)))),
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(
                              height: 45.0,
                              child: Align(
                                  alignment: Alignment.center,
                                  child: Text(title,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Color(0xff323334),
                                        height: 1,
                                        fontSize: 18,
                                      )))),
                        ),
                        Expanded(
                          flex: 1,
                          child: Container(
//                                  height: 45.0,
//                                  child: Icon(Icons.chevron_right)
                              ),
                        ),
                      ],
                    ),
                    Divider(color: MyColors.dividerColor, height: 2, thickness: 1.0),
                    Container(
                      height: 300.0,
                      child: ListView.builder(
                        itemCount: nowList.length,
                        itemBuilder: (context, index) {
                          return Container(
                            child: ListTile(
                              title: Row(
                                children: [
                                  GestureDetector(
                                      child: Icon(Icons.radio_button_checked, color: Colors.grey),
                                      onTap: () => choose(nowList[index], setState, context, name, code)),
                                  GestureDetector(
                                      child: Container(
                                        padding: const EdgeInsets.fromLTRB(18.0, 0, 0, 0),
                                        child: Text(nowList[index]["name"]),
                                      ),
                                      onTap: () => toDeepDepartment(nowList[index], setState, context, name, code)),
                                  nowList[index]["children"].length != 0 ? Icon(Icons.chevron_right) : Container()
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  //展示深层的数据
  static toDeepDepartment(item, setState, context, name, code) {
    if (item["children"].length != 0) {
      setState(() {
        nowListLast.add(nowList);
        nowList = item["children"];
      });
    } else {
      choose(item, setState, context, name, code);
    }
  }

  static choose(item, setState, context, textEditingController, code) {
    textEditingController.text = item["name"];
    code.text = item["id"];
    Navigator.pop(context, item["id"]);
  }

  //返回上一级
  static returnLevel(setState) {
    if (0 != nowListLast.length) {
      setState(() {
        nowList = nowListLast[nowListLast.length - 1];
        nowListLast.removeLast();
      });
    }
  }
}
