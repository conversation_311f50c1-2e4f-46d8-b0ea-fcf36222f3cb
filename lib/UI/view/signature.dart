import 'package:ent_secutity_app/common/colors.dart';
import 'package:ent_secutity_app/widget/flat_button.dart';
import 'package:flutter/material.dart';

//签名
class SignatureView extends StatefulWidget {
  final signatureController;
  final finishCallback; //回调
  final colorCallback; //回调
  final double width;
  final double height;
  final bool landScape; // 是否横屏
  SignatureView(
      {Key? key,
      this.signatureController,
      this.finishCallback,
      this.colorCallback,
      required this.width,
      required this.height,
      required this.landScape})
      : super(key: key);

  @override
  _SignatureViewState createState() => _SignatureViewState();
}

class _SignatureViewState extends State<SignatureView> {
  late bool isEmpty;

  @override
  void initState() {
    super.initState();

    if (widget.signatureController.value.length > 0) {
      isEmpty = false;
    } else {
      isEmpty = true;
    }

    // 监听画板
    widget.signatureController.addListener(() {
      bool tmpIsEmpty = true;
      if (widget.signatureController.value.length > 0) {
        tmpIsEmpty = false;
      } else {
        tmpIsEmpty = true;
      }
      if (isEmpty != tmpIsEmpty) {
        if (this.mounted) {
          setState(() {
            isEmpty = tmpIsEmpty;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // 监听画板
    widget.signatureController.addListener(() {
      bool tmpIsEmpty = true;
      if (widget.signatureController.value.length > 0) {
        tmpIsEmpty = false;
      } else {
        tmpIsEmpty = true;
      }
      if (isEmpty != tmpIsEmpty) {
        if (this.mounted) {
          setState(() {
            isEmpty = tmpIsEmpty;
          });
        }
      }
    });

    return RotatedBox(
      quarterTurns: widget.landScape ? 1 : 0,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Signature(
          //   controller: widget.signatureController,
          //   width:widget.width ,
          //   height: widget.height,
          //   backgroundColor: Colors.white,
          // ),

          // 暂无签名
          Offstage(
            offstage: isEmpty ? false : true,
            child: Text(
              '签名（必填）',
              style: TextStyle(
                fontSize: 44,
                color: Color(0xffA5ACB4),
              ),
            ),
          ),
          // 橡皮 & 完成 按钮
          Positioned(
            bottom: 0,
            right: 10,
            child: Row(
              children: [
                Offstage(
                  offstage: widget.landScape ? false : true,
                  child: GestureDetector(
                      child: Container(
                        width: 40,
                        height: 30,
                        margin: EdgeInsets.fromLTRB(0, 0, 15, 0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2),
                          color: Colors.red,
                        ),
                      ),
                      onTap: () {
                        setState(() {
                          widget.colorCallback(Colors.red);
                        });
                      }),
                ),
                Offstage(
                  offstage: widget.landScape ? false : true,
                  child: GestureDetector(
                      child: Container(
                        width: 40,
                        height: 30,
                        margin: EdgeInsets.fromLTRB(0, 0, 50, 0),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2),
                          color: Colors.black,
                        ),
                      ),
                      onTap: () {
                        setState(() {
                          widget.colorCallback(Colors.black);
                        });
                      }),
                ),
                // 清除
                Offstage(
                  offstage: widget.landScape ? false : true,
                  child: Container(
                    width: 80,
                    height: 50,
                    margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: MyColors.grey,
                    ),
                    child: FlatButton(
                      padding: EdgeInsets.zero,
                      child: Text(
                        '重写',
                        style: TextStyle(
                          fontSize: 25,
                          color: Colors.white,
                        ),
                      ),
                      onPressed: () {
                        setState(() {
                          widget.signatureController.clear();
                        });
                      },
                    ),
                  ),
                ),
                // 预览
                Offstage(
                  offstage: widget.landScape ? false : true,
                  child: Container(
                    width: 80,
                    height: 50,
                    margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: MyColors.theme,
                    ),
                    child: FlatButton(
                      padding: EdgeInsets.zero,
                      child: Text(
                        '预览',
                        style: TextStyle(
                          fontSize: 25,
                          color: Colors.white,
                        ),
                      ),
                      onPressed: () async {
                        if (widget.signatureController.isNotEmpty) {
                          var data = await widget.signatureController.toPngBytes();
                          print(data);
                          print(data.runtimeType);
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (BuildContext context) {
                                return RotatedBox(
                                    quarterTurns: widget.landScape ? 1 : 0,
                                    child: Scaffold(
                                        appBar: AppBar(
                                            centerTitle: true, // 标题居中
                                            backgroundColor: MyColors.theme,
                                            title: Text(
                                              "签名预览",
                                            )),
                                        body: Container(
                                          width: widget.width,
                                          height: widget.height,
                                          child: Image.memory(data),
                                        )));
                              },
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ),
                // 完成
                Offstage(
                  offstage: widget.landScape ? false : true,
                  child: Container(
                    width: 80,
                    height: 50,
                    margin: EdgeInsets.fromLTRB(0, 0, 10, 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: MyColors.theme,
                    ),
                    child: FlatButton(
                      padding: EdgeInsets.zero,
                      child: Text(
                        '完成',
                        style: TextStyle(
                          fontSize: 25,
                          color: Colors.white,
                        ),
                      ),
                      onPressed: () async {
                        var data = await widget.signatureController.toPngBytes();
                        widget.finishCallback(data);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
