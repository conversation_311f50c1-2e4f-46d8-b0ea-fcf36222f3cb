import 'BaseViewModel.dart';

/// 基于
abstract class BaseListViewModel<T> extends BaseViewModel {
  /// 页面数据
  List<T> list = [];

  /// 第一次进入页面loading skeleton
  initData({Map? param}) async {
    setStateLoding();
    await refresh(init: true);
  }

  // 下拉刷新
  refresh({bool init = false}) async {
    try {
      List<T> data = await loadData();
      if (data.isEmpty) {
        list.clear();
        setStateEmpty();
      } else {
        onCompleted(data);
        list.clear();
        list.addAll(data);
        setStateContent();
      }
    } catch (e, s) {
      if (init) list.clear();
      setStateFail();
    }
  }

  // 加载数据
  Future<List<T>> loadData();

  onCompleted(List<T> data) {}
}
