import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'base_state.dart';

typedef Success = void Function();
typedef Fail = void Function();

class BaseViewModel extends ChangeNotifier {
  /// 防止页面销毁后,异步任务才完成,导致报错
  bool _disposed = false;

  BaseState state = BaseState.EMPTY;

//  LOADING,
//  EMPTY,
//  CONTENT,
//  FAIL,
  /// 当前的页面状态,默认为busy,可在viewModel的构造方法中指定;
//  ViewState _viewState;

  /// 根据状态构造
  ///
  /// 子类可以在构造函数指定需要的页面状态
  /// FooModel():super(viewState:ViewState.busy);
//  BaseViewModel({ViewState viewState})
//      : _viewState = viewState ?? ViewState.idle {
//    debugPrint('ViewStateModel---constructor--->$runtimeType');
//  }

  /// ViewState
//  ViewState get viewState => _viewState;
//
//  set viewState(ViewState viewState) {
//    _viewStateError = null;
//    _viewState = viewState;
//    notifyListeners();
//  }

  /// ViewStateError
//  ViewStateError _viewStateError;
//

//  ViewStateError get viewStateError => _viewStateError;

  /// 以下变量是为了代码书写方便,加入的get方法.严格意义上讲,并不严谨
  ///
  /// get
  bool get isLoading => state == BaseState.LOADING;

//
//  bool get isIdle => viewState == ViewState.idle;
//
//  bool get isEmpty => viewState == ViewState.empty;
//
//  bool get isError => viewState == ViewState.error;

  /// set
  void setStateLoding() {
    state = BaseState.LOADING;
    notifyListeners();
  }

  void setStateContent() {
    state = BaseState.CONTENT;
    notifyListeners();
  }

  void setStateEmpty() {
    state = BaseState.EMPTY;
    notifyListeners();
  }

  void setStateFail() {
    state = BaseState.FAIL;
    notifyListeners();
  }

  // showMessage(String message) {
  //   if (message != null) {
  //     Fluttertoast.showToast(
  //         msg: message,
  //         toastLength: Toast.LENGTH_SHORT,
  //         gravity: ToastGravity.CENTER,
  //         timeInSecForIosWeb: 1,
  //         backgroundColor: MyColors.title,
  //         textColor: Colors.white,
  //         fontSize: 16.0);
  //   }
  // }

  @override
  void notifyListeners() {
    if (!_disposed) {
      super.notifyListeners();
    }
  }

  /// [e]分类Error和Exception两种
//  void setError(e, stackTrace, {String message}) {
//    ViewStateErrorType errorType = ViewStateErrorType.defaultError;
//
//    /// 见https://github.com/flutterchina/dio/blob/master/README-ZH.md#DioExceptionType
//    if (e is DioException) {
//      if (e.type == DioExceptionType.CONNECT_TIMEOUT ||
//          e.type == DioExceptionType.SEND_TIMEOUT ||
//          e.type == DioExceptionType.RECEIVE_TIMEOUT) {
//        // timeout
//        errorType = ViewStateErrorType.networkTimeOutError;
//        message = e.error;
//      } else if (e.type == DioExceptionType.RESPONSE) {
//        // incorrect status, such as 404, 503...
//        message = e.error;
//      } else if (e.type == DioExceptionType.CANCEL) {
//        // to be continue...
//        message = e.error;
//      } else {
//        // dio将原error重新套了一层
//        e = e.error;
////        if (e is UnAuthorizedException) {
////          stackTrace = null;
////          errorType = ViewStateErrorType.unauthorizedError;
////        } else if (e is NotSuccessException) {
////          stackTrace = null;
////          message = e.message;
////        } else if (e is SocketException) {
////          errorType = ViewStateErrorType.networkTimeOutError;
////          message = e.message;
////        } else {
////          message = e.message;
////        }
//      }
//    }
//    viewState = ViewState.error;
//    _viewStateError = ViewStateError(
//      errorType,
//      message: message,
//      errorMessage: e.toString(),
//    );
////    printErrorStack(e, stackTrace);
//    onError(viewStateError);
//  }

//  void onError(ViewStateError viewStateError) {}
//
//  /// 显示错误消息
//  showErrorMessage(context, {String message}) {
//    if (viewStateError != null || message != null) {
//      if (viewStateError.isNetworkTimeOut) {
//        message ??= "网络连接异常,请检查网络或稍后重试";
//      } else {
//        message ??= viewStateError.message;
//      }
//      Future.microtask(() {
//        showMessage(message);
//      });
//    }
//  }

  @override
  void dispose() {
    _disposed = true;
    debugPrint('view_state_model dispose -->$runtimeType');
    super.dispose();
  }
}
