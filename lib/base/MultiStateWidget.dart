import 'package:ent_secutity_app/utils/textUtils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'base_state.dart';

typedef Content = Widget Function(BuildContext context);

class MultiStateWidget extends StatefulWidget {
  Widget? loadingWidget;
  Widget? emptyWidget;
  Widget? failWidget;
  Content? builder;
  BaseState? state;

  MultiStateWidget({Key? key, Content? builder, this.state, this.emptyWidget, this.failWidget, this.loadingWidget})
      : super(key: key) {
    if (state == BaseState.CONTENT) {
      if (builder != null) {
        this.builder = builder;
      }
    }
    if (emptyWidget == null) {
      emptyWidget = EmptyStateWidget();
    }

    if (failWidget == null) {
      failWidget = ErrorStateWidget();
    }

    if (loadingWidget == null) {
      loadingWidget = LoadingStateWidget();
    }
  }

  @override
  _MultiStateWidgetState createState() => _MultiStateWidgetState();
}

class _MultiStateWidgetState extends State<MultiStateWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.state == null)
      return Container(
        width: 0,
        height: 0,
      );
    if (widget.state == BaseState.CONTENT) {
      return widget.builder!(context);
    } else if (widget.state == BaseState.EMPTY) {
      return widget.emptyWidget!;
    } else if (widget.state == BaseState.FAIL) {
      return widget.failWidget!;
    } else {
      return widget.loadingWidget!;
    }
  }
}

class EmptyStateWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.fromLTRB(0, 80, 0, 0),
                child: Column(
                  children: [
                    Image(
                      image: AssetImage('lib/assets/images/no_data.png'),
                      fit: BoxFit.contain,
                      width: 200,
                      height: 200,
                    ),
                    Text(
                      "暂无数据",
                      style: TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
          onTap: () {
//            Utils.toastCenter("请尝试退出页面重新进入");
          }),
    );
  }
}

class ErrorStateWidget extends StatelessWidget {
  String? reason;

  ErrorStateWidget({this.reason});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
          child: Container(
            margin: EdgeInsets.fromLTRB(0, 80, 0, 0),
            child: Column(
              children: [
                Image(
                  image: AssetImage('lib/assets/images/fail.png'),
                  fit: BoxFit.contain,
                  width: 200,
                  height: 200,
                ),
                Text(
                  TextUtils.getNonStr(reason ?? '', ifNonReplace: "获取数据失败"),
                  style: TextStyle(color: Colors.grey, fontSize: 14),
                ),
              ],
            ),
          ),
          onTap: () {
//            Utils.toastCenter("请尝试退出页面重新进入");
          }),
    );
  }
}

class LoadingStateWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        child: Text(
          "加载中...",
          style: TextStyle(color: Colors.grey, fontSize: 14),
        ),
      ),
    );
  }
}
