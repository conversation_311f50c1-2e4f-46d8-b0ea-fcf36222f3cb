import 'package:flutter/foundation.dart';

class AC {
  static bool offlineMode = false;
  static bool isTest = kDebugMode; // 是否开启测试模式
  static const MessageEnable = true; // 是否开启新版消息相关功能

  /*test 参数，正式发布时如下
  static const SkipScan =false; // 跳过巡检扫码!!!!!
  static const useTestUpload = false; // 是否使用固定的文件上传地址。
  static const  isOpenTUICall = false; // 是否打开音视频通话
  */
  static const SkipScan = kDebugMode; // 跳过巡检扫码!!!!!Å
  static const useTestUpload = true; // 是否使用固定的文件上传地址。
  static const isOpenTUICall = false; //!kDebugMode;//!isTest; // 是否打开音视频通话
}
