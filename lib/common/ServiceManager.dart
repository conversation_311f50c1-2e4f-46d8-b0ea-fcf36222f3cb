import 'dart:convert';
import 'dart:io';

import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ServiceManager {
  static String currentHost = '';
  static const String BUILD_CHANNEL = String.fromEnvironment('APP_CHANNEL');

// ============================================================================

  dynamic _serverData;

  static bool isDevMode = true;

  List testList = [];

  List serverList = [];

  Future<void> init() async {
    String addressStr = await rootBundle.loadString('assets/code/address.json');
    Map distances = json.decode(addressStr);
    testList = distances['testList'];
    serverList = distances['serverList'];
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    String channel = prefs.getString('channel') ?? '';
    if (channel.isEmpty) {
      channel = BUILD_CHANNEL;
      // Toast.show(channel);
      //  channel = 'GuangDongSaas';
    }
    String storedHost = prefs.getString("serviceHost") ?? '';
    // if (storedHost.isNotEmpty) {
    //   _serverData = findServerDataByHost(storedHost);
    //   isDevMode = true;
    // } else if (kDebugMode) {
      _serverData = testList.first;
      isDevMode = true;
    // } else {
    //   _serverData = findServerDataByChannel(channel);
    // }
    // if (_serverData == null) {
    //   // Toast.show('未匹配到服务');
    // }
    print('currentServer: $_serverData');
    currentHost = DU.safeString(_serverData, ['host']);
  }

  dynamic findServerDataByChannel(String channel) {
    for (dynamic server in serverList) {
      if (server['app'] == channel) {
        return server;
      }
    }
    return null;
  }

  dynamic findServerDataByHost(String host) {
    List temp = List.from(testList)..addAll(serverList);
    for (dynamic server in temp) {
      if (server['host'] == host) {
        return server;
      }
    }
    return {"name": "自定义", "host": host};
  }

  // String getHost() {
  //   return DU.safeString(_serverData, ['host']);
  // }

  String get currentServerName {
    return DU.safeString(_serverData, ['name']);
  }

  String get currentServerLogo {
    return DU.safeString(_serverData, ['logo']);
  }

  Future<void> setHost(String host) async {
    if (host.isEmpty) {
      return;
    }
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('serviceHost', host);
    _serverData = findServerDataByHost(host);
    isDevMode = true;
    currentHost = DU.safeString(_serverData, ['host']);
    print('currentServer: $_serverData');
  }

  Future<bool> setChannel(String entKey) async {
    // if (entKey == "XinPai*#2022#") {
      await setHost((testList.first)['host']);
      return true;
    // }
    // entKey = entKey.toUpperCase();
    // for (dynamic element in serverList) {
    //   if (element["entKey"] == entKey) {
    //     final SharedPreferences prefs = await SharedPreferences.getInstance();
    //     await prefs.setString('channel', element["app"]);
    //     _serverData = element;
    //     print('currentServer: $_serverData');
    //     currentHost = DU.safeString(_serverData, ['host']);
    //     return true;
    //   }
    // }
    // return false;
  }
}
