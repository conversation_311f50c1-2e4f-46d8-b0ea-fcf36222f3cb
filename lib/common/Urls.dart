class Urls {
  //登录
  static const String login = "/auth/login";

  //用户信息
  static const String userInfo = "/user/info";

  //修改密码
  static const String userpsdEdit = "/user/modify-password";

  //获取用户权限
  static const String userRole = "/appBussiness/role/roleList";

  //----------------数据字典----------------
  static const String dict = "/sys/data-dict/";

  //历史
  //app获取版本
  static const String appUpdate = "/qyaq/appUpdate/get";

  //用户列表
  static const String userList = "/user/search";
  //基础参数设置
  static const String systemSetting = "/qyaq/bs/system/setting/search";

  //修改用户信息
  static const String userinoEdit = "/user/update"; // "/qyaq/user/update/current";

  //部门组织树
  static const String departmentTree = "/department/tree";
  static const String departmentChildTree = '/qyaq/department/childTree';
  static const String limitedDepartmentTree = "/task-department/tree";
  //排查任务列表
  static const String taskList = "/qyaq/xj/task/search";

  //排查任务列表 --待执行和执行中的合并列表
  static const String taskListDoing = "/qyaq/xj/task/searchDoing";

  //排查任务详情
  static const String taskDetail = "/qyaq/xj/task/get";

  //排查任务-审核
  static const String taskCheck = "/qyaq/xj/task/check";

  //领取排查任务
  static const String getTask = "/qyaq/xj/task/getTheTask";

  //取消排查任务
  static const String cancelTask = "/qyaq/xj/task/cancelTheTask";

  //排查任务详情-风险分析单元列表
  static const String checkPointList = "/qyaq/xj/task/point/search";

  //排查任务详情-风险分析单元详情
  static const String getCheckPoint = "/qyaq/xj/point/get";

  //排查任务详情-风险分析单元列表-风险分析单元执行提交
  static const String subCheckPoint = "/qyaq/xj/task/point/submit";

  //排查任务详情-风险分析单元列表-管控措施-异常提交
  static const String subPointItemException = "/qyaq/xj/abnormal/add";

  //排查任务详情-风险分析单元列表-管控措施-异常状态更改
  static const String editPointItemException = "/qyaq/xj/abnormal/edit";

  //排查任务详情-风险分析单元列表-管控措施-异常信息
  static const String getAbnormal = "/qyaq/xj/abnormal/get";

  //排查任务详情-风险分析单元列表-管控措施-异常删除
  static const String delPointItemException = "/qyaq/xj/abnormal/delByItem";

  //排查任务详情-风险分析单元列表-管控措施-异常现场解决
  static const String subExceptionLiveResolve = "/qyaq/xj/abnormalSolve/add";

  //排查任务详情-风险分析单元列表-管控措施-异常现场解决详情获取
  static const String getExceptionLiveResolve = "/qyaq/xj/abnormalSolve/getByAbnormal";

  //排查任务详情-风险分析单元列表-管控措施
  static const String checkPointItemList = "/qyaq/xj/task/pointItem/search";

  //排查任务详情-风险分析单元列表-管控措施 提交管控措施执行结果
  static const String subCheckPointItem = "/qyaq/xj/task/pointItem/execute";

  //管控措施详情
  static const String getCheckPointItem = "/qyaq/item/form/get";

  //上传文件
  static const String upload = "/upload";

  //整改任务列表
  static const String reformTaskList = "/qyaq/reformtask/search";

  //执行整改任务
  static const String executeReformTask = "/qyaq/reformtask/execute";

  //整改任务 -详情
  static const String reformTaskDetail = "/qyaq/reformtask/get";

  //整改任务 -详情id 获取排查任务信息
  static const String getByReformtaskIds = '/qyaq/xj/abnormal/getByReformtaskIds';

  //整改任务 审核
  static const String reformTaskEdit = "/qyaq/reformtask/edit";

  //整改任务
  static const String setReformUser = "/qyaq/reformtask/editAppoint";

  //问题整改列表
  static const String problemFeedbackList = "/qyaq/feedback/search";

  //新增-问题整改
  static const String addProblemFeedback = "/qyaq/feedback/add";

  //编辑-问题整改
  static const String editProblemFeedback = "/qyaq/feedback/edit";

  //问题整改 -详情
  static const String getProblemFeedback = "/qyaq/feedback/get";

  //问题整改 -详情-通过整改ReformtaskId获取
  static const String getFeedbackByReformtaskId = "/qyaq/feedback/getByReformtaskId";

  //问题整改-审核
  static const String checkProblemFeedback = "/qyaq/feedback/editRefuse";

  //创建整改任务
  static const String createReform = "/qyaq/reformtask/add";

  //设备保养 列表
  static const String deviceKeepList = "/qyaq/sb/baoyang/task/search";
  static const String deviceKeepAll = "/qyaq/sb/baoyang/task/list";
  static const String deviceKeepInfo = '/qyaq/sb/baoyang/task/get';

  //设备检测 列表
  static const String deviceCheckAll = "/qyaq/sb/deviceCheck/list";

  //完成设备保养
  static const String deviceKeepTaskFinish = "/qyaq/sb/baoyang/task/finish";

  //设备台账 列表
  static const String deviceList = "/qyaq/sb/device/search";

  //设备维修列表
  static const String deviceMaintainList = "/qyaq/sb/device/repair/search";
  static const String deviceMaintainAll = "/qyaq/sb/device/repair/list";
  static const String deviceMaintainInfo = '/qyaq/sb/device/repair/get';

  //设备列表
  static const String getDeviceList = "/qyaq/sb/device/search";
  // static const String getDeviceList = "/qyaq/sb/device/list";

  //发起维修
  static const String addDeviceRepair = "/qyaq/sb/device/repair/add";

  //发布维修
  static const String publishDeviceRepair = "/qyaq/sb/device/repair/editStatus3";

  //发起工单
  static const String startDeviceRepair = "/qyaq/sb/device/repair/edit";

  //暂停维修
  static const String finishDeviceRepair = "/qyaq/sb/device/repair/editStatus4";

  //维修完成
  static const String stopDeviceRepair = "/qyaq/sb/device/repair/editStatus5";

  //承包商
  static const String contractorAll = "/qyaq/cbs/contractor/list";
  static const String contractorList = "/qyaq/cbs/contractor/search";
  static const String violationList = "/qyaq/cbs/violation/search"; //违章记录
  static const String peopleList = "/qyaq/cbs/people/search"; //人员列表
  static const String violationAdd = "/qyaq/cbs/violation/add"; //添加违章

  //危化品
  static const String chemicalsList = "/qyaq/wxhxp/whpdzdn/getListByPage";
  static const String storeOutList = "/qyaq/wxhxp/storeInOutRecord/search"; //出库
  //"/qb/report/searchInRecords"
  static const String storeInList = "/qyaq/wxhxp/storeInOutRecord/search"; //入库
  static const String storeList = "/qb/report/getListByPage"; //库存
  static const String storeAll = "/qyaq/wxhxp/whpck/list"; //所有仓库
  static const String supplierAll = "/qyaq/wxhxp/gys/list"; //所有供应商

  //排查任务-执行规则(扫码规则)
  static const String taskSetting = "/qyaq/bs/system/setting/get";

  //计划任务 ---我的任务
  static const String scheduleTaskList = '/task/my/todo';

  //计划任务 ---我的任务详情
  static const String getScheduleTask = '/task/get';

  //计划任务 ---任务进展列表
  static const String getProgressList = '/task/progress/list';

  //计划任务 ---已结束任务
  static const String CloseScheduleTaskList = '/task/my/closed';

  //计划任务 ---由我分配的任务
  static const String AssignedScheduleTaskList = '/task/my/assigned';

  //计划任务 ---由我评估的任务
  static const String JudgeScheduleTaskList = '/task/my/judge';

  //计划任务 ---我关注的任务
  static const String FollowerScheduleTaskList = '/qb/report/MyFollowTasks';

  //计划任务 ---数据字典
  static const String ScheduleDict = '/cascade-dict/item/tree/by_dict_code';

  //计划任务-创建任务
  static const String scheduleTaskAdd = '/task/add';

  //计划任务-编辑任务
  static const String scheduleTaskUpdate = '/task/update';

  //计划任务-编辑任务
  static const String scheduleTaskDelete = '/task/delete';

  //计划任务-初始化进度表单
  static const String scheduleTaskProgressInit = '/task/progress/init';

  //计划任务-提交进度表单
  static const String scheduleTaskProgressAdd = '/task/progress/add';

  //计划任务-初始化任务评估
  static const String scheduleTaskJudgeInit = '/task/judge/init';

  //计划任务-添加任务评估
  static const String scheduleTaskJudgeAdd = '/task/judge/add';

//计划任务-任务评估列表
  static const String scheduleTaskJudgeList = '/task/judge/list';

//计划任务-任务评估列表
  static const String schedueTaskSaveChildren = '/task/save-children';

//计划任务-任务沟通记录
  static const String schedueTaskChatList = '/task/comment/list';

//计划任务-任务沟通记录添加
  static const String schedueTaskChatAdd = '/task/comment/add';

  //随手拍
  static const String takePictureList = '/qyaq/yh/search';
  static const String takePictureAdd = '/qyaq/yh/add';
  static const String takePictureDetail = '/qyaq/yh/get';

  //整改任务
  static const String taskRectifyList = '/qb/report/yh_reform_app_list';
  static const String taskRectifyDetail = '/qyaq/yh/zhenggai/search/get';
  static const String taskRectifyDetail2 = '/qyaq/check/abnormal/get';
  static const String taskRectifyDetail3 = '/qyaq/yh/zhenggai/searchHistoricVariables';

  //--------------工作流----------------
  //发起
  static const String bpmStart = "/bpm/run/start";

  //提交并保存
  static const String bpmSubmit = "/bpm/run/submit";

  //领取任务
  static const String bpmClaim = "/bpm/run/claim";

  //取消任务
  static const String bpmUnClaim = "/bpm/run/unclaim";

  //通过rocessInstanceId获取task
  static const String todoByProcessInstanceId = "/bpm/task/todoByProcessInstanceId";

  //待办列表
  static const String bpmTODOList = "/bpm/task/todo";

  //    作业票 提交/ 取消
  static const String cancelOrSubmitTicket = "/qyaq/wxzyTicket/handleSubmit";

  // 作业票  完工审批
  static const String ticketFinishCheck = "/qyaq/wxzyTicket/handlerWangong";

  // 作业票  签发
  static const String ticketRemoteSignSub = "/qyaq/wxzyTicket/handlerYuancheng";

  // 作业票  现场确认
  static const String ticketLiveSureSub = "/qyaq/wxzyTicket/handleXianchang";

  // 作业票  完工申请
  static const String ticketFinishApplySub = "/qyaq/wxzyTicket/handlerWangongSq";

  // 作业票  评估审批
  static const String assessCheckTicket = "/qyaq/wxzyTicket/handlerAudit";

  //作业票  延期申请
  static const String delayAddTicket = "/qyaq/wxzyTicket/handlerYqsqSubmit";

  //  作业票  延期签发
  static const String delayCheckTicket = "/qyaq/wxzyTicket/handlerYqqfSubmit";

  //作业票添加/作业票编辑
  static const String addAndEditTicket = "/qyaq/wxzyTicket/edit";

  //作业申请列表
  static const String ticketList = "/qyaq/wxzyTicket/search";

  //作业票查询列表
  static const String ticketSearchList = "/qb/report/wxzySearch";

  // 作业票  气体检测查询
  static const String qitiSearch = "/qyaq/wxzyTicket/qitiSearch";

  //审批人员列表t
  static const String ticketBpmMember = "/qyaq/wxzyTicketBpmMember/list";

  //风险识别列表
  static const String ticketRiskIdentify = "/qyaq/wxzyriskIdentify/listByType";

  //获取主票其他作业票种类
  static const String ticketOthers = "/qyaq/wxzyTicket/list";

  //根据主票获取从票
  static const String ticketCong = "/qyaq/wxzyTicket/getViceTicketList";

  //作业票详情
  static const String getTicket = "/qyaq/wxzyTicket/get";

//  提交作业票-审批流程信息
  static const String subFlows = "/qyaq/wxzy/ticketAuditMember/addBatch";

// 提交作业票-风险辨识信息
  static const String subRiskData = "/qyaq/wxzyTicketRiskIdentify/add";

//  获取作业票-审批流程信息
  static const String getTicketFlows = "/qyaq/wxzy/ticketAuditMember/list";

  //  获取作业票-风险辨识信息
  static const String getTicketRiskData = "/qyaq/wxzy/ticketRiskIdentify/list";

  //根据流程ID查询数据
  static const String getTicketByProcessInstanceId = "/qyaq/wxzyTicket/getByProcessInstanceId";

  //安全培训列表
  static const String getSafeList = "/qyaq/wxzyTrainingDisclosure/search";

  //查询作业区域列表
  static const String getArea = "/qyaq/wxzy/TicketArea/list";

  //查询作业票-气体检测列表
  static const String getTicketGasCheck = "/qyaq/wxzyTicketGasCheck/listByTicket";

  //查询作业票-现场确认列表
  static const String getTicketlive = "/qyaq/wxzy/ticketConfirm/list";

  //查询作业票-完工申请列表
  static const String getTicketFinishApply = "/qyaq/wxzy/ticketFinishApply/list";

  //查询作业票-完工申请-完工确认列表
  static const String getTicketFinishSureList = "/qyaq/wxzyCompleteConfirmItem/list";

  //查询作业票-完工申请-完工确认列表
  static const String getTicketFinish = "/qyaq/wxzy/ticketFinishApply/list";

//根据作业票ID，获取作业票最大的延期申请时间
  static const String getMaxDelayTime = "/qyaq/wxzyTicketDelay/getMaxDelayTime";

//根据作业票ID，获取延期申请列表
  static const String geticketDelay = "/qyaq/wxzy/ticketDelay/list";

  //获取延期申请列表
  static const String getAllicketDelay = '/qyaq/wxzyTicketDelay/search';

  //添加气体检测
  static const String addTicketGas = "/qyaq/wxzyTicket/handleQitijiance";

  //通过ids获取作业票列表
  static const String getTicketByIds = "/qyaq/wxzyTicket/getByIds";

  //  按照作业票和节点名称添加（编辑）
  static const String subYCQFFlows = "/qyaq/wxzy/ticketAuditMember/updateNode";

  //施工人员 --企业内部人员列表
  static const String workUserEntList = "/qb/report/archCertSearchReport";

  //获取 应急措施
  static const String getYingjicuoshiData = "/qyaq/wxzy/yingjicuoshi/list";

  //获取 获取防范措施
  static const String getFangfancuoshiData = "/qyaq/wxzy/fangfancuoshi/list";

  //获取 作业票子表数据
  static const String getDongHuoSubDataByTicketId = "/qyaq/wxzy/ticketDonghuo/getByTicketId"; //动火子表
  static const String getShouXianSubDataByTicketId = "/qyaq/wxzy/ticketSubShouxiankongjian/getByTicketId";
  static const String getGaoChuSubDataByTicketId = "/qyaq/wxzy/ticketSubGaochu/getByTicketId";
  static const String getDiaoZhuangSubDataByTicketId = "/qyaq/wxzy/ticketSubDiaozhuang/getByTicketId";
  static const String getYongDianSubDataByTicketId = "/qyaq/wxzy/ticketSubLinshiyongdian/getByTicketId";
  static const String getDongTuSubDataByTicketId = "/qyaq/wxzy/ticketSubDongtun/getByTicketId";
  static const String getDuanLuSubDataByTicketId = "/qyaq/wxzy/ticketSubDuanlu/getByTicketId";
  static const String getMangBanSubDataByTicketId = "/qyaq/wxzy/ticketSubMangban/getByTicketId";

  //根据 作业票id 获取节点处理数据
  static const String getDealDataByTicketId = "/qyaq/wxzyticketNodeExecute/list";

  //
  static const String getTicketAllowDesc = "/qyaq/wxzyTicket/getTicketAllowDesc";

  //当班班长验票
  static const String leaderSure = "/qyaq/wxzyTicket/handlerDangbanbzyp";

  //----------------------



  //极光推送发送registrationId
  // static const String jPushSubmitRegistrationId = "/jiguang/register";
  static const String jPushSubmitRegistrationId = "/notification/j-push/employee/bind";

  //课程--查看个人课程列表
  static const String classList = "/qb/report/AppGetUserClassList";

  //课程答题--课程答题卡列表
  static const String getClassQuestionCardList = "/qb/report/AppGetUserClassQuestion";

  //课程答题--保存某道题的答案
  static const String saveAnswerClass = "/qyaq/class/saveAnswer";

  //课程答题--开始答题/继续答题 都用这个，
  static const String startClass = "/qyaq/class/startClass";

  //课程答题--获取某道题的信息
  static const String classGetQuestion = "/qyaq/class/getQuestion";

  //考试列表
  static const String examList = "/qb/report/AppGetUserExamList";

  //考试--开始考试/补考（
  static const String startExam = "/qyaq/exam/startExam";

  //考试--继续考试 继续考试
  static const String continueExam = "/qyaq/exam/continueExam";

  //考试---获取某道题的信息
  static const String getQuestionExam = "/qyaq/exam/getQuestion";

  //考试---保存某道题的答案
  static const String saveAnswerExam = "/qyaq/exam/saveAnswer";

  //考试--考试答题卡列表
  static const String getExamQuestionCardList = "/qb/report/AppGetUserExamQuestion";

  // 交卷
  static const String submitPaper = "/qyaq/exam/submitPaper";

  //告警
  static const String EntWarning = '/sp/warning/search'; // 企业告警

// 视频告警 -卸油告警列表
  static const String xyOilWarning = "/qyaq/oilWarning/list";

  //视频告警 -加油告警列表
  static const String jyOilWarning = "/qyaq/refuelingAreaWarning/list";

  //人工处理警告结果  -卸油
  static const String handleWarnXY = "/qyaq/xyTaskWarning/updTaskWarnInfo";

  //人工处理警告结果  -加油
  static const String handleWarnJY = "/qyaq/refuelingAreaWarning/modifyStatusAndHandlerResultById";

  //------巡检任务 简易版----------
  //巡检任务列表
  static const String xjTaskList = "/qyaq/rcxj/task/search1";

  // static const String xjTaskList = "/qyaq/rcxj/task/searchAll";
  //巡检任务详情
  static const String xjTaskDetail = "/qyaq/rcxj/task/get";

  //巡检任务-审核
  static const String xjTaskCheck = "/qyaq/rcxj/task/check";

  //领取巡检任务
  static const String getxjTask = "/qyaq/rcxj/task/getTheTask";

  //取消巡检任务
  static const String cancelxjTask = "/qyaq/rcxj/task/cancelTheTask";

  //巡检任务详情-巡检点列表
  static const String getxjTaskPointList = "/qyaq/rcxj/task/point/search";

  //巡检任务详情-巡检点详情
  static const String getxkTaskPointDetail = "/qyaq/rcxj/point/get";

  //巡检任务详情-巡检点列表-巡检点执行提交
  static const String subxjTaskPoint = "/qyaq/rcxj/task/point/submit";

  // start
  //巡检任务详情-巡检点列表-巡检条目-异常提交
  static const String subxjTaskPointItemException = "/qyaq/yh/addYhHiddenDanger";

  //巡检任务详情-巡检点列表-巡检条目-异常状态更改
  static const String editxjTaskPointItemException = "/qyaq/yh/update";

  //巡检任务详情-巡检点列表-巡检条目-异常信息
  static const String getxjAbnormal = "/qyaq/yh/getDetailMsg";

  //巡检任务详情-巡检点列表-巡检条目-异常删除
  static const String delxjTaskPointItemException = "/qyaq/yh/deleteDetailMsg";

  //巡检任务详情-巡检点列表-巡检条目-异常现场解决
  static const String subxjExceptionLiveResolve = "/qyaq/yh/update";

  //巡检任务详情-巡检点列表-巡检条目-异常现场解决详情获取
  static const String getxjExceptionLiveResolve = "/qyaq/rcxj/abnormalSolve/getByAbnormal";

  //end

  //巡检任务详情-巡检点列表-巡检条目
  static const String xjTaskPointItemList = "/qyaq/rcxj/task/pointItem/search";

  //巡检任务详情-巡检点列表-巡检条目 提交巡检条目执行结果
  static const String subxjTaskPointItem = "/qyaq/rcxj/task/pointItem/execute";

  //巡检条目详情
  static const String getxjTaskPointItem = "/qyaq/rcxj/item/form/get";

  //======应急模块======
  //立即报警
  static const String warnPost = "/qyaq/yj/warn/add";

  // 报警详情
  static const String warnDetails = "/qyaq/yj/warn/get";

  //报警处理
  static const String warnDisposalList = "/qb/report/yj_warn_handle_report";

  //报警处理详情
  static const String warnDisposalDetail = "/qyaq/yj/warnhandle/get";

  //现场取证措施
  static const String warnHandleplan = "/qyaq/yj/warnhandleplan/get";

  //应急会话sign
  static const String warnSessionSign = "/qyaq/imtencent/getImUserSign"; //"/qyaq/imtencent/getUserSig";
  //应急会话成员
  static const String warnMemberList = "/qyaq/yj/warngroup/searchOrgByHandleId";

  //应急会话现场处置措施
  static const String warnSessionNow = "/qyaq/yj/warnhandleplan/get";

  //应急会话所需物资
  static const String warnSessionGood = "/qyaq/yj/warnhandleplan/get";

  //匹配预案
  static const String warnDisposaltPlan = "/qyaq/yj/plan/page";

  //启动预案
  static const String warnStartPlan = "/qyaq/yj/warnhandle/appstart";

  //查询模版
  static const String disposalTemplate = "/qyaq/yj/plan/disposal-template/get";

  //新增处理预案信息
  static const String addWarnHandlePlan = "/qyaq/yj/warnhandleplan/add";
}
