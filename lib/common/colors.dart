import 'package:flutter/material.dart';

@Deprecated('Please use DefaultThemeData.ThemeData;')
class MyColors {
  static const Color title = Color(0xff3E3E3E);
  static const Color content = Color(0xff121212);
  static const Color lable = Color(0xff4A4A4A);
  static const Color colorPrimaryLight = Color(0xffC8E6C9);

  static const Color primaryText = Color(0xff212121);
  static const Color secondaryText = Color(0xff757575);

  static const Color dividerColor = Color(0xffF6F6F6);

  static const Color theme = Color(0xff2A80FF);
  static const Color blue = Color(0xff084DFD);
  static const Color blue2 = Color(0xff3d7eff);
  static const Color blue3 = Color(0xffcadcff);
  static const Color grey = Color(0xff999999);
  static const Color red = Color(0xffF95555);

  static const Color inputLine = Color(0xffEBEBEB);
  static const Color inputHint = Color(0xffCDCDCD);
  static const Color backgray = Color(0xffF6F6F6);
  static const Color grayC9 = Color(0xffC9C9C9);
  static const Color gray234 = Color(0xffF4F4F4); //234 234 234

  static const Color gray333 = Color(0xff333333);
  static const Color gray666 = Color(0xff666666);
  static const Color gray999 = Color(0xff999999);
  static const Color grayCA = Color(0xffcacaca);
  static const Color grayCA2 = Color(0xffCACACA);
  static const Color grayDf = Color(0xffDFDFDF);

  //课程与考试考试
  static const Color examRight = Color.fromARGB(255, 94, 202, 64);
  static const Color examError = Color.fromARGB(255, 236, 113, 106);
  static const Color examUndo = Color.fromARGB(255, 39, 66, 135);
  static const Color examUnd02 = Color(0xffEBEBED);
}
