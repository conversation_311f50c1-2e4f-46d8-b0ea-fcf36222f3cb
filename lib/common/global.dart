class Global {
  static String qbApplicationId = '10500000000000000000000000000000';
  static bool loginIsEncrypt = false;
  static dynamic userInfo;
  static String authorization = '';
  static String username = '';
  static List role = [];
  // static bool isDepartmentHead = Global.role == '部门负责人';
  static String name = '';
  static String userName = '';
  static String scanType = '1'; //排查任务扫描方式 //1：nfc，2：二维码
  static String departmentFlag = '';
  static String userFlag = '';
  static String departmentId = '';
  static String departmentName = '';
  static List menusList = [];
  static dynamic menus = {
    "menus": [
      {
        // "id" : "87e4dc9ce70e4c0cb33265d9995cda43",
        // "ord": 0,
        "url": "/warehouse_keeper_sale_list",
        "icon": "assets/images/module/other/warehouse_keeper_sale.png",
        "name": "销售出库",
        "type": "kuguanyuan",
        // "appId": "10500000000000000000000000000000",
        // "parentId": "72e50733e3174ba08a1b6fdb330b24d0",
        // "createTime": 1663902574000
      },
      {
        "url": "/warehouse_keeper_purchase_list",
        "icon": "assets/images/module/other/warehouse_keeper_purchase.png",
        "name": "采购入库",
        "type": "kuguanyuan",
      },
      {
        "url": "/entrance_guard_list",
        "icon": "assets/images/module/other/entrance_guard_list.png",
        "name": "检车",
        "type": "menwei",
      },
      {
        "url": "/quality_testing_sale_list",
        "icon": "assets/images/module/other/quality_testing_sale.png",
        "name": "销售质检",
        "type": "quyangyuan",
      },
      {
        "url": "/quality_testing_purchase_list",
        "icon": "assets/images/module/other/quality_testing_purchase.png",
        "name": "采购质检",
        "type": "quyangyuan",
      },
      {
        "url": "/user_manage",
        "icon": "assets/images/module/other/user_manage.png",
        "name": "用户管理",
        "type": "admin",
      },
      {
        "url": "/question_manage",
        "icon": "assets/images/module/other/question_manage.png",
        "name": "题目管理",
        "type": "all",
      },
    ]
  }; //菜单
  static String entId = '';
  static String entName = '';
  static String userId = '';
  static String systemRole = ''; //系统角色
  static String appVersion = '';
  static String serviceVersion = '';
  static int appVersionCode = 0;
  static bool YJMaster = false; //应急负责人权限

  static void reset() {
    authorization = '';
    loginIsEncrypt = false;
    List role = [];
    name = '';
    userName = '';
    scanType = '1'; //排查任务扫描方式 //1：nfc，2：二维码
    departmentFlag = '';
    userFlag = '';
    departmentId = '';
    departmentName = '';
    menus = {
      "menus": [
        {
          // "id" : "87e4dc9ce70e4c0cb33265d9995cda43",
          // "ord": 0,
          "url": "/warehouse_keeper_sale_list",
          "icon": "assets/images/module/other/warehouse_keeper_sale.png",
          "name": "销售出库",
          "type": "kuguanyuan",
          // "appId": "10500000000000000000000000000000",
          // "parentId": "72e50733e3174ba08a1b6fdb330b24d0",
          // "createTime": 1663902574000
        },
        {
          "url": "/warehouse_keeper_purchase_list",
          "icon": "assets/images/module/other/warehouse_keeper_purchase.png",
          "name": "采购入库",
          "type": "kuguanyuan",
        },
        {
          "url": "/entrance_guard_list",
          "icon": "assets/images/module/other/entrance_guard_list.png",
          "name": "检车",
          "type": "menwei",
        },
        {
          "url": "/quality_testing_sale_list",
          "icon": "assets/images/module/other/quality_testing_sale.png",
          "name": "销售质检",
          "type": "quyangyuan",
        },
        {
          "url": "/quality_testing_purchase_list",
          "icon": "assets/images/module/other/quality_testing_purchase.png",
          "name": "采购质检",
          "type": "quyangyuan",
        },
      ]
    }; //菜单
    entId = '';
    entName = '';
    userId = '';
    systemRole = ''; //系统角色
  }
}
