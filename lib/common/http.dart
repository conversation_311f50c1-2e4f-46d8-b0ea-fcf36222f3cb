import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/utils/Log.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../dnc/logger/LoggerManager.dart';
import 'ServiceManager.dart';
import 'Urls.dart';

//import 'package:webview_cookie_manager/webview_cookie_manager.dart';
typedef Success = void Function(dynamic json);
typedef Fail = void Function(String reason, dynamic code);
typedef After = void Function();
//https://javiercbk.github.io/json_to_dart/
//https://flutterstudio.app/

//HTTP请求工具类
class Http {
  static const Timeout = Duration(seconds: 30);
  Dio? _dio;

  Http() {
    _dio = createDio();
  }

  Future<String?> getAuthorization() async {
    String? authorization;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    authorization = prefs.getString("Authorization");
    return authorization;
  }

  Dio createDio() {
    // print('======================createDio======================');
    // print('baseHost: ${ServiceManager.currentHost}');
    var dio = Dio(BaseOptions(
      connectTimeout: Timeout,
      receiveTimeout: Timeout,
      sendTimeout: Timeout,
      baseUrl: '${ServiceManager.currentHost}/api',
      responseType: ResponseType.json,
    ));

//    CookieJar cookieJar = CookieJar();
//    dio.interceptors.add(CookieManager(cookieJar));
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          // print("\n================================= 请求数据 =================================");
          //  print("method = ${options.method.toString()}");
          //  print("headers = ${options.headers}");
          //  print("params = ${options.queryParameters}");
          //  print("data = ${options.data}");
          //  print("url = ${options.uri.toString()}");

          print("\n================================= headers =================================");
          options.headers['QbApplicationId'] = Global.qbApplicationId;
          if (!DU.isStrEmpty(Global.authorization)) {
            options.headers["Authorization"] = Global.authorization;
            print('Authorization: ${Global.authorization}');
          }
          if (!DU.isStrEmpty(Global.entId) &&
              DU.isStrEmpty(options.queryParameters['entId'])) {
            options.queryParameters['entId'] = Global.entId;
          }

          handler.next(options);
          //old
          // Future<dynamic> future = Future(() async {
          //   SharedPreferences prefs = await SharedPreferences.getInstance();
          //   return prefs.getString("Authorization");
          // });
          // future.then((value) {
          //   options.headers["Authorization"] = value;
          //   return handler.next(options);
          // }); // unlock the dio
        },
        onResponse: (response, handler) {
          LoggerManager().d('[method] ${response.requestOptions.method}\n'
              '[url] ${response.realUri}\n'
              '[param] ${response.requestOptions.queryParameters}\ n'
              '[post] ${response.requestOptions.data}\n'
              '[response] ${response.data}\n');
          // print("\n================================= 响应数据开始 =================================");
          //   print("response = ${response}");
          // print("code = ${response.statusCode}");
          // print("data = ${response.data}");
          // print("================================= 响应数据结束 =================================\n");
          return handler.next(response); // continue
        },
        onError: (DioException e, handler) {
          LoggerManager().e('[method] ${e.requestOptions.method}\n'
              '[url] ${e.requestOptions.uri}\n'
              '[param] ${e.requestOptions.queryParameters}\ n'
              '[post] ${e.requestOptions.data}\n'
              '[response] ${e.toString()}\n');
          // print("\n=================================错误响应数据 =================================");
          // print(e);
          return handler.next(e); //continue
        },
      ),
    );

    //抓包代理
    // (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
    //   // config the http client
    //   client.findProxy = (uri) {
    //     //proxy all request to localhost:8888
    //     return 'PROXY ************:8888';
    //     // return 'PROXY *************:8888';
    //   };
    //   // you can also create a HttpClient to dio
    //   // return HttpClient();
    // };

    return dio;
  }

//GET
  Future get(String uri, Map<String, dynamic>? params,
      {Success? success, Fail? fail, After? after, bool loading = true}) async {
    try {
      if (loading) {
        Loading.show();
      }
      Response response = await _dio!.get(uri, queryParameters: params);
      Map<String, dynamic> result = jsonDecode(response.toString());
      print(
          "=================================数据请求(get) =================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      print("[params] $params");
      // print("response = ${result}");
      L.d('[response] $result');
      // if (result["code"] == 200 && result["success"]) {
      //   if (success != null) {
      //     success(result["data"]);
      //   } else {}
      // } else {
      //   if (fail != null) {
      //     fail(result["msg"], result["code"]);
      //   }
      // }

      if (result["code"] == 0) {
        if (success != null) {
          success(result["data"]);
        }
      } else {
        if (fail != null) {
          fail(result["msg"], result["code"]);
        }
      }
      if (after != null) {
        after();
      }

      return result["data"];
    } on DioException catch (e) {
      print(
          "=================================数据请求(get) error=================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      print("[params] $params");
      print("[response] ${e.response}");
      String errorMsg = '';
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.unknown) {
        errorMsg = '网络错误,请稍后重试';
      } else {
        final result = e.response?.data;
        if (result != null) {
          if (result is String) {
            errorMsg = DU.safeString(jsonDecode(result), ['msg']);
          } else {
            errorMsg = DU.safeString(result, ['msg']);
          }
        } else {
          errorMsg = e.message ?? e.type.toString();
        }
      }
      if (fail != null) {
        fail(errorMsg, e.type);
      }
    } finally {
      if (loading) {
        Loading.dismiss();
      }
      print(
          "=====get finally=============================================================================");
    }
  }

  //POST
  Future post(String uri, Map<String, dynamic> params,
      {Success? success, Fail? fail, After? after, bool loading = true}) async {

    try {
      Response response;
      if (loading) {
        Loading.show();
      }
      // if (uri == Urls.login || uri == Urls.bpmClaim || uri == Urls.bpmUnClaim) {
      //   print('Urls1:${Urls.login}');
      //   print('Urls11:${uri}');
      //   //登录
      //   response = await _dio!.post(uri, queryParameters: params);
      // //   // options: Options(
      // //   //   headers: {
      // //   //     Headers.contentLengthHeader: 800, // 设置content-length   queryParameters
      // //   //   },
      // //   // )
      // } else {
        print('Urls2:$Urls');
        response = await _dio!.post(uri, data: params);
      // }
      print(
          "=================================数据请求(post) =================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      L.d('[params]  $params');
      L.d('[response] $response');
      // print("[params]    ${params}");
      // print("[response]  ${response}");

      Map<String, dynamic> result = jsonDecode(response.toString());

      // if (result["code"] == 200 && result["success"]) {
      //   if (success != null) {
      //     success(result["data"]);
      //   }
      // } else {
      //   if (fail != null) {
      //     fail(result["msg"], result["code"]);
      //   }
      // }
      //新
      if (result["code"] == 0) {
        if (success != null) {
          success(result["data"]);
        }
      } else {
        if (fail != null) {
          fail(result["msg"], result["code"]);
        }
      }
      if (after != null) {
        after();
      }
      return result["data"];
    } on DioException catch (e) {
      print(
          "=================================数据请求(post) error=================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      L.d('[params] $params');
      print('response: ${e.response}');
      print(e.message);
      String errorMsg = '';
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.unknown) {
        errorMsg = '网络错误,请稍后重试';
      } else {
        final result = e.response?.data;
        print('result: $result');
        if (result != null) {
          if (result is String) {
            errorMsg = DU.safeString(jsonDecode(result), ['msg']);
          } else {
            errorMsg = DU.safeString(result, ['msg']);
          }
        } else {
          errorMsg = e.message ?? e.type.toString();
        }
      }
      if (fail != null) {
        fail(errorMsg, e.type.name);
      }
    } finally {
      if (loading) {
        Loading.dismiss();
      }
      print(
          "====post finaly==============================================================================");
    }
  }

  //数据字典
  Future dict(String uri, Map<String, dynamic> params,
      {Success? success, Fail? fail, After? after, bool loading = true}) async {
    try {
      if (loading) {
        Loading.show();
      }
      Response response;

      response = await _dio!.get(uri);
//      print("jsonDecode:");
//      print(jsonDecode(response.toString()));
      Map<String, dynamic> result = jsonDecode(response.toString());
      print(
          "=================================数据请求(dict) =================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      L.d("[response] $response");

      if (result["code"] == 0) {
        if (success != null) {
          success(result["data"]["items"]);
        }
      } else {
        if (fail != null) {
          fail(result["msg"], result["code"]);
        }
      }
      if (after != null) {
        after();
      }
      return result["data"]["items"];
    } on DioException catch (e) {
      //catch 提示  error.type == DioExceptionType.CONNECT_TIMEOUT
      // print("catch e = ${e}");
      // print(e);
      print(
          "=================================数据请求(dict) error=================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      print("[params] $params");
      print('---------------------');
      print(e);
      Map<String, dynamic> result = jsonDecode(e.response.toString());
      print(result);
      if (fail != null) {
        fail(result['msg'], result['code']);
      }
    } finally {
      if (loading) {
        Loading.dismiss();
      }
      print(
          "===dict finally==================================================================================");
    }
  }

  //数据字典
  Future dictGet(String uri, Map<String, dynamic> params,
      {Success? success, Fail? fail, After? after, bool loading = true}) async {
    print(uri);
    try {
      if (loading) {
        Loading.show();
      }
      Response response;

      response = await _dio!.get(uri, queryParameters: params);
//      print("jsonDecode:");
//      print(jsonDecode(response.toString()));
      Map<String, dynamic> result = jsonDecode(response.toString());
      print(
          "=================================数据请求(dict2) =================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      print("[params] $params");
      print("[response] $response");
      if (result["code"] == 200 && result["success"]) {
        if (success != null) {
          // print("result[data:");
          // print(result["data"]);
          success(result["data"]);
        }
      } else {
        if (fail != null) {
          fail(result["msg"], result["code"]);
        }
      }
      if (after != null) {
        after();
      }
      return result["data"];
    } on DioException catch (e) {
      print(
          "=================================数据请求(dict2) error=================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      print("[params] $params");
      print('---------------------');
      print(e);
      //catch 提示  error.type == DioExceptionType.CONNECT_TIMEOUT
      // print(" //catch 提示" + "e = ${e}");
      // print(e);

      Map<String, dynamic> result = jsonDecode(e.response.toString());
      print(result);
      if (fail != null) {
        fail(result['msg'], result['code']);
      }
    } finally {
      if (loading) {
        Loading.dismiss();
      }
      print(
          "===dict2 finally==================================================================================");
    }
  }

  //patch
  Future patch(String uri,
      {Success? success, Fail? fail, After? after, bool loading = true}) async {
    var dio = Dio();
    final url = '${ServiceManager.currentHost}/api' + uri + '/read' ; // 构建完整的 URL
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': Global.authorization,
    };
    try {
        print('patch---url:${url}');
        await dio.patch(url,
        options: Options(headers: headers),
      );
    } catch (e) {
      print('PATCH Error: $e');
    }
  }
  //patch2
  Future patchs(String uri,Map<String, dynamic> params,
      {Success? success, Fail? fail, After? after, bool loading = true}) async {
    var dio = Dio();
    final url = '${ServiceManager.currentHost}/api' + uri ; // 构建完整的 URL
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': Global.authorization,
    };
    try {
      print('patch---url:${url}');
      print('patch---params:${params}');
      await dio.patch(url,data: params,
        options: Options(headers: headers),
      );
    } catch (e) {
      print('PATCH Error: $e');
    }
  }
}
