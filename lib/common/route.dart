import 'package:ent_secutity_app/UI/pages/car_inspect/InspectListPage.dart';
import 'package:ent_secutity_app/UI/pages/meetings/MeetingsListPage.dart';
import 'package:ent_secutity_app/UI/pages/warehouse_keeper_sale/WarehouseKeeperSaleListPage.dart';
import 'package:ent_secutity_app/UI/pages/warehouse_keeper_purchase/WarehouseKeeperPurchaseListPage.dart';
import 'package:ent_secutity_app/UI/pages/quality_testing_sale/QualityTestingSaleListPage.dart';
import 'package:ent_secutity_app/UI/pages/quality_testing_purchase/QualityTestingPurchaseListPage.dart';
import '../UI/pages/home/<USER>';
import '../UI/pages/login/LoginPage.dart';
import 'package:ent_secutity_app/UI/pages/center/UserManagePage.dart';
import 'package:ent_secutity_app/UI/pages/question/QuestionManagePage.dart';

//配置路由
final routes = {
  "/login": (context) => LoginPage(),
  //登录
  "/home": (context) => HomePage(),
  //库管员销售
  "/warehouse_keeper_sale_list": (context, {arguments}) =>
      WarehouseKeeperSaleListPage(),
  //库管员采购
  "/warehouse_keeper_purchase_list": (context, {arguments}) =>
      WarehouseKeeperPurchaseListPage(),
  //质检员销售  quality testing
  "/quality_testing_sale_list": (context, {arguments}) =>
      QualityTestingSaleListPage(),
  //质检员采购
  "/quality_testing_purchase_list": (context, {arguments}) =>
      QualityTestingPurchaseListPage(),
  // 会议管理
  "/meetings_list": (context, {arguments}) => MeetingsListPage(),
  //门卫
  "/entrance_guard_list": (context, {arguments}) => InspectListPage(),
  "/user_manage": (context, {arguments}) => UserManagePage(),
  "/question_manage": (context, {arguments}) => QuestionManagePage(),
};
