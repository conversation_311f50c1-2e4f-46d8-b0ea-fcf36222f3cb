import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:ent_secutity_app/common/AppConfig.dart';
import 'package:ent_secutity_app/common/ServiceManager.dart';

import '../dnc/utils/Log.dart';

//import 'package:webview_cookie_manager/webview_cookie_manager.dart';
typedef Success = void Function(dynamic json);
typedef Fail = void Function(String reason, int code);
typedef After = void Function();
//https://javiercbk.github.io/json_to_dart/
//https://flutterstudio.app/

//文件上传工具类
class UploadDio {
  static Dio? _dio;
  static UploadDio https = UploadDio();

  static UploadDio getInstance() {
    return https;
  }

  UploadDio() {
    // if (_dio == null) {
    _dio = createDio();
    // }
  }

  Dio createDio() {
    var dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 15),
      sendTimeout: const Duration(seconds: 15),
      baseUrl: AC.useTestUpload ? 'http://**************:9309' : ServiceManager.currentHost,
      responseType: ResponseType.json,
    ));
    dio.interceptors.add(InterceptorsWrapper(onRequest: (options, handler) {
      return handler.next(options);
    }, onResponse: (response, handler) {
      return handler.next(response); // continue
    }, onError: (DioException e, handler) {
      print(" DioException  = $e");
      return handler.next(e); //continue
    }));
    return dio;
  }

  Future post(String uri, Map<String, dynamic> params, {Success? success, Fail? fail, After? after}) async {
    try {
      Response response;
      FormData formData = FormData.fromMap(params);
      response = await _dio!.post(uri, data: formData);
      print("=================================数据请求(Upload) =================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      print("[params] ${(params['file'] as MultipartFile).filename}");
      L.d('[response] $response');
      Map<String, dynamic> result = jsonDecode(response.toString());
      if (result["code"] == 200 && result["success"]) {
        if (success != null) {
          success(result["data"]);
        } else {}
      } else {
        if (fail != null) {
          fail(result["msg"], result["code"]);
        }
      }
      if (after != null) {
        after();
      }
      return result["data"];
    } on DioException catch (e) {
      print("=================================数据请求(Upload error) =================================");
      print("[uri] ${_dio!.options.baseUrl}$uri");
      L.d('[response] ${e.response}');
      Map<String, dynamic> result = jsonDecode(e.response.toString());
      if (fail != null) {
        fail(result['msg'], result['code']);
      }
    } finally {}
  }
}
