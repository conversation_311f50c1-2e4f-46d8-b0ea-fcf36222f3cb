import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:ent_secutity_app/dnc/logger/LoggerManager.dart';
import 'package:ent_secutity_app/dnc/network/Request.dart';
import 'package:ent_secutity_app/dnc/utils/Log.dart';
import 'package:ent_secutity_app/main.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart' as picker;

import '../../common/AppConfig.dart';
import '../../common/ServiceManager.dart';
import '../../common/global.dart';
import '../utils/DataUtils.dart';
import '../widget/components/File.dart';
import '../widget/components/Pop.dart';
import '../widget/components/Widget.dart';

class Http {
  static const _Timeout = Duration(seconds: 30);
  static const String? _Proxy = null;

  // static const String? _Proxy = '192.168.2.126:8888';//'192.168.2.126:8888'
  // static const String? _Proxy = '192.168.3.80:8888'; //'192.168.3.199:8888';
  Dio _createAPI(RequestType requestType,
      {required ResponseType responseType, bool? needEntId}) {
    // final authorization = Global.authorization;
    final headers = {'QbApplicationId': Global.qbApplicationId};
    if (!DU.isStrEmpty(Global.authorization)) {
      headers['Authorization'] = Global.authorization;
      // print('111111---Authorization: ${Global.authorization}');
    }
    var dio = Dio(
      BaseOptions(
          connectTimeout: _Timeout,
          receiveTimeout: _Timeout,
          sendTimeout: _Timeout,
          baseUrl: ServiceManager.currentHost,
          responseType: responseType,
          headers: headers,
          // receiveDataWhenStatusError: true,
          validateStatus: (int? code) {
            return true;
          }),
    );
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          options.path = '/api${options.path}';
          if (requestType == RequestType.GET && (needEntId ?? true)) {
            if (!DU.isStrEmpty(Global.entId) &&
                DU.isStrEmpty(options.queryParameters['entId'])) {
              options.queryParameters['entId'] = Global.entId;
            }
          }
          handler.next(options);
        },
        onResponse: (response, handler) {
          print(
              "================================= 请求数据 =================================");
          print('[method] ${response.requestOptions.method}');
          print('[url] ${response.realUri}');
          L.d('[param] ${response.requestOptions.queryParameters}');
          if (response.requestOptions.method == 'POST') {
            L.d('[post] ${response.requestOptions.data} ');
          }
          L.d('[response] ${response.data} ');
          print(
              "==========================================================================");
          LoggerManager().d('[method] ${response.requestOptions.method}\n'
              '[url] ${response.realUri}\n'
              '[param] ${response.requestOptions.queryParameters}\ n'
              '[post] ${response.requestOptions.data}\n'
              '[response] ${response.data}\n');
          final result = response.data;

          if (responseType == ResponseType.json) {
            if (result["code"] != 0 ) {
              final msg = DU.safeString(result, ['msg']);
              Toast.show(msg);
              handler.reject(DioException(
                  requestOptions: response.requestOptions, message: msg));
            } else {
              print('222');
              handler.next(response);
            }
          } else {
            handler.next(response);
          }
        },
        onError: (DioException e, handler) {
          print(
              '=================================请求数据错误 =================================');
          print('[method] ${e.requestOptions.method}');
          print('[url] ${e.requestOptions.uri}');
          print('[param] ${e.requestOptions.queryParameters} ');
          if (e.requestOptions.method == 'POST') {
            print('[post] ${e.requestOptions.data} ');
          }
          print('[response] ${e.toString()}');
          LoggerManager().e('[method] ${e.requestOptions.method}\n'
              '[url] ${e.requestOptions.uri}\n'
              '[param] ${e.requestOptions.queryParameters}\ n'
              '[post] ${e.requestOptions.data}\n'
              '[response] ${e.toString()}\n');
          if (e.type == DioExceptionType.connectionTimeout ||
              e.type == DioExceptionType.sendTimeout ||
              e.type == DioExceptionType.receiveTimeout ||
              e.type == DioExceptionType.connectionError ||
              e.type == DioExceptionType.unknown) {
            Toast.show('网络错误,请稍后重试');
          } else if (e.response != null) {
            print('[response.data] ${e.response?.data} ');
            Map<String, dynamic> result;
            if (e.response?.data is Map<String, dynamic>) {
              result = e.response!.data;
            } else {
              result = jsonDecode(e.response!.data);
            }
            // print('result: $result');
            if (result["code"] == 403) {
              final msg = DU.safeString(result, ['msg']);
              Toast.show(msg);
              if (MyApp.routeObserver.navigator != null) {
                SDialog.showInfo(MyApp.routeObserver.navigator!.context,
                    title: '错误', info: '认证失败,请重新登录', onCommand: () {
                      Global.reset();
                      Navigator.pushNamedAndRemoveUntil(
                          MyApp.routeObserver.navigator!.context,
                          '/login',
                              (route) => false);
                    });
              }
            }
            String msg = DU.safeString(result, ['msg']);
            if (msg.isEmpty) {
              msg = DU.safeString(result, ['status']) +
                  '  ' +
                  DU.safeString(result, ['message']);
            }
            Toast.show(msg);
          } else {
            Toast.show(e.message);
          }
          print(
              "============================================================================");
          handler.next(e); //continue
        },
      ),
    );
    if (!DU.isStrEmpty(_Proxy)) {
      setProxy(dio: dio, host: _Proxy!);
    }
    return dio;
  }

  Dio _createSource() {
    // final authorization = Global.authorization;
    final headers = {'QbApplicationId': Global.qbApplicationId};
    if (!DU.isStrEmpty(Global.authorization)) {
      headers['authorization'] = Global.authorization;
      // print('Authorization: ${Global.authorization}');
    }
    var dio = Dio(
      BaseOptions(
        connectTimeout: _Timeout,
        receiveTimeout: _Timeout,
        sendTimeout: _Timeout,
        responseType: ResponseType.bytes,
        headers: headers,
      ),
    );
    dio.interceptors.add(
      InterceptorsWrapper(
        onResponse: (response, handler) {
          print(
              "================================= 请求数据 =================================");
          print('[url] ${response.realUri}');
          L.d('[response] ${response.data.length} ');
          print(
              "==========================================================================");
          handler.next(response);
        },
        onError: (DioException e, handler) {
          print(
              '=================================请求数据错误 =================================');
          print('[url] ${e.requestOptions.uri}');
          print('[response] ${e.toString()}');
          if (e.type == DioExceptionType.connectionTimeout ||
              e.type == DioExceptionType.sendTimeout ||
              e.type == DioExceptionType.receiveTimeout ||
              e.type == DioExceptionType.connectionError ||
              e.type == DioExceptionType.unknown) {
            Toast.show('网络错误,请稍后重试');
          } else if (e.response != null) {
            print('[response.data] ${e.response?.data} ');
            Map<String, dynamic> result;
            if (e.response?.data is Map<String, dynamic>) {
              result = e.response!.data;
            } else {
              result = jsonDecode(e.response!.data);
            }
            // print('result: $result');
            if (result["code"] == 403) {
              final msg = DU.safeString(result, ['msg']);
              Toast.show(msg);
              if (MyApp.routeObserver.navigator != null) {
                SDialog.showInfo(MyApp.routeObserver.navigator!.context,
                    title: '错误', info: '认证失败,请重新登录', onCommand: () {
                      Global.reset();
                      Navigator.pushNamedAndRemoveUntil(
                          MyApp.routeObserver.navigator!.context,
                          '/login',
                              (route) => false);
                    });
              }
            }
            String msg = DU.safeString(result, ['msg']);
            if (msg.isEmpty) {
              msg = DU.safeString(result, ['status']) +
                  '  ' +
                  DU.safeString(result, ['message']);
            }
            Toast.show(msg);
          } else {
            Toast.show(e.message);
          }
          print(
              "============================================================================");
          handler.next(e); //continue
        },
      ),
    );
    if (!DU.isStrEmpty(_Proxy)) {
      setProxy(dio: dio, host: _Proxy!);
    }
    return dio;
  }

  Dio _createUpload() {
    final headers = {'Authorization': Global.authorization};
    // 'QbApplicationId': Global.qbApplicationId
    // print(Global.authorization);
    // if (!DU.isStrEmpty(Global.authorization)) {
    //   // headers['authorization'] = Global.authorization;
    //   headers['Authorization'] = Global.authorization;
    //
    // }
    print('Global:');
    print(headers);
    var dio = Dio(
      BaseOptions(
        connectTimeout: _Timeout,
        receiveTimeout: _Timeout,
        sendTimeout: Duration(seconds: 120),
        baseUrl:
        // true//用这行和下一行切换保险，记得改回来就行。
        kDebugMode && AC.useTestUpload
            ? 'http://***************:9000'
        // ? 'http://**************:9309'
            : ServiceManager.currentHost,
        responseType: ResponseType.json,
        headers: headers,
      ),
    );
    dio.interceptors.add(InterceptorsWrapper(onRequest: (options, handler) {
      handler.next(options);
    }, onResponse: (response, handler) {
      print(
          "================================= 上传图片成功 =================================");
      print('[response] ${response.data} ');
      print(
          "==========================================================================");
      handler.next(response); // continue
    }, onError: (DioException e, handler) {
      print(
          '=================================上传图片失败 =================================');
      print('[url] ${e.requestOptions.method} ${e.requestOptions.uri}');
      print('[response] ${e.toString()}');
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.connectionError ||
          e.type == DioExceptionType.unknown) {
        Toast.show('网络错误,请稍后重试');
      } else if (e.response != null &&
          e.response!.data is Map<String, dynamic>) {
        print('[response.data] ${e.response?.data} ');
        Map<String, dynamic> result =
            e.response!.data; //jsonDecode(e.response.toString());
        final msg = DU.safeString(result, ['msg']);
        Toast.show(msg);
      } else {
        Toast.show(e.message);
      }
      print(
          "============================================================================");
      handler.next(e); //continue
    }));
    if (!DU.isStrEmpty(_Proxy)) {
      setProxy(dio: dio, host: _Proxy!);
    }
    return dio;
  }

  void setProxy({required Dio dio, required String host}) {
    dio.httpClientAdapter = IOHttpClientAdapter(
      createHttpClient: () {
        final client = HttpClient();
        client.findProxy = (uri) {
          print('PROXY $host');
          return 'PROXY $host';
        };
        return client;
      },
    );

    // (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate =
    //     (client) {
    //   client.findProxy = (uri) {
    //     return 'PROXY $host';
    //   };
    //   return null;
    //   // you can also create a HttpClient to dio
    //   // return HttpClient();
    // };
  }

// api ===========================================================================
  Future<dynamic> request(Request request, {bool? showLoading}) async {
    Response response;
    final bool show = showLoading ?? false;
    if (show) {
      Loading.show();
    }
    final params = request.params != null ? request.params!() : null;
    final post = request.post != null ? request.post!() : null;
    try {
      if (request.type == RequestType.GET) {
        response = await _createAPI(
          request.type,
          responseType: request.responseType,
          needEntId: request.needEntId,
        ).get(request.uri, queryParameters: params);
      } else if (request.type == RequestType.POST) {
        // (request.type == RequestType.POST)
        response =
        await _createAPI(request.type, responseType: request.responseType)
            .post(request.uri, queryParameters: params, data: post);
      } else {
        response =
        await _createAPI(request.type, responseType: request.responseType)
            .put(request.uri, queryParameters: params, data: post);
      }
    } on DioException catch (e) {
      // if (request.interceptor != null) {
      //   return request.interceptor!(e);
      // } else {
      //   return e;
      // }
      print(
          '============ catch错误 ============ \n $e \n============== End ==============');
      return e;
    } finally {
      if (show) {
        Loading.dismiss();
      }
    }
    if (request.responseType == ResponseType.json) {
      final result = response.data['data'];
      if (request.interceptor != null) {
        return request.interceptor!(result);
      } else {
        return result;
      }
    } else {
      return response.data;
    }
  }

  Future<dynamic> api({
    required RequestType type,
    required String uri,
    Map<String, dynamic>? params,
    dynamic post,
    void Function(dynamic result)? onSuccess,
    void Function(String msg)? onError,
    ResponseType? responseType,
    bool? showLoading,
  }) async {
    Response? response;
    final bool show = showLoading ?? false;
    if (show) {
      Loading.show();
    }
    try {
      responseType ??= ResponseType.json;
      if (type == RequestType.GET) {
        response = await _createAPI(type, responseType: responseType)
            .get(uri, queryParameters: params);
      } else if (type == RequestType.POST) {
        response = await _createAPI(type, responseType: responseType)
            .post(uri, queryParameters: params, data: post);
      } else if (type == RequestType.PUT) {
        response = await _createAPI(type, responseType: responseType)
            .put(uri, queryParameters: params, data: post);
      }
    } on DioException catch (e) {
      if (onError != null) {
        onError(e.message ?? '未知错误');
      }
      return e;
    } finally {
      if (show) {
        Loading.dismiss();
      }
    }

    dynamic result;
    if (responseType == ResponseType.json) {
      final Map<String, dynamic> responseData =
          response?.data ?? {}; //jsonDecode(response.toString())['data'];
      result = responseData['data'];
    } else {
      result = response?.data;
      // print("000099999987777");
    }

    if (response != null) {}
    if (onSuccess != null) {
      onSuccess(result);
    }
    return result;
  }

  Future<List<T>?> getSources<T>({
    required List<String> urls,
    bool showLoading = false,
  }) async {
    if (showLoading) {
      Loading.show();
    }
    List<T> datas = [];
    for (String url in urls) {
      final data = await getSource<T>(url: url);
      if (data == null) {
        return null;
      }
      datas.add(data);
    }
    if (showLoading) {
      Loading.dismiss();
    }
    return datas;
  }

  Future<T?> getSource<T>({
    required String url,
    bool showLoading = false,
  }) async {
    if (showLoading) {
      Loading.show();
    }
    Response? response;
    try {
      response = await _createSource().get(url);
    } on DioException catch (e) {
      print(e);
      return null;
    } finally {
      if (showLoading) {
        Loading.dismiss();
      }
    }
    return response.data;
  }

  // upload ===========================================================================
  static int isUploading = 0;

  Future<bool> uploadWidgetList({
    required WidgetList<FileData> wl,
    required String tag
  }) async {
    if (isUploading > 0) return false;
    isUploading++;

    final List<FileData> fileDataList = [];
    for (FileData data in wl.list) {
      if (data.url == null && (data.data is picker.AssetEntity || data.imgData != null)) {
        fileDataList.add(data);
      }
    }

    if (fileDataList.isEmpty) {
      isUploading = 0;
      return true; // 无需上传的文件
    }

    List<String> allUploadedUrls = [];
    for (FileData fileData in fileDataList) {
      Uint8List? byteData;

      if (fileData.imgData != null && fileData.imgData!.isNotEmpty) {
        byteData = await FlutterImageCompress.compressWithList(
          fileData.imgData!,
          minHeight: 1920,
          minWidth: 1080,
          quality: 95,
        );
      } else if (fileData.data != null && fileData.data is picker.AssetEntity) {
        final u8Data = await fileData.data.originBytes;
        if (u8Data != null && u8Data.isNotEmpty) {
          byteData = await FlutterImageCompress.compressWithList(
            u8Data,
            minHeight: 1920,
            minWidth: 1080,
            quality: 95,
          );
        }
      }

      if (byteData != null) {
        final file = MultipartFile.fromBytes(
            byteData,
            filename: '${tag}_${DateTime.now().millisecondsSinceEpoch.toString()}.jpeg'
        );

        final formData = FormData.fromMap({'file': file});
        try {
          final response = await _createUpload().post('/api/upload/file', data: formData);
          if (response.data != null && response.data['data'] != null && response.data['data']['url'] != null) {
            String fullUrl = 'http://***************:9000' + response.data['data']['url'];
            allUploadedUrls.add(fullUrl);
          }
        } on DioException catch (e) {
          print('图片上传失败: ${e.toString()}');
          // 可以选择在这里继续或停止，这里选择继续其他文件的上传
        }
      }
    }

    // 更新已上传文件的URL
    for (int i = 0; i < fileDataList.length; i++) {
      if (i < allUploadedUrls.length) {
        fileDataList[i].url = allUploadedUrls[i];
      }
    }

    isUploading = 0;
    return allUploadedUrls.length == fileDataList.length; // 如果所有文件都成功上传，返回 true
  }

  Future<dynamic> uploadMultipartFiles({
    required List<MultipartFile> files,
    required String tag,
    bool isBlock = true,
  }) async {
    print('=========uploadMultipartFiles===========');
    ValueNotifier<double> value = ValueNotifier<double>(0);
    if (isBlock) Progress.show(value);
    var formData = FormData.fromMap({'files': files});
    Response response;
    try {
      response = await _createUpload().post('/api/upload/file', data: formData,
          onSendProgress: (int count, int total) {
            value.value = count / total * 0.99;
            // print('upload progress: ${value.value}');
          });
    } on DioException catch (e) {
      print('图片上传失败: ${e.toString()}');
      Toast.show(e.message);
      Toast.show('图片上传失败,请重新尝试');
      return e;
    } finally {
      value.value = 1;
      if (isBlock) Progress.dismiss();
    }
    final urls = DU.safeList<String>(response.data, ['data']);
    return urls;
  }

  Future<bool> uploadWidgetList1(
      {required WidgetList<FileData> wl, required String tag}) async {
    final List<FileData> fileDataList = [];
    for (FileData data in wl.list) {
      if (data.url == null && data.data is picker.AssetEntity) {
        fileDataList.add(data);
      } else if (data.url == null && data.imgData != null) {
        fileDataList.add(data);
      }
    }
    if (fileDataList.isEmpty) {
      return true;
    }

    final result = await uploadAssetEntities(
        assets: DU.transformList<picker.AssetEntity, FileData>(
            fileDataList, (data) => data.data),
        tag: tag);
    if (result is DioException || result is! List) {
      return false;
    }
    for (int i = 0; i < fileDataList.length; i++) {
      fileDataList[i].url = result[i];
    }
    return true;
  }

  Future<dynamic> uploadAssetEntities(
      {required List<picker.AssetEntity> assets,
        required String tag,
        bool isBlock = true}) async {
    print('=========uploadAssetEntities===========');
    ValueNotifier<double> value = ValueNotifier<double>(0);
    if (isBlock) Progress.show(value);
    final List<MultipartFile> fileList = [];
    if (assets.isEmpty) {
      return [];
    }
    for (picker.AssetEntity asset in assets) {
      if (asset.type == picker.AssetType.image) {
        final u8Data = await asset.originBytes;
        if (u8Data == null) {
          continue;
        }
        final compressData = await FlutterImageCompress.compressWithList(
          u8Data,
          minHeight: 1920,
          minWidth: 1080,
          quality: 95,
        );
        final file = MultipartFile.fromBytes(compressData,
            filename:
            '${tag}_${DateTime.now().millisecondsSinceEpoch.toString()}.jpeg');
        fileList.add(file);
      } else if (asset.type == picker.AssetType.video) {
        final u8Data = await asset.originBytes;
        if (u8Data == null) {
          continue;
        }
        String type = 'mp4';
        final videoFile = await asset.file;
        if (videoFile != null) {
          type = DU.getExtName(videoFile.path) ?? 'mp4';
        }

        final file = MultipartFile.fromBytes(u8Data,
            filename:
            '${tag}_${DateTime.now().millisecondsSinceEpoch.toString()}.$type');
        fileList.add(file);
      }
    }
    var formData = FormData.fromMap({'files': fileList});
    Response response;
    try {
      response = await _createUpload().post('/upload', data: formData,
          onSendProgress: (int count, int total) {
            value.value = count / total * 0.99;
            // print('upload progress: ${value.value}');
          });
    } on DioException catch (e) {
      print('图片上传失败: ${e.toString()}');
      Toast.show(e.message);
      Toast.show('图片上传失败,请重新尝试');
      return e;
    } finally {
      value.value = 1;
      if (isBlock) Progress.dismiss();
    }
    final urls = DU.safeList<String>(response.data, ['data']);
    return urls;
  }

  Future<List<String>> uploadImages({
    required List<Uint8List> images,
    required String tag,
    bool isBlock = true,
  }) async {
    ValueNotifier<double> value = ValueNotifier<double>(0);
    List<String> allUploadedUrls = [];

    if (isBlock) Progress.show(value);
    if (images.isEmpty) {
      return [];
    }

    for (Uint8List data in images) {
      final compressData = await FlutterImageCompress.compressWithList(
        data,
        minHeight: 1920,
        minWidth: 1080,
        quality: 95,
      );
      final file = MultipartFile.fromBytes(compressData,
          filename: '${tag}_${DateTime.now().millisecondsSinceEpoch.toString()}.jpeg');

      var formData = FormData.fromMap({'file': [file]});
      Response response;
      try {
        response = await _createUpload().post('/api/upload/file', data: formData,
            onSendProgress: (int count, int total) {
              value.value = count / total * 0.99;
            });
        if(null!=response.data){
          String url = 'http://***************:9000'+DU.safeString(response.data, ['data','url']);
          allUploadedUrls.add(url);
        }
      } on DioException catch (e) {
        print('图片上传失败: ${e.toString()}');
        Toast.show(e.message);
        Toast.show('图片上传失败,请重新尝试');
      }

    }

    value.value = 1;
    if (isBlock) Progress.dismiss();

    print('>>>>allUploadedUrls: $allUploadedUrls');
    return allUploadedUrls;
  }

  Future<String?> uploadImage(
      {required Uint8List image, required String tag}) async {
    FormData formData = FormData.fromMap({
      'file': MultipartFile.fromBytes(image,
          filename:
          '${tag}_${DateTime.now().millisecondsSinceEpoch.toString()}.png')
    });
    Response response;
    ValueNotifier<double> value = ValueNotifier<double>(0);
    try {
      Progress.show(value);
      response = await _createUpload().post('/upload', data: formData,
          onSendProgress: (int count, int total) {
            value.value = count / total * 0.99;
            // print(value.value);
          });
    } on DioException {
      return null;
    } finally {
      value.value = 1;
      Progress.dismiss();
    }
    final result = DU.getValue(response.data, ['data']);
    return result;
  }
}
