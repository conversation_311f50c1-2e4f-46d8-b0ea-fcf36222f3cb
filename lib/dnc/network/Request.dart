import 'package:dio/dio.dart';

import 'Http.dart';

/// **请求类型**
/// * GET 请求方式为get
/// * POST 请求方式为POST
enum RequestType { GET, POST ,PUT}

/// **请求封装**
class Request {
  /// 请求类型 [RequestType]
  final RequestType type;
  final ResponseType responseType;

  /// 请求地址。注意uri不包含Host，host设置参见[DncGlobal.currentHost]
  final String uri;

  /// 请求参数。类型：Map<String, dynamic>
  final Map<String, dynamic> Function()? params;

  /// post体。如果请求方式为 [RequestType.POST],此参数有效
  final dynamic Function()? post;

  /// 标识，请求的别名，便于并发请求时查找响应。
  final String? tag;

  final bool needEntId;

  /// 响应拦截 在[execute]返回前调用，[result] 响应体
  final dynamic Function(dynamic result)? interceptor;

  const Request(this.type, this.uri,
      {this.params,
      this.post,
      this.tag,
      this.interceptor,
      bool? needEntId,
      ResponseType? responseType})
      : needEntId = needEntId ?? true,
        responseType = responseType ?? ResponseType.json;

  /// 执行请求，同步返回结果
  /// * showLoading 是否显示请求中弹层
  Future<dynamic> execute({bool? showLoading}) async {
    return Http().request(this, showLoading: showLoading);
  }
}
