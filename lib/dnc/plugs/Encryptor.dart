import 'package:encrypt/encrypt.dart';
import 'package:flutter/services.dart';
import 'package:pointycastle/asymmetric/api.dart';

class Encryptor {
  final String publicKeyPath = 'assets/key/rsa_public_key.pem';
  Future<String> encrypt(String content) async {
    String addressStr = await rootBundle.loadString(publicKeyPath);
    final publicKey = RSAKeyParser().parse(addressStr) as RSAPublicKey;
    Encrypter encryptor = Encrypter(RSA(publicKey: publicKey));
    final result = encryptor.encrypt(content);
    return result.base64;
  }
}
