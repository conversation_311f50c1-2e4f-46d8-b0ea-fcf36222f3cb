import 'dart:async';

import 'package:device_information/device_information.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../network/Request.dart';
import '../utils/Colors.dart';
import '../utils/DataUtils.dart';
import '../utils/WidgetUtils.dart';
import '../widget/components/Pop.dart';
import '../widget/components/Text.dart';

class IMEIInfo {
  Future<String?> get() async {
    String? imei;
    imei = (await SharedPreferences.getInstance()).getString("deviceID");
    if (DU.isStrEmpty(imei)) {
      imei = await editIMEI();
    }
    if (!DU.isStrEmpty(imei)) {
      (await SharedPreferences.getInstance()).setString('deviceID', imei!);
    }
    return imei;
  }

  Future<String?> editIMEI() async {
    bool skip = false;
    //  IMEI上报是否必填    imei_required        0-否 1-是
    final result = await Request(RequestType.GET, '/qyaq/bs/system/setting/get',
        params: () => {
              'settingCode': 'imei_required',
            }).execute();
    if (result is! DioException) {
      skip = DU.safeString(result, ['settingValue']) == '0';
    }
    print('editIMEI skip: $skip');

    Completer<String?> completer = Completer();
    TextEditingController controller = TextEditingController();
    SmartDialog.show(
      tag: 'editIMEI',
      clickMaskDismiss: false,
      backDismiss: false,
      builder: (context) {
        return Container(
          width: WU.getScreenWidth(context) * 0.8,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(8)),
          ),
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          child: SingleChildScrollView(
            child: Column(
              children: [
                PaddingText(
                  '提示',
                  style: TextStyle(fontSize: 16, color: ColorsI.blue),
                  alignment: Alignment.center,
                ),
                PaddingText(
                  '请输入设备的IMEI',
                  style: TextStyle(fontSize: 15, color: ColorsI.black66),
                  alignment: Alignment.center,
                  padding: EdgeInsets.only(top: 8),
                ),
                PaddingText(
                  '获取方式: 在手机拨号界面输入"*#06#",如有多组，请填写第一个',
                  style: TextStyle(fontSize: 13, color: ColorsI.black54),
                  padding: EdgeInsets.only(top: 8),
                ),
                TextField(
                  controller: controller,
                  maxLines: 1,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                    LengthLimitingTextInputFormatter(15)
                  ],
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: '请输入IMEI',
                    contentPadding: EdgeInsets.fromLTRB(0, 19, 10, 15),
                    hintStyle: TextStyle(color: Colors.black26, fontSize: 14),
                    border: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color(0xffF6F6F6),
                        width: 1,
                      ),
                    ),
                  ),
                  style: TextStyle(fontSize: 14, color: Colors.black87),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      imeiButton(
                        context,
                        name: '取消',
                        onPressed: () {
                          completer.complete(null);
                          SmartDialog.dismiss(tag: 'editIMEI');
                        },
                      ),
                      if (skip)
                        imeiButton(
                          context,
                          name: '跳过',
                          onPressed: () {
                            completer.complete('');
                            SmartDialog.dismiss(tag: 'editIMEI');
                          },
                        ),
                      imeiButton(
                        context,
                        name: '提交',
                        onPressed: () {
                          if (!check(controller.text)) {
                            return;
                          }
                          completer.complete(controller.text);
                          SmartDialog.dismiss(tag: 'editIMEI');
                        },
                      )
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
    return completer.future;
  }

  Widget imeiButton(BuildContext context, {required String name, required Function() onPressed}) {
    Color textColor = ColorsI.black26;
    Color bgColor = ColorsI.white;
    Color borderColor = ColorsI.black26;
    if (name == '提交') {
      textColor = ColorsI.white;
      bgColor = ColorsI.blue;
      borderColor = ColorsI.transparent;
    }
    return InkWell(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 4),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.all(Radius.circular(32)),
          border: Border.all(width: 1, color: borderColor),
        ),
        child: Text(
          name,
          style: TextStyle(fontSize: 15, color: textColor),
        ),
      ),
      onTap: onPressed,
    );
  }

  bool check(String? imei) {
    if (DU.isStrEmpty(imei) || imei!.length != 15) {
      Toast.show('请输入完整15位IMEI码');
      return false;
    }
    List<int> digits = [];
    for (int charCode in imei.codeUnits) {
      digits.add(int.parse(String.fromCharCode(charCode)));
    }
    int sum = 0;
    int length = digits.length;
    for (int i = 0; i < length; i++) {
      // get digits in reverse order
      int digit = digits[length - i - 1];

      // every 2nd number multiply with 2
      if (i % 2 == 1) {
        digit *= 2;
      }
      sum += digit > 9 ? digit - 9 : digit;
    }
    final result = sum % 10 == 0;
    if (!result) {
      Toast.show('您输入的IMEI码有误,请检查修改');
      return false;
    }
    return true;
  }

  int getImeiLast(String imei) {
    int sum = 0;
    for (int i = imei.length - 1 - 1; i >= 0; i--) {
      // get digits in reverse order
      int digit = int.parse(imei[i]);
      // every 2nd number multiply with 2
      if (i % 2 == 1) {
        digit *= 2;
      }
      sum += digit > 9 ? digit - 9 : digit;
    }
    return 10 - sum % 10;
  }

  Future<String?> getIMEI() async {
    Completer<String?> completer = Completer();
    PermissionStatus status = await Permission.phone.status;
    if (status != PermissionStatus.granted) {
      status = await Permission.phone.request();
      if (status != PermissionStatus.granted) {
        Toast.show('获取权限失败');
        return null;
      }
    }
    _getImei((String? imei) {
      print('IMEI: $imei');
      if (!completer.isCompleted) {
        completer.complete(imei);
      }
    });
    Future.delayed(Duration(seconds: 1), () {
      if (!completer.isCompleted) completer.complete(null);
    });
    return completer.future;
  }

  Future _getImei(ValueChanged<String?> onResult) async {
    try {
      String? imei;
      imei = await DeviceInformation.deviceIMEINumber;
      onResult(imei);
    } catch (e) {
      Toast.show('获取IMEI失败');
      onResult(null);
    }
  }
}
