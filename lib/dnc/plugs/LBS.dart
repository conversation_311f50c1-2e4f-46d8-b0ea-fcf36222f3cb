/*
import 'dart:async';
import 'dart:io';

import 'package:flutter_bmflocation/flutter_bmflocation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_baidu_mapapi_base/flutter_baidu_mapapi_base.dart';

class LBS {
  static Future<String> getLocationDetail() async {
    final lbs = await _getLBS();
    if (lbs == null) {
      return '';
    }
    final result = await _getLBSInfo(lbs);
    if (result == null || result.locTime == null) {
      return '';
    }
    return result.locationDetail ?? '';
  }

  static Future<LocationFlutterPlugin?> _getLBS() async {
    if (!await _requestLocationPermission()) {
      return null;
    }
    // 设置是否隐私政策
    final LocationFlutterPlugin lbs = LocationFlutterPlugin();
    lbs.setAgreePrivacy(true);
    BMFMapSDK.setAgreePrivacy(true);
    if (!await _setApiKey(lbs)) {
      return null;
    }
    return lbs;
  }

  /// 动态申请定位权限
  static Future<bool> _setApiKey(LocationFlutterPlugin lbs) async {
    Completer<bool> completer = Completer();
    final AK = 'TiqjWxxO6VUvdtYuHe0X4ppIKrBbz2Da';
    if (Platform.isIOS) {
      lbs.authAK(AK);
      BMFMapSDK.setApiKeyAndCoordType(AK, BMF_COORD_TYPE.BD09LL);
    } else if (Platform.isAndroid) {
      BMFMapSDK.setCoordType(BMF_COORD_TYPE.BD09LL);
      completer.complete(true);
    }

    lbs.getApiKeyCallback(callback: (String result) {
      String str = result;
      completer.complete(result.isNotEmpty);
    });
    return completer.future;
  }

  /// AppKey 鉴权
  static Future<bool> _requestLocationPermission() async {
    PermissionStatus status = await Permission.location.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  static Future<BaiduLocation?> _getLBSInfo(LocationFlutterPlugin lbs) async {
    Map androidMap = BaiduLocationAndroidOption(
            coorType: 'bd09ll',
            locationMode: BMFLocationMode.hightAccuracy,
            isNeedAddress: true,
            isNeedAltitude: true,
            isNeedLocationPoiList: true,
            isNeedNewVersionRgc: true,
            isNeedLocationDescribe: true,
            openGps: true,
            locationPurpose: BMFLocationPurpose.sport,
            coordType: BMFLocationCoordType.bd09ll)
        .getMap();
    Map iosMap = BaiduLocationIOSOption(
            coordType: BMFLocationCoordType.bd09ll,
            BMKLocationCoordinateType: 'BMKLocationCoordinateTypeBMK09LL',
            desiredAccuracy: BMFDesiredAccuracy.best)
        .getMap();
    if (!await lbs.prepareLoc(androidMap, iosMap)) {
      return null;
    }
    Completer<BaiduLocation> completer = Completer();
    if (Platform.isAndroid) {
      lbs.seriesLocationCallback(callback: (BaiduLocation result) {
        lbs.stopLocation();
        completer.complete(result);
      });
    } else {
      lbs.singleLocationCallback(callback: (BaiduLocation result) {
        completer.complete(result);
      });
    }
    if (Platform.isAndroid) {
      lbs.startLocation();
    } else {
      lbs.singleLocation({'isReGeocode': true, 'isNetworkState': true});
    }
    return completer.future;
  }
}

 */
