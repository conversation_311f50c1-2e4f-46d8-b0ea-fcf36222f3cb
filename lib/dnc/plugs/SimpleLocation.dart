import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class SimpleLocation {
  /// 申请权限的说明文案
  final String alertInfo;

  /// 定位超时时间，默认10秒
  final int locationTimeLimit;

  /// 定位精度等级 0~4 默认2
  /// 0：lowest	安卓500m      苹果3000m  <1s
  /// 1：low   	安卓500m      苹果1000m  <1s
  /// 2: medium	安卓100~500m  苹果100m   <2s
  /// 3: high 	安卓0~100m    苹果10m    <5s
  /// 4: best 	安卓0~100m    苹果<10m   <10s
  final int level;

  /// [level]
  SimpleLocation({String? alertInfo, int? locationTimeLimit, int? level})
      : alertInfo = alertInfo ?? '您需要打开定位服务才能使用定位功能,请在设置中打开定位服务.',
        locationTimeLimit = locationTimeLimit ?? 10,
        level = level ?? 2;

  Future<LocationData?> getLocation(BuildContext context) async {
    bool locationServiceStatus = await _checkLocationServiceStatus(context);
    if (!locationServiceStatus) {
      return null;
    }
    bool permissionStatus = await _checkPermissionStatus();
    if (!permissionStatus) {
      return null;
    }
    Position? position;
    int duration = 0;
    try {
      int startTime = DateTime.now().millisecondsSinceEpoch;
      LocationAccuracy accuracy;
      switch (level) {
        case 0:
          accuracy = LocationAccuracy.lowest;
          break;
        case 1:
          accuracy = LocationAccuracy.low;
          break;
        case 3:
          accuracy = LocationAccuracy.high;
          break;
        case 4:
          accuracy = LocationAccuracy.best;
          break;
        default:
          accuracy = LocationAccuracy.medium;
          break;
      }
      position ??= await Geolocator.getCurrentPosition(
          desiredAccuracy: accuracy,
          forceAndroidLocationManager: true,
          timeLimit: Duration(seconds: locationTimeLimit));
      duration = DateTime.now().millisecondsSinceEpoch - startTime;
    } catch (e) {
      position = await Geolocator.getLastKnownPosition(
        forceAndroidLocationManager: true,
      );
    }

    if (position != null) {
      return LocationData(
        longitude: position.longitude,
        latitude: position.latitude,
        timestamp: position.timestamp ?? DateTime.now(),
        accuracy: position.accuracy,
        altitude: position.altitude,
        heading: position.heading,
        speed: position.speed,
        speedAccuracy: position.speedAccuracy,
        duration: duration,
      );
    } else {
      return null;
    }
  }

  Future<bool> _checkLocationServiceStatus(BuildContext context) async {
    final navigator = Navigator.of(context);
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      await showDialog<void>(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: const Text('提示'),
            content: Text(alertInfo),
            actions: <Widget>[
              TextButton(
                child: const Text('取消'),
                onPressed: () => Navigator.of(context).pop(),
              ),
              TextButton(
                child: const Text('设置'),
                onPressed: () async {
                  await AppSettings.openAppSettings(
                      type: AppSettingsType.location);
                  navigator.pop();
                },
              ),
            ],
          );
        },
      );
    }
    return await Geolocator.isLocationServiceEnabled();
  }

  Future<bool> _checkPermissionStatus() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      await Geolocator.requestPermission();
    } else if (permission == LocationPermission.deniedForever) {
      await openAppSettings();
    }
    permission = await Geolocator.checkPermission();
    return permission != LocationPermission.denied &&
        permission != LocationPermission.deniedForever;
  }
}

class LocationData {
  ///位置的纬度,以度为单位（WGS84）
  ///范围 -90 至 +90（包括两者）。
  final double latitude;

  ///位置的经度,以度为单位（WGS84）
  ///范围 -180（不包括）至 +180（包括）。
  final double longitude;

  ///确定此位置的时间。
  final DateTime timestamp;

  ///位置的海拔高度（以米为单位）。
  ///并非所有设备都有海拔高度。在这些情况下，返回值为0.0。
  final double altitude;

  ///位置的经纬度精度（以米为单位）。
  ///并非所有设备都具有该精度。在这些情况下，值为0.0.
  final double accuracy;

  ///位置的移动方向，单位为度。
  ///标题并非在所有设备上都可用。在这些情况下，值为0.0
  final double heading;

  ///位置移动的速度，单位为米/秒
  ///并非所有设备都具有此速度。在这些情况下，值为0.0.
  final double speed;

  ///位置的速度精度，单位为米/秒。
  ///并非在所有设备上都可用。在这些情况下值为0.0。
  final double speedAccuracy;

  ///本次定位的耗时，单位毫秒
  ///定位失败，时间为0.
  final int duration;

  @override
  String toString() {
    return '经度:$longitude 纬度:$latitude 精度:$accuracy';
  }

  const LocationData({
    required this.longitude,
    required this.latitude,
    required this.timestamp,
    required this.accuracy,
    required this.altitude,
    required this.heading,
    required this.speed,
    required this.speedAccuracy,
    required this.duration,
  });
}
