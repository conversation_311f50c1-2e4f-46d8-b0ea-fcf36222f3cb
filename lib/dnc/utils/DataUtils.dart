import 'dart:convert';
import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/common/AppConfig.dart';
import 'package:ent_secutity_app/dnc/utils/Log.dart';
import 'package:html/parser.dart';
import 'package:url_launcher/url_launcher.dart';

import '../widget/components/Pop.dart';

class DU {
  static bool isListEmpty(List? list) => list == null || list.isEmpty;

  static bool isStrEmpty(String? value) => value == null || value == "";

  static dynamic findValueByTree(
      List tree, String key, String k, String v, String? child) {
    for (var item in tree) {
      if (item[k] == key) {
        return item[v];
      }
      if (child != null && item[child] != null && item[child].length > 0) {
        final result = findValueByTree(item[child], key, k, v, child);
        if (result != null) {
          return result;
        }
      }
    }
    return null;
  }

  static List<T> safeList<T>(dynamic item, List<String> args) {
    final dynamic data = safeValue<dynamic>(item, args, []);
    if (data == null) {
      return [];
    }
    if (data is String) {
      if(data.isEmpty){
        return [];
      }
      List temp = [];
      dynamic tl = [];
      try {
        tl = jsonDecode(data);
      } catch (e) {
        // tl = data.substring(1, data.length - 1).replaceAll("\\s", "").split(',');
        // Toast.show('jsonDecode错误.\n$data');
        print('jsonDecode错误.\n$data');
       throw Exception('jsonDecode错误.\n$data');
      }
      if (tl is String) {
        if (AC.isTest) {
          SDialog.showAlert(
              title: '数据错误', content: '该数据进行了多次encode： $data \n\n 页面将无法正常显示');
        }
        // throw UnimplementedError('数据进行了多次encode： $data');
      } else {
        temp = tl;
      }
      return temp.map<T>((e) => e).toList();
    } else {
      return (data as List).map<T>((e) => e).toList();
    }
  }

  static String safeString(dynamic item, List<String> args,
      [String defValue = '']) {
    final value = getValue(item, args);
    if (value != null) {
      final result = value is String ? value : '$value';
      return DU.isStrEmpty(result) ? defValue : result;
    } else {
      return defValue;
    }
  }

  static bool safeBool(dynamic item, List<String> args,
      [bool defValue = false]) {
    final value = getValue(item, args);
    if (value != null) {
      return value is bool ? value : value == 'true';
    } else {
      return defValue;
    }
  }

  static Map safeMap(dynamic item, List<String> args,
      {Map defValue = const {}}) {
    final value = getValue(item, args);
    if (value != null) {
      return value is String ? jsonDecode(value) : value;
    } else {
      return defValue;
    }
  }

  static int safeInt(dynamic item, List<String> args, [int defValue = 0]) {
    try {
      return int.parse(safeString(item, args, '0'));
    } catch (e) {
      return defValue;
    }
  }

  static double safeDouble(dynamic item, List<String> args,
      [double defValue = 0]) {
    try {
      return double.parse(safeString(item, args, '0'));
    } catch (e) {
      return defValue;
    }
  }

  static T safeValue<T>(dynamic item, List<String> args, T defValue) {
    return getValue(item, args) ?? defValue;
  }

  static dynamic getValue(dynamic item, [List<String>? args]) {
    if (item == null) {
      return null;
    } else if (args == null || args.length == 0) {
      return item;
    } else {
      // print('item: $item');
      if (item is String) {
        item = json.decode(item);
      }
      if (!(item is Map)) {
        print('>>>>>>>getValue item is not map !!!<<<<<<<');
        print('data: $item \n args:$args');
        print('>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<');
        return item;
      } else {
        return args.length > 1
            ? getValue(item[args.removeAt(0)], args)
            : item[args[0]];
      }
    }
    // return item == null || args.length == 0
    //     ? null
    //     : (args.length > 1 ? getValue(item[args.removeAt(0)], args) : item[args[0]]);
  }

  static List<T> transformList<T, L>(
      List<dynamic>? list, T? Function(L data) change) {
    List<T> result = [];
    if (isListEmpty(list)) {
      return result;
    }
    for (int i = 0; i < list!.length; i++) {
      final data = list[i];
      final T? r = change(data);
      if (r != null) {
        result.add(r);
      }
    }
    return result;
  }

  static String mergeInfo(List? list, [List<String>? args]) {
    return mergeObject(list, (dynamic data) {
      return DU.isListEmpty(args) ? data : safeString(data, args!);
    });
  }

  static String mergeList({List? list, List<String>? args, String? connector}) {
    return mergeObject(list, (dynamic data) {
      return DU.isListEmpty(args) ? data : safeString(data, args!);
    }, connector);
  }

  static String mergeObject<T>(List<T>? list, String? Function(T data) change,
      [String? connector]) {
    if (list == null || list.length == 0) {
      return '';
    }
    var mi = '';
    list.forEach((T t) {
      final info = change(t);
      if (!isStrEmpty(info)) {
        mi += '$info${connector ?? ','}';
      }
    });
    if (mi.length > 0) {
      mi = mi.substring(0, mi.length - 1);
    }
    return mi;
  }

  @Deprecated('Please use [mergeInfo(List? list, List<String> args)];')
  static String mergeInfo1l(List? info) {
    if (info == null || info.length == 0) {
      return '';
    }
    var mi = '';
    info.forEach((item) {
      if (item != null) {
        mi += '$item,';
      }
    });
    if (mi.length > 0) {
      mi = mi.substring(0, mi.length - 1);
    }
    return mi;
  }

  @Deprecated(
    'Please use [mergeInfo(List? list, List<String> args)];'
        'This feature was deprecated after July.2022',
  )
  static String mergeInfo2l(List<dynamic>? info, String arg) {
    if (info == null || info.length == 0) {
      return '';
    }
    var mi = '';
    info.forEach((item) {
      if (item[arg] != null) {
        mi += '${item[arg]},';
      }
    });
    if (mi.length > 0) {
      mi = mi.substring(0, mi.length - 1);
    }
    return mi;
  }

  @Deprecated(
    'Please use [mergeInfo(List? list, List<String> args)];'
        'This feature was deprecated after July.2022',
  )
  static String mergeInfo3l(List<dynamic>? info, String arg1, String arg2) {
    if (info == null || info.length == 0) {
      return '';
    }
    var mi = '';
    info.forEach((item) {
      if (item[arg1] != null && item[arg1][arg2] != null) {
        mi += '${item[arg1][arg2]},';
      }
    });
    if (mi.length > 0) {
      mi = mi.substring(0, mi.length - 1);
    }
    return mi;
  }

  static DateTime formatDateTime(dynamic data, List<String>? args,
      {DateTime? def}) {
    final dynamic date = getValue(data, args);
    if (date is int) {
      return DateUtil.getDateTimeByMs(date);
    } else if (date is String) {
      return DateTime.parse(date);
    } else {
      return def ?? DateTime.now();
    }
  }

  static String formatDate(dynamic data, List<String>? args,
      {String def = '', String? format}) {
    dynamic date = getValue(data, args);
    final defFormat = format ?? DateFormats.y_mo_d_h_m;

    // 处理奇葩的代码
    // if (date is String) {
    //   if (date == 'null') {
    //     return def;
    //   }
    //   try {
    //     date = int.parse(date);
    //   } catch (e) {}
    // }
    // 处理奇葩代码end
    if (date is int) {
      return DateUtil.formatDateMs(date, format: defFormat);
    } else if (date is String && date.isNotEmpty) {
      try {
        return DateUtil.formatDate(DateTime.parse(date), format: defFormat);
      } catch (e) {
        return date;
      }
    } else if (date is DateTime) {
      return DateUtil.formatDate(date, format: defFormat);
    } else {
      return def;
    }
  }

  static String formatSmartDate(dynamic data, List<String> args,
      [String def = '']) {
    final dateStr = formatDate(data, args, def: def);
    if (dateStr == def) {
      return dateStr;
    }
    final date = DateTime.parse(dateStr);
    if (date.isAfter(getDayStart())) {
      return DateUtil.formatDate(date, format: DateFormats.h_m);
    } else {
      return DateUtil.formatDate(date, format: DateFormats.y_mo_d);
    }
  }

  /// 格式化金额(小数点前每3位增加逗号)
  static String formatMoney(String money) {
    String prefix = '';
    if (money.startsWith("-")) {
      prefix = "-";
      money = money.substring(1);
    }

    if (money.contains(".")) {
      String intStr = money.split(".")[0];
      String decimalStr = money.split(".")[1];
      // 如果小数点后都是0,则不显示
      if (decimalStr == "0" || decimalStr == "00") {
        return prefix + formatMoney(intStr);
      }
      return prefix + formatMoney(intStr) + "." + decimalStr;
    } else {
      if (money.length <= 3) {
        return prefix + money;
      } else {
        return prefix +
            formatMoney(money.substring(0, money.length - 3)) +
            "," +
            money.substring(money.length - 3);
      }
    }
  }

  static String getDateTimestamp({String? format}) {
    return DateUtil.formatDate(DateTime.now(), format: format);
  }

  static DateTime getDayStart() {
    final now = DateTime.now();
    final timestamp = DateTime(now.year, now.month, now.day);
    return timestamp;
  }

  static DateTime getWeekStart() {
    var time = DateTime.now();
    time = time.subtract(Duration(days: time.weekday - 1));
    final timestamp = DateTime(time.year, time.month, time.day);
    return timestamp;
  }

  static DateTime getMonthStart({int offside = 0}) {
    final now = DateTime.now();
    final timestamp = DateTime(now.year, now.month + offside);
    return timestamp;
  }

  static DateTime getMonthEnd({int offside = 0}) {
    final now = DateTime.now();
    var timestamp = DateTime(now.year, now.month + 1 + offside);
    timestamp = timestamp.subtract(Duration(seconds: 1));
    return timestamp;
  }

  static DateTime getQuarterStart() {
    final now = DateTime.now();
    final quarterStartMonth = (((now.month - 1) / 3).floor()) * 3 + 1;
    return DateTime(now.year, quarterStartMonth);
  }

  static DateTime getYearStart() {
    final now = DateTime.now();
    final timestamp = DateTime(now.year);
    return timestamp;
  }

  static String formatHtml(dynamic data, List<String> args, [String def = '']) {
    final html = getValue(data, args);
    if (html != null) {
      return parse(html).body?.text ?? '';
    } else {
      return def;
    }
  }

  static String formatDuration(Duration duration) {
    final durationInfo = duration.toString().split(':');
    final last = durationInfo[durationInfo.length - 1];
    durationInfo[durationInfo.length - 1] =
        last.substring(0, last.indexOf('.'));
    return DU.mergeList(list: durationInfo, connector: ':');
  }

  /// perfect true: list1是否和list2相等，false：list1 是否包含于list2
  static bool listEquals(List? list1, List? list2,
      bool Function(dynamic item1, dynamic item2) compare) {
    // print('list1: ${list1?.length}; list2: ${list2?.length}');
    // L.d('list1:\n$list1\nlist2:\n$list2');

    if ((list1 == null && list2 != null) || (list1 != null && list2 == null)) {
      // print('false_____________________1');
      return false;
    }
    if (list1 == null && list2 == null) {
      // print('true_____________________');
      return true;
    }
    if (list1!.length != list2!.length) {
      // print('false_____________________2');
      return false;
    }

    List temp = List.from(list1);
    for (dynamic item2 in list2) {
      for (dynamic item1 in temp) {
        if (compare(item1, item2)) {
          // print('=== \n$item1  \n$item2\n---');
          temp.remove(item1);
          break;
        }
      }
    }
    // print(temp);
    return temp.isEmpty;
  }

  static dynamic findItemByDict(List<dynamic> dict, dynamic item,
      {String c = 'code', String v = 'value'}) {
    if (dict.length == 0) {
      return null;
    }
    for (int i = 0; i < dict.length; i++) {
      final data = dict[i];
      // print('findItemByDict: $data');
      if (data[c] == item[c] || (item[c] == null && data[v] == item[v])) {
        return data;
      }
    }
    return null;
  }

  static dynamic findCodeByDict(List<dynamic> dict, String c,
      {String code = "code"}) {
    if (dict.length == 0) return null;
    for (int i = 0; i < dict.length; i++) {
      final item = dict[i];
      if (item[code] == c) {
        return item;
      }
    }
    return null;
  }

  static String findValueByDict(List<dynamic>? dict, String? value,
      [String arg1 = "code", String arg2 = "name"]) {
    var res = '';
    if (dict == null || dict.length == 0 || value == null) return '';
    dict.forEach((element) {
      // print("taskType: ${element[arg1]}");
      if (element[arg1] == value) {
        res = element[arg2];
      }
    });
    return res;
  }

  static bool compareVersion(String newVersion, String version) {
    if (newVersion.isEmpty || version.isEmpty) return false;
    int newVersionInt, oldVersionInt;
    var newList = newVersion.split('.');
    var oldList = version.split('.');
    if (newList.length == 0 || oldList.length == 0) {
      return false;
    }
    for (int i = 0; i < newList.length; i++) {
      if (i > oldList.length - 1) {
        return true;
      }
      newVersionInt = int.parse(newList[i]);
      oldVersionInt = int.parse(oldList[i]);
      if (newVersionInt > oldVersionInt) {
        return true;
      } else if (newVersionInt < oldVersionInt) {
        return false;
      }
    }
    return false;
  }

  static String getVersionName(String version, {bool full = false}) {
    if (version.isEmpty) return version;
    final verList = version.split('.');
    final last = int.parse(verList.removeLast());
    return full
        ? '${verList.join('.')}.${last ~/ 10}.${last % 10}'
        : '${verList.join('.')}.${last ~/ 10}';
  }

  static String showVersionValue(String version, int code,
      {bool full = false}) {
    if (version.isEmpty) return '';
    return 'V$version (${DateUtil.formatDateMs(code * 1000, format: DateFormats.mo_d)})';
    // return full
    //     ? 'V${getVersionName(version, full: true)} (${DateUtil.formatDateMs(code * 1000, format: DateFormats.mo_d)})'
    //     : 'V${getVersionName(version, full: false)}';
  }

  static String? getFileName(String? url) {
    if (DU.isStrEmpty(url)) return null;
    final index = url!.lastIndexOf('/');
    final name =
    index > -1 && index < url.length - 1 ? url.substring(index + 1) : url;
    return name;
  }

  static String? getExtName(String? url) {
    if (DU.isStrEmpty(url)) return null;
    List em = url!.toLowerCase().split('.');
    return em.length > 1 ? em.last : '';
  }

  static bool isImageFile(String? url) {
    final ext = getExtName(url);
    if (DU.isStrEmpty(ext)) return false;
    return _ImageExt.contains(ext);
  }

  static bool isVideo(String? url) {
    final ext = getExtName(url);
    if (DU.isStrEmpty(ext)) return false;
    return _VideoExt.contains(ext);
  }

  static bool isOfficeFile(String? url) {
    final ext = getExtName(url);
    if (DU.isStrEmpty(ext)) return false;
    return _OfficeExt.contains(ext);
  }

  static void launchCaller(String number) async {
    String url = Platform.isIOS ? 'tel://$number' : 'tel:$number';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      Toast.show("暂不支持拨打");
    }
  }

  static const _ImageExt = ['jpg', 'jpeg', 'jpe', 'png', 'bmp', 'gif'];
  static const _VideoExt = ['mp4'];

  static const _OfficeExt = [
    'doc',
    'docx',
    'ppt',
    'pptx',
    'xls',
    'xlsx',
    'pdf'
  ];
}
