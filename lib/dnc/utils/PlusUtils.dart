import 'dart:io';

import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:flutter/services.dart';
import 'package:flutter_nfc_kit/flutter_nfc_kit.dart';
import 'package:ndef/record.dart';
import 'package:ndef/record/text.dart';
import 'package:url_launcher/url_launcher.dart';
import '../widget/components/Pop.dart';

class NFC {
  /// **读取NFC信息**
  /// * return 返回NFC信息，如果失败返回null
  static Future<String?> getNFCInfo() async {
    //判断权限
    var availability = await FlutterNfcKit.nfcAvailability;
    if (availability != NFCAvailability.available) {
      Toast.show('当前设备不支持NFC');
      return null;
    }
    //timeout：只对android有效，ios的控制不了
    //iosAlertMessage是ios扫码界面的提示，可设置为空。
    Toast.show('请将NFC卡片靠近手机背面');
    await FlutterNfcKit.poll(
      timeout: Duration(seconds: 60),
      iosAlertMessage: "请将NFC卡片靠近手机背面",
    );

    try {
      List<NDEFRecord> list = await FlutterNfcKit.readNDEFRecords(cached: false);
      if (list.isEmpty) {
        return null;
      }

      final record = list.first;
      String? result;
      if (record is TextRecord) {
        result = record.text!.trim();
      }

      if (Platform.isIOS) {
        await FlutterNfcKit.finish(iosAlertMessage: "扫描成功");
      } else {
        Toast.show('扫描成功');
      }
      return result;
    } catch (e) {
      return null;
    } finally {
      if (Platform.isIOS) {
        await FlutterNfcKit.finish(iosAlertMessage: "扫描失败");
      }
    }
  }
}

class ScanQR {
  /// **读取二维码信息**
  /// * decode 信息是否需要json解码，默认false
  /// * return 返回二维码信息，如果失败返回null
  static Future<String> scan() async {
    var options = ScanOptions(
      strings: {
        "cancel": "取消",
        "flash_on": "闪光灯",
        "flash_off": "关闭闪光灯",
      },
    );
    ScanResult? barcode;
    try {
      barcode = await BarcodeScanner.scan(options: options);
    } on PlatformException catch (e) {
      if (e.code == BarcodeScanner.cameraAccessDenied) {
        barcode = null;
        Toast.show("请在设置中打开摄像头使用权限!");
      } else {
        Toast.show("扫描失败,请重新尝试#1!");
      }
    }
    if (barcode == null) {
      Toast.show("扫描失败,请重新尝试#2!");
      return '';
    }
    if (barcode.type == ResultType.Cancelled) {
      return '';
    } else if (barcode.type != ResultType.Barcode) {
      Toast.show("扫描失败,请重新尝试#3!");
      return '';
    }
    final result = barcode.rawContent;
    // print('QrResult: $result');
    if (result.isEmpty) {
      Toast.show("无效的二维码!");
    }
    return result;

    // dynamic data;
    // // print("data:: $data");
    // try {
    //   data = json.decode(result);
    // } on FormatException {
    //   data = null;
    //   Toast.show("无效的二维码!");
    // }
    // return data;
  }
}

class Launcher {
  /// **打开网页**
  /// * url 打开网页对应的网址
  static void openWeb(String url) {
    Uri uri;
    try {
      uri = Uri.parse(url);
    } on FormatException catch (e) {
      Toast.show('无效访问地址');
      return;
    }
    try {
      launchUrl(uri);
    } on PlatformException catch (e) {
      Toast.show('请检查是否开启访问权限');
    }
  }
}
