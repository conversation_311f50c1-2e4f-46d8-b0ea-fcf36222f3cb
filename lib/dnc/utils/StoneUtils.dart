import 'dart:convert';
import 'dart:io';

import 'package:path_provider/path_provider.dart';

import '../widget/components/Pop.dart';

/*
模拟器存储路径
/data/user/0/com.example.demo/app_flutter/
 */
class StoneUtils {
  static String? _basePath;

  static Future<String> get basePath async {
    if (_basePath == null) {
      _basePath = (await getApplicationDocumentsDirectory()).path;
    }
    return _basePath!;
  }

  static Future<bool> makeDir(String path) async {
    Directory dir = Directory(path);
    if (!dir.existsSync()) {
      Directory? result;
      try {
        result = await dir.create();
      } catch (e) {
        Toast.show('存储异常#01\n${e.toString()}');
        print('存储异常#01\n${e.toString()}');
      }
      return result != null;
    }
    return true;
  }

  static Future<bool> makeFileByString(String path, String name, String contents) async {
    File file = File('$path/$name');
    if (!file.existsSync()) {
      file.createSync();
    }
    try {
      file.writeAsStringSync(contents);
    } catch (e) {
      Toast.show('存储异常#02\n${e.toString()}');
      print('存储异常#02\n${e.toString()}');
      return false;
    }
    return true;
  }

  static Future<bool> makeFileByMap(String path, String name, Map data) async {
    File file = File('$path/$name');
    if (!file.existsSync()) {
      file.createSync();
    }
    try {
      final String content = json.encode(data);
      file.writeAsStringSync(content);
    } catch (e) {
      Toast.show('存储异常#02\n${e.toString()}');
      print('存储异常#02\n${e.toString()}');
      return false;
    }
    return true;
  }

  static Future<String?> makeFileByBytes(String path, String name, List<int> bytes) async {
    final filePath = '$path/$name';
    File file = File(filePath);
    if (!file.existsSync()) {
      file.createSync();
    }
    try {
      file.writeAsBytesSync(bytes);
    } catch (e) {
      Toast.show('存储异常#03\n${e.toString()}');
      print('存储异常#03\n${e.toString()}');
      return null;
    }
    return filePath;
  }

  static Future<bool> deleteDir(String path) async {
    Directory dir = Directory(path);
    if (!dir.existsSync()) {
      return true;
    }
    try {
      dir.delete(recursive: true);
    } catch (e) {
      Toast.show('删除文件异常#02\n${e.toString()}');
      print('删除文件异常#02\n${e.toString()}');
      return false;
    }
    return true;
  }

  static Future<bool> deleteFile(String path) async {
    File file = File(path);
    if (!file.existsSync()) {
      return true;
    }
    try {
      file.delete();
    } catch (e) {
      Toast.show('删除文件异常#01\n${e.toString()}');
      print('删除文件异常#01\n${e.toString()}');
      return false;
    }
    return true;
  }

  static Future<String?> readFileByString(String path) async {
    File file = File('$path');
    if (!file.existsSync()) {
      return null;
    }
    try {
      return file.readAsStringSync();
    } catch (e) {
      Toast.show('存储异常#04\n${e.toString()}');
      print('存储异常#04\n${e.toString()}');
      return null;
    }
  }

  static Future<Map?> readFileByMap(String path) async {
    File file = File('$path');
    if (!file.existsSync()) {
      return null;
    }

    try {
      final content = file.readAsStringSync();
      return json.decode(content);
    } catch (e) {
      Toast.show('存储异常#04\n${e.toString()}');
      print('存储异常#04\n${e.toString()}');
      return null;
    }
  }

  static Future<List<int>?> readFileByBytes(String path) async {
    File file = File('$path');
    if (!file.existsSync()) {
      return null;
    }
    try {
      return file.readAsBytesSync();
    } catch (e) {
      Toast.show('文件读取异常#05\n${e.toString()}');
      print('文件读取异常#05\n${e.toString()}');
      return null;
    }
  }

  static Future<List<String>?> readFileByLines(String path) async {
    File file = File('$path');
    if (!file.existsSync()) {
      return null;
    }
    try {
      return file.readAsLinesSync();
    } catch (e) {
      Toast.show('存储异常#04\n${e.toString()}');
      print('存储异常#04\n${e.toString()}');
      return null;
    }
  }
}
