import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:flutter/material.dart';

import '../widget/components/File.dart';
import '../widget/components/LoadWidget.dart';
import '../widget/components/Pop.dart';
import '../widget/components/Widget.dart';
import 'DataUtils.dart';

class WU {
  /// **空信息widget**
  /// * context 当前context
  /// * return 返回空信息wieget
  static Widget emptyWidget(BuildContext context) {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: WU.getScreenWidth(context) * 0.8,
          maxHeight: WU.getScreenHeight(context) * 0.25,
        ),
        alignment: Alignment.center,
        // color: Color(0xffFAFAFA),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Image(
                image: AssetImage('lib/assets/images/no_data.png'),
                // width: 112,
                // height: 112,
                fit: BoxFit.fitHeight,
              ),
            ),
            Text(
              "暂无数据",
              maxLines: 2,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: ColorsI.black38,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// **错误页面**
  /// * context 当前context
  /// * msg 所需要显示的信息
  /// * return 返回错误页面
  static Widget errorWidget(BuildContext context, String? msg) {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: WU.getScreenWidth(context),
          maxHeight: WU.getScreenHeight(context) * 0.25,
        ),
        alignment: Alignment.center,
        // color: Color(0xffFAFAFA),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Image.asset(
                'lib/assets/images/no_data.png',
              ),
            ),
            Text(
              "$msg",
              maxLines: 2,
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: ColorsI.black38,
                  fontSize: 12,
                  overflow: TextOverflow.ellipsis),
            ),
          ],
        ),
      ),
    );
  }

  /// **加载中页面**
  /// * context 当前context
  /// * info 所需要显示的信息
  /// * return 加载中页面
  static Widget loadingWidget(BuildContext context, {String? info}) {
    return Center(
      child: Container(
        constraints: BoxConstraints(
          maxWidth: WU.getScreenWidth(context),
          maxHeight: WU.getScreenHeight(context) * 0.25,
        ),
        alignment: Alignment.center,
        color: Color(0xffFAFAFA),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LoadRotateWidget(),
            Text(
              info ?? "加载中…",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.blue, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  /// **提交中页面**
  /// * context 当前context
  /// * return 提交中页面
  static Widget commitWidget(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      color: Color(0x66000000),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          LoadingWidget(),
          Text(
            "提交中…",
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.white70, fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// **获取当前屏幕宽度**
  /// * context 当前context
  /// * return 当前屏幕宽度
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// **获取当前屏幕高度**
  /// * context 当前context
  /// * return 当前屏幕高度
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// **移除焦点**
  /// * context 当前context
  static void removeFocus(BuildContext context) {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  /// **打开新页面**
  /// * context 当前context
  /// * page 新页面
  /// * tag 设置路由tag，用于定向返回
  static dynamic nextPage(BuildContext context, Widget page,
      {String? tag}) async {
    removeFocus(context);
    return await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (BuildContext context) {
          return page; //构造函数传递参数
        },
        settings: RouteSettings(
          name: tag,
        ),
      ),
    );
  }

  /// **关闭当前页面**
  /// * context 当前context
  /// * arguments 关闭后的返回值
  static void closePage(BuildContext context, {dynamic arguments}) {
    Navigator.pop(context, arguments);
  }

  /// **关闭页面至tag**
  /// * context 当前context
  /// * tag 返回页面的名称
  static void backPage(BuildContext context, Widget page, String tag) async {
    Navigator.of(context).popUntil((route) {
      return route.settings.name == tag;
    });
  }

  static WidgetList<FileData> urls2WidgetList(List<String> urls) {
    return WidgetList(
      DU.transformList<FileData, String>(
          urls, (data) => data.isNotEmpty ? FileData.byUrl(data) : null),
    );
  }
}
