import 'package:flutter/cupertino.dart';
///**基础数据模型**
abstract class BaseModel extends ChangeNotifier {
  ModelState state = ModelState.Empty;
  bool _disposed = false;

  Future<dynamic> loadData(bool reset);

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  @override
  void notifyListeners() {
    if (_disposed) {
      return;
    }
    super.notifyListeners();
  }
}

enum ModelState { Loading, Empty, Content, Fail }
