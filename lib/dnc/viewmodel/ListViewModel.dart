import 'package:dio/dio.dart';
import 'package:ent_secutity_app/dnc/viewmodel/BaseModel.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../network/Request.dart';
import '../utils/DataUtils.dart';

abstract class ListViewModel extends BaseModel {
  RefreshController controller = RefreshController(initialRefresh: false);
  int startPage = 1;
  int perPage = 10;
  int page = 1;
  List list = [];
  String? errorInfo;

  ListViewModel();

  String get url;

  Map<String, dynamic>? get params;

  Future<void> initData() async {}

  dynamic Function(dynamic result)? get interceptor;

  void refresh({bool refresh = true}) {
    if (refresh) {
      loadData(true);
    }
  }

  void notifyChanged(List list) {}

  @override
  Future<void> loadData(bool reset) async {
    if (reset) {
      state = ModelState.Loading;
      controller.refreshCompleted(resetFooterState: false);
      notifyListeners();
    }
    if (params != null) {
      startPage = params!['page'] ?? 1;
      perPage = params!['perPage'] ?? 10;
    }
    page = reset ? startPage : page + 1;
    // print('======================# $reset $page');
    dynamic data = await Request(RequestType.GET, url, params: () {
          Map<String, dynamic> p = params ?? {};
          p['page'] = page;
          p['perPage'] = perPage;
          return p;
        }).execute(showLoading: false) ??
        [];
    if (data is DioException) {
      state = ModelState.Fail;
      list = [];
      errorInfo = data.message;
      notifyListeners();
      return;
    }
    if (interceptor != null) {
      data = interceptor!(data);
    }
    if (data is Map) {
      data = DU.safeList(data, ['list']);
    }
    if (data.length < perPage) {
      controller.loadNoData();
    } else {
      controller.loadComplete();
    }
    if (reset) {
      list = data;
    } else {
      list.addAll(data);
    }
    state = list.length > 0 ? ModelState.Content : ModelState.Empty;
    controller.refreshCompleted(resetFooterState: true);
    notifyListeners();
    notifyChanged(list);
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }
}
