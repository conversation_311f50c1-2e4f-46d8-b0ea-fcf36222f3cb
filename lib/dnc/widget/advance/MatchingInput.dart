import 'package:flutter/material.dart';

import '../base/BasePage.dart';
import '../base/DefaultThemeData.dart';
import '../components/Button.dart';
import '../components/Tile.dart';

/// **快捷匹配用输入框**
class MatchingInputPage extends StatefulWidget {
  /// **页面名称**
  final String name;
  /// **缺省提示**
  final String? hint;
  /// **默认回填数据**
  final String value;

  // final TextEditingController controller;
  final Future<List<String>> Function() matchingList;

  const MatchingInputPage({
    Key? key,
    required this.name,
    // required this.controller,
    required this.matchingList,
    String? value,
    this.hint,
  })  : this.value = value ?? '',
        super(key: key);

  @override
  State<StatefulWidget> createState() => MatchingInputPageState();
}

class MatchingInputPageState extends BasePageState<MatchingInputPage> {
  final TextEditingController controller = TextEditingController();

  @override
  String strTitle() => widget.name;
  List<String> matchingList = [];

  @override
  void initState() {
    this.controller.text = this.widget.value;
    initMatchingList();
    super.initState();
  }

  Future<void> initMatchingList() async {
    matchingList = await widget.matchingList();
    setState(() {});
  }

  @override
  List<Widget> createActions(BuildContext context) {
    return [
      InfoButton(
          name: '确定',
          onPressed: () {
            Navigator.of(context).pop(this.controller.text);
          }),
    ];
  }

  @override
  Widget body(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        BaseContentTile(
          title: widget.name,
          bottomLine: true,
          body: Container(
            padding: EdgeInsets.fromLTRB(8, 0, 8, 8),
            child: TextFormField(
              controller: this.controller,
              // initialValue: wd.data,
              textAlign: TextAlign.left,
              minLines: 1,
              maxLines: 1,
              decoration: DefaultThemeData.inputDecoration(context, widget.hint ?? '请填写'),
              style: Theme.of(context).primaryTextTheme.bodyText1,
              enableInteractiveSelection: false,
              onChanged: (v) {
                setState(() {});
              },
            ),
          ),
        ),
        SizedBox(height: 4),
        matchingWidget(),
      ],
    );
  }

  Widget matchingWidget() {
    final matchedList = getMatched();
    return Flexible(
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(bottom: 16),
        child: ListView.builder(
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            return GestureDetector(
              child: Container(
                padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
                color: Colors.transparent,
                child: Text(
                  matchedList[index],
                  style: TextStyle(
                    color: Color(0x8a000000),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              onTap: () {
                // setState(() {
                //   this.controller.text = matchedList[index];
                // });
                Navigator.of(context).pop(matchedList[index]);
              },
            );
          },
          itemCount: matchedList.length,
        ),
      ),
    );
  }

  List<String> getMatched() {
    final List<String> matchedList = [];
    for (String item in matchingList) {
      if (item.contains(this.controller.text)) {
        matchedList.add(item);
      }
    }
    return matchedList;
  }
}
