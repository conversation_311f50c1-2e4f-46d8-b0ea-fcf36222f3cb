// Copyright 2014 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'package:flutter/material.dart';

export 'package:flutter/services.dart' show SmartQuotesType, SmartDashesType;

/// **密码专用输入框**
/// 包含隐藏和显示密码按钮
class PasswordTextFormField extends FormField<String> {
  final TextEditingController? controller;

  PasswordTextFormField({
    super.key,
    this.controller,
    bool readOnly = false,
    bool obscureText = false,
  })  :
        super(
        initialValue: controller != null ? controller.text :  '',
        enabled: true,
        autovalidateMode:  AutovalidateMode.disabled,
        builder: (FormFieldState<String> field) {
          final _PasswordTextFormFieldState state = field as _PasswordTextFormFieldState;
          return UnmanagedRestorationScope(
            bucket: state.bucket,
            child: TextField(
              controller: state._effectiveController,
              decoration: InputDecoration(
                hintText: "请输入密码",
                contentPadding: EdgeInsets.fromLTRB(10, 19, 10, 15),
                hintStyle: TextStyle(color: Color(0x8a000000), fontSize: 15),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(
                    color: Color(0xffF6F6F6),
                    width: 1,
                  ),
                ),
                focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(
                      color: Color(0xffF6F6F6),
                      width: 1,
                    )),
                suffixIcon: IconButton(
                  icon: Icon(
                    state.passwordVisible ? Icons.visibility : Icons.visibility_off,
                    color: Color(0x8a000000),
                  ),
                  onPressed: () {
                    state.changeVisible();
                  },
                ),
              ),
              style: TextStyle(color: Colors.black, fontSize: 16),
              textCapitalization: TextCapitalization.none,
              autofocus: false,
              readOnly: readOnly,
              obscuringCharacter: '•',
              smartDashesType: obscureText ? SmartDashesType.disabled : SmartDashesType.enabled,
              smartQuotesType: obscureText ? SmartQuotesType.disabled : SmartQuotesType.enabled,
              enableSuggestions: true,
              maxLines: 1,
              expands: false,
              onChanged: (String value) {
                state.didChange(value);
              },
              obscureText: !state.passwordVisible,
              enabled: true,
              cursorWidth: 2,
              scrollPadding: const EdgeInsets.all(20.0),
              enableInteractiveSelection: !obscureText || !readOnly,
              enableIMEPersonalizedLearning: true,
            ),
          );
        },
        validator: (value) {
          return value!.trim().length > 0 ? null : "密码不能为空";
        },
      );

  @override
  FormFieldState<String> createState() => _PasswordTextFormFieldState();
}

class _PasswordTextFormFieldState extends FormFieldState<String> {
  RestorableTextEditingController? _controller;
  bool passwordVisible = false;

  TextEditingController get _effectiveController => _textFormField.controller ?? _controller!.value;

  PasswordTextFormField get _textFormField => super.widget as PasswordTextFormField;

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    super.restoreState(oldBucket, initialRestore);
    if (_controller != null) {
      _registerController();
    }
    // Make sure to update the internal [FormFieldState] value to sync up with
    // text editing controller value.
    setValue(_effectiveController.text);
  }

  void _registerController() {
    assert(_controller != null);
    registerForRestoration(_controller!, 'controller');
  }

  void _createLocalController([TextEditingValue? value]) {
    assert(_controller == null);
    _controller = value == null
        ? RestorableTextEditingController()
        : RestorableTextEditingController.fromValue(value);
    if (!restorePending) {
      _registerController();
    }
  }

  @override
  void initState() {
    super.initState();
    if (_textFormField.controller == null) {
      _createLocalController(
          widget.initialValue != null ? TextEditingValue(text: widget.initialValue!) : null);
    } else {
      _textFormField.controller!.addListener(_handleControllerChanged);
    }
  }

  @override
  void didUpdateWidget(PasswordTextFormField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_textFormField.controller != oldWidget.controller) {
      oldWidget.controller?.removeListener(_handleControllerChanged);
      _textFormField.controller?.addListener(_handleControllerChanged);

      if (oldWidget.controller != null && _textFormField.controller == null) {
        _createLocalController(oldWidget.controller!.value);
      }

      if (_textFormField.controller != null) {
        setValue(_textFormField.controller!.text);
        if (oldWidget.controller == null) {
          unregisterFromRestoration(_controller!);
          _controller!.dispose();
          _controller = null;
        }
      }
    }
  }

  void changeVisible() {
    setState(() {
      passwordVisible = !passwordVisible;
    });
  }

  @override
  void dispose() {
    _textFormField.controller?.removeListener(_handleControllerChanged);
    _controller?.dispose();
    super.dispose();
  }

  @override
  void didChange(String? value) {
    super.didChange(value);
    if (_effectiveController.text != value) {
      _effectiveController.text = value ?? '';
    }
  }

  @override
  void reset() {
    _effectiveController.text = widget.initialValue ?? '';
    super.reset();
  }

  void _handleControllerChanged() {
    if (_effectiveController.text != value) {
      didChange(_effectiveController.text);
    }
  }
}
