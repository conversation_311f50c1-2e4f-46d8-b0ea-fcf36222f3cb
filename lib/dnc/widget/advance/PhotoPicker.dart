import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/File.dart';
import 'package:ent_secutity_app/dnc/widget/components/Text.dart';
import 'package:ent_secutity_app/dnc/widget/components/Widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image_picker/image_picker.dart' as image_picker;
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../../../common/global.dart';
import '../../utils/WidgetUtils.dart';
import '../components/Button.dart';
import '../components/Image.dart';
import '../components/Pop.dart';
import '../components/Tile.dart';

class PhotoPicker extends StatelessWidget {
  final String title;
  final WidgetList<FileData> wl;
  final bool? read;
  final int? maxCount;
  final bool? hasVideo;
  final String? mark;
  final bool? bottomLine;

  PhotoPicker({
    Key? key,
    required this.title,
    required this.wl,
    this.read,
    this.maxCount,
    this.bottomLine,
    this.mark,
    this.hasVideo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    wl.list = wl.list;
    return BaseContentTile(
      title: title,
      bottomLine: bottomLine,
      body: Container(
        padding: EdgeInsets.fromLTRB(6, 8, 6, 8),
        child: PhotoPickerWidget(
          wl: wl,
          read: read,
          hasVideo: hasVideo,
          maxCount: maxCount,
          mark: mark,
        ),
      ),
    );
  }
}

class PhotoPickerWidget extends StatefulWidget {
  final WidgetList<FileData> wl;
  final int maxCount;
  final bool read;
  final bool? hasVideo;
  final String? mark;
  final EdgeInsets? margin;

  const PhotoPickerWidget(
      {Key? key,
        required this.wl,
        int? maxCount,
        bool? read,
        this.mark,
        this.margin,
        this.hasVideo})
      : maxCount = maxCount ?? 9,
        read = read ?? true,
        super(key: key);

  @override
  State<StatefulWidget> createState() => PhotoPickerWidgetState();
}

class PhotoPickerWidgetState extends State<PhotoPickerWidget> with ImagePicker {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final kScreenWidth = MediaQuery.of(context).size.width;
    final spacing = kScreenWidth / 64;
    final maxCount = widget.maxCount;
    final gridLength = widget.read
        ? widget.wl.list.length > 0
        ? widget.wl.list.length
        : 1
        : widget.wl.list.length < maxCount
        ? widget.wl.list.length + 1
        : maxCount;
    return Container(
      margin: widget.margin,
      child: GridView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.all(spacing),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
        ),
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (BuildContext context, int index) {
          return itemBuilder(context, index, maxCount - widget.wl.list.length);
        },
        itemCount: gridLength,
      ),
    );
  }

  Widget itemBuilder(BuildContext context, int index, int margin) {
    // 0 空提示，1 资源，2 添加按钮
    final type = widget.read
        ? widget.wl.list.length > 0
        ? 1
        : 0
        : index < widget.wl.list.length
        ? 1
        : 2;
    if (type == 1) {
      return Stack(
        children: [
          FileWidget(data: widget.wl.list[index]),
          Positioned(
            right: 0,
            child: !widget.read
                ? GestureDetector(
              child: Container(
                // 我理解
                child: Image.asset(
                  "assets/images/module/plan_budget/clear_solid.png",
                  fit: BoxFit.fill,
                  width: 16,
                ),
              ),
              onTap: () {
                widget.wl.list.removeAt(index);
                setState(() {});
              },
            ) /* IConButton(

                      size: Size(16, 16),
                      padding: EdgeInsets.fromLTRB(8, 0, 0, 8),
                      onPressed: () {
                        setState(() {
                          widget.wl.list.removeAt(index);
                        });
                      },
                    )*/
                : SizedBox(),
          ),
        ],
      );
    } else if (type == 2) {
      return Builder(builder: (context) {
        return GestureDetector(
            child: Container(
              margin: EdgeInsets.all(4),
              child: ClipRRect(
                child: Image.asset("lib/assets/images/add_img.png"),
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            onTap: () async {
              FocusScope.of(context).requestFocus(FocusNode());
              pick(
                context: context,
                wl: widget.wl,
                maxCount: margin,
                hasVideo: widget.hasVideo,
                mark: widget.mark,
                cameraOnly: false,
              );
            });
      });
    } else {
      return Container(
        color: Color(0xffEEEEEE),
        alignment: Alignment.center,
        child: Text(
          "暂无内容",
          style: TextStyle(
              color: Color(0x8a000000),
              fontSize: 14,
              fontWeight: FontWeight.normal,
              overflow: TextOverflow.ellipsis),
        ),
      );
    }
  }

  @override
  BuildContext getContext() {
    return this.context;
  }
}

class PhotoPickerTile extends StatefulWidget {
  final String title;
  final double? titleSize;
  final WidgetList<FileData> wl;
  final bool? hasVideo;
  final bool? read;
  final int? maxCount;
  final bool? showBottomLine;
  final String? mark;
  final bool? important;
  final double? minHeight;

  const PhotoPickerTile(
      {Key? key,
        required this.title,
        required this.wl,
        this.read,
        this.maxCount,
        this.showBottomLine,
        this.mark,
        this.important,
        this.minHeight,
        this.titleSize,
        this.hasVideo})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => PhotoPickerTileState();
}

class PhotoPickerTileState extends State<PhotoPickerTile> with ImagePicker {
  @override
  Widget build(BuildContext context) {
    final count = widget.wl.list.length;
    final maxCount = widget.maxCount ?? 9;
    return SimpleTile(
      title: widget.title,
      titleSize: widget.titleSize,
      bottomLine: widget.showBottomLine,
      important: widget.important,
      minHeight: widget.minHeight,
      body: Row(mainAxisSize: MainAxisSize.min, children: [
        GestureDetector(
          child: Container(
            padding: EdgeInsets.fromLTRB(8, 4, 8, 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(18),
              color: Color(0xffEEEEEE),
            ),
            child: Text(
              '$count/$maxCount',
              style: TextStyle(
                color: Color(0xff9D9D9D),
                fontSize: 14,
              ),
            ),
          ),
          onTap: () async {
            if (count < 1) {
              Toast.show('当前无图片预览');
            } else {
              await WU.nextPage(
                  context,
                  ImageGridPage(
                    widget.wl,
                    read: widget.read,
                  ));
              setState(() {});
            }
          },
        ),
        SizedBox(width: 8),
        widget.read ?? true
            ? SizedBox()
            : Builder(builder: (context) {
          return GestureDetector(
            child: Container(
              color: Colors.transparent,
              padding: EdgeInsets.all(8),
              child: Icon(
                CupertinoIcons.camera_fill,
                color: Color(0xff2A80FF),
                size: 20,
              ),
            ),
            onTap: () async {
              if (count == maxCount) {
                Toast.show('最多支持上传$maxCount张照片');
              } else {
                pick(
                  context: context,
                  wl: widget.wl,
                  maxCount: maxCount - count,
                  hasVideo: widget.hasVideo,
                  mark: widget.mark,
                  cameraOnly: false,
                );
              }
            },
          );
        }),
      ]),
    );
  }

  @override
  BuildContext getContext() {
    return this.context;
  }
}

class ImageGridPage extends StatefulWidget {
  final WidgetList<FileData> wl;
  final bool? read;

  ImageGridPage(this.wl, {this.read});

  @override
  State<StatefulWidget> createState() => ImageGridPageState();
}

class ImageGridPageState extends BasePageState<ImageGridPage> {
  @override
  String strTitle() => '预览';

  @override
  Widget body(BuildContext context) {
    return Container(
      child: ImageGrid(
        widget.wl,
        read: widget.read,
      ),
    );
  }
}

mixin ImagePicker {
  void setState(VoidCallback fn);

  BuildContext getContext();

  pick(
      {required BuildContext context,
        required final WidgetList<FileData> wl,
        required int maxCount,
        bool? hasVideo,
        required String? mark,
        bool? cameraOnly}) async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
      Permission.storage,
    ].request();

    final video = hasVideo ?? false;
    if (cameraOnly ?? true && maxCount > 0) {
      final fileData = await _byCamera(context, video, mark);
      if (fileData != null) {
        setState(() {
          wl.list.add(fileData);
        });
      }
      return;
    }
    SDialog.showCenter(
          (_) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          width: 128 + 32,
          padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _itemBuilder(context, CupertinoIcons.photo_fill, '相册', () async {
                SDialog.dismiss();
                await byAlbum(context,
                    wl: wl, maxCount: maxCount, video: video);
                setState(() {});
              }),
              Divider(color: Colors.black12, height: 1, thickness: 1),
              _itemBuilder(context, CupertinoIcons.camera_fill, '拍照', () async {
                SDialog.dismiss();
                final fileData = await _byCamera(context, video, mark);
                if (fileData != null) {
                  setState(() {
                    wl.list.add(fileData);
                  });
                }
              }),
            ],
          ),
        );
      },
    );
  }

  Widget _itemBuilder(
      BuildContext context, IconData data, String name, void Function() onTap) {
    return GestureDetector(
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.fromLTRB(0, 8, 0, 8),
        color: Colors.transparent,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(data, size: 24, color: Color(0x8a000000)),
            SizedBox(width: 32),
            Text(name,
                style: TextStyle(
                  color: Color(0x9a000000),
                  fontSize: 15,
                  fontWeight: FontWeight.w400,
                )),
          ],
        ),
      ),
      onTap: onTap,
    );
  }

  Future<FileData?> _byCamera(
      BuildContext context, bool video, String? mark) async {
    // final imageFile = await image_picker.ImagePicker()
    //     .pickImage(source: image_picker.ImageSource.camera, imageQuality: 70);

    final AssetEntity? asset = await CameraPicker.pickFromCamera(
      context,
      pickerConfig: CameraPickerConfig(
        enableRecording: video,
      ),
    );
    // final imageFile = await entity?.originBytes;
    if (asset == null) {
      Toast.show('添加失败,请重新尝试');
      return null;
    }
    // print('imageFile: ${asset.type}');
    final prefs = await SharedPreferences.getInstance();
    final waterMark = prefs.getBool('waterMark') ?? true;

    if (waterMark && asset.type == AssetType.image) {
      final date = asset.createDateTime;
      final data = await asset.originBytes;
      if (data == null) {
        return null;
      }
      final result = await WU.nextPage(
        getContext(),
        WaterMarkPage(
          imageData: data,
          name: mark ?? '',
          person: Global.name,
          ent: Global.entName,
          date: date,
        ),
      );
      return result != null ? FileData.byImageData(result) : null;
    } else {
      return FileData.byAssetEntity(asset);
    }
  }

  Future<WidgetList<FileData>> byAlbum(BuildContext context,
      {required WidgetList<FileData> wl,
        required int maxCount,
        required bool video}) async {
    final List<AssetEntity> assets = await AssetPicker.pickAssets(
      context,
      pickerConfig: AssetPickerConfig(
        requestType: video ? RequestType.common : RequestType.image,
        maxAssets: maxCount,
      ),
    ) ??
        [];
    print(assets.length);
    final List<FileData> files = [];
    for (AssetEntity asset in assets) {
      final data = await asset.originBytes;
      files.add(FileData.byAssetEntity(asset));
    }
    wl.list.addAll(files);
    return wl;
  }

  // Future<List<Asset>> byAlbum1(
  //     {required WidgetList<FileData> wl, required int maxCount}) async {
  //   // final List<FileData> files = new List.from(wl.list);
  //   // final List<Asset> assets = [];
  //   List<Asset> result = [];
  //   // for (FileData file in wl.list) {
  //   //   if (file.data != null && file.data is Asset) {
  //   //     files.remove(file);
  //   //     assets.add(file.data);
  //   //   }
  //   // }
  //   try {
  //     result = await MultiImagePicker.pickImages(
  //       maxImages: maxCount,
  //       enableCamera: false,
  //       // selectedAssets: assets,
  //       materialOptions: MaterialOptions(
  //           actionBarColor: '#2A80FF',
  //           startInAllView: true,
  //           textOnNothingSelected: '没有选择照片',
  //           autoCloseOnSelectionLimit: false,
  //           selectionLimitReachedText: "最多上传$maxCount张照片"),
  //     );
  //   } catch (e) {
  //     result = [];
  //   }
  //   // if (result.isEmpty) {
  //   //   result = assets;
  //   // }
  //   final List<FileData> files = [];
  //   for (Asset asset in result) {
  //     files.add(await FileData.byAssetData(asset));
  //     // wl.list.add(await FileData.byAssetData(asset));
  //   }
  //   wl.list.addAll(files);
  //   return result;
  // }

  Future<List<FileData>> byAlbum2(
      {required BuildContext context,
        required WidgetList<FileData> wl,
        required int maxCount}) async {
    final List<FileData> selectedImages = new List.from(wl.list);
    final List<Uint8List?> datas = [];

    try {
      List<XFile> files = [];
      XFile? f = await image_picker.ImagePicker()
          .pickImage(source: ImageSource.gallery);
      if (f != null) files.add(f);
      for (XFile file in files) {
        final Uint8List? data = await file.readAsBytes();
        if (data != null) {
          datas.add(data);
        }
      }
    } catch (e) {}

    print('datas: ${datas.length}');
    final List<FileData> temp = [];
    for (Uint8List? data in datas) {
      bool exist = false;
      for (FileData image in selectedImages) {
        if (image.imgData.toString() == data.toString()) {
          exist = true;
          break;
        }
      }
      if (!exist) {
        temp.add(FileData.byImageData(data));
      }
    }
    if (temp.isNotEmpty) {
      selectedImages.addAll(temp);
    }
    print(selectedImages.length);
    return selectedImages;
  }
}

class WaterMarkPage extends StatefulWidget {
  final Uint8List imageData;
  final String? name;
  final String person;
  final String ent;
  final DateTime date;

  WaterMarkPage({
    Key? key,
    required this.imageData,
    this.name,
    required this.person,
    required this.ent,
    required this.date,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => WaterMarkPageState();
}

class WaterMarkPageState extends BasePageState<WaterMarkPage> {
  final GlobalKey _repaintKey = GlobalKey();

  // final textController = TextEditingController();
  // final focusNode = FocusNode();

  @override
  String strTitle() => '预览';

  @override
  void initState() {
    // WidgetsBinding.instance?.addPostFrameCallback((_) {
    //   getImageData();
    // });
    super.initState();
  }

  List<Widget> createActions(BuildContext context) => [
    IconTextButton(
      iconData: Icons.save_alt,
      name: '保存',
      onPressed: () async {
        // if (textController.text.isEmpty) {
        //   Toast.show('请填写名称');
        //   return;
        // }
        // if (focusNode.hasFocus) {
        //   focusNode.unfocus();
        //   return;
        // }
        getImageData2();
      },
    )
  ];

  @override
  Widget body(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      color: Colors.black,
      child: RepaintBoundary(
        key: _repaintKey,
        child: Stack(
          children: [
            Container(
              color: Colors.grey,
              child: Image.memory(
                widget.imageData,
                width: double.infinity,
                fit: BoxFit.fitWidth,
              ),
            ),
            Positioned(
              bottom: 8,
              left: 8,
              child: waterMarkBuilder(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget waterMarkBuilder(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,

      // constraints: BoxConstraints(
      //   minHeight: 128,
      //   minWidth: 240,
      // ),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(5)),
        color: Color(0x44000000),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 4,
                height: 16,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(2)),
                  color: Color(0xff2A80FF),
                ),
              ),
              SizedBox(width: 12),
              // SizedBox(
              //   width: 128,
              //   child: TextFormField(
              //     controller: textController,
              //     focusNode: focusNode,
              //     decoration: InputDecoration(
              //       isCollapsed: true,
              //       contentPadding: EdgeInsets.zero,
              //       hintText: '请填写名称',
              //       hintStyle: Theme.of(context).inputDecorationTheme.hintStyle,
              //       border: Theme.of(context).inputDecorationTheme.border,
              //     ),
              //     style: TextStyle(fontSize: 16, color: Colors.white),
              //   ),
              // )

              Text(
                '${widget.person}',
                style: TextStyle(fontSize: 14, color: Colors.white),
              ),
            ],
          ),
          // PaddingText(
          //   '${widget.person}',
          //   style: TextStyle(fontSize: 14, color: Colors.white),
          //   padding: EdgeInsets.only(top: 8),
          // ),
          PaddingText(
            '${widget.ent}',
            style: TextStyle(fontSize: 14, color: Colors.white),
            padding: EdgeInsets.only(top: 8),
          ),
          Container(
            padding: EdgeInsets.only(top: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  CupertinoIcons.time,
                  color: Colors.white,
                  size: 12,
                ),
                SizedBox(width: 8),
                Text(
                  DateUtil.formatDate(widget.date,
                      format: 'yyyy年MM月dd日 HH:mm:ss'),
                  style: TextStyle(fontSize: 14, color: Colors.white),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> getImageData2() async {
    BuildContext? buildContext = _repaintKey.currentContext;
    if (buildContext != null) {
      RenderRepaintBoundary? boundary =
      buildContext.findRenderObject() as RenderRepaintBoundary?;
      // 获取当前设备的像素比
      double dpr = ui.window.devicePixelRatio;
      // pixelRatio 代表截屏之后的模糊程度，因为不同设备的像素比不同
      // 定义一个固定数值显然不是最佳方案，所以以当前设备的像素为目标值
      ui.Image image = await boundary!.toImage(pixelRatio: dpr);
      final ByteData? byteData =
      await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List? imageBytes = byteData?.buffer.asUint8List();
      // 返回图片的数据
      Navigator.pop(context, imageBytes);
    }

    // await Future.delayed(Duration(milliseconds: 200));
    // RenderRepaintBoundary? boundary =
    //     _repaintKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
    // final ui.Image img = await boundary!.toImage();
    // print("getImageData: 2 $img");
    // final byteData = await img.toByteData(format: ImageByteFormat.png);
    // print("getImageData: 3 $byteData");
    // Uint8List pngBytes = byteData!.buffer.asUint8List();
    // print("getImageData: 4 ${pngBytes.length}");
    // Navigator.pop(context, pngBytes);
  }

  Future<void> getImageData1() async {
    BuildContext? buildContext = _repaintKey.currentContext;
    if (buildContext != null) {
      RenderRepaintBoundary? boundary =
      buildContext.findRenderObject() as RenderRepaintBoundary?;
      // 第一次执行时，boundary.debugNeedsPaint 为 true，此时无法截图（如果为true时直接截图会报错）
      if (boundary!.debugNeedsPaint) {
        // 延时一定时间后，boundary.debugNeedsPaint 会变为 false，然后可以正常执行截图的功能
        await Future.delayed(Duration(milliseconds: 50));
        // 重新调用方法
        return getImageData1();
      }
      // 获取当前设备的像素比
      double dpr = ui.window.devicePixelRatio;
      // pixelRatio 代表截屏之后的模糊程度，因为不同设备的像素比不同
      // 定义一个固定数值显然不是最佳方案，所以以当前设备的像素为目标值
      ui.Image image = await boundary.toImage(pixelRatio: dpr);
      final ByteData? byteData =
      await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List? imageBytes = byteData?.buffer.asUint8List();
      // 返回图片的数据
      Navigator.pop(context, imageBytes);
    }

    // await Future.delayed(Duration(milliseconds: 200));
    // RenderRepaintBoundary? boundary =
    //     _repaintKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
    // final ui.Image img = await boundary!.toImage();
    // print("getImageData: 2 $img");
    // final byteData = await img.toByteData(format: ImageByteFormat.png);
    // print("getImageData: 3 $byteData");
    // Uint8List pngBytes = byteData!.buffer.asUint8List();
    // print("getImageData: 4 ${pngBytes.length}");
    // Navigator.pop(context, pngBytes);
  }
}
