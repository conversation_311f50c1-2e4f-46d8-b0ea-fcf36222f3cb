import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../utils/DataUtils.dart';
import '../components/Button.dart';

///** 搜索栏控件**
class SearchWidget extends StatelessWidget {
  ///** 点击事件回调接口**
  final void Function()? onTap;

  const SearchWidget({Key? key, this.onTap}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        height: 28,
        padding: EdgeInsets.fromLTRB(8, 2, 8, 2),
        decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(50)), color: Colors.white),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(CupertinoIcons.search, size: 18, color: Colors.grey),
            SizedBox(
              width: 4,
            ),
            Text(
              '搜索',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            )
          ],
        ),
      ),
      onTap: () {
        if (onTap != null) {
          onTap!();
        }
      },
    );
  }
}

/// **搜索页面,带搜索历史**
class SearchPage extends StatefulWidget {
  /// **根据搜索内容返回搜索结果的实现接口**
  final Future<dynamic> Function(String search) resultData;
  /// **根据搜索结果，返回搜索结果信息的Widget
  final Widget Function(BuildContext context, dynamic result) resultBuilder;
  /// **搜索结果的保存标识，此标识用来管理该搜索框在持久存储里的唯一标识。
  final String tag;

  SearchPage({required this.tag, required this.resultData, required this.resultBuilder});

  @override
  State<StatefulWidget> createState() => SearchPageState();
}

enum PageState { search, result }

class SearchPageState extends State<SearchPage> {
  PageState _state = PageState.search;
  bool historyEdit = false;
  TextEditingController _searchController = TextEditingController();
  FocusNode _searchFocusNode = FocusNode();
  dynamic searchResult;
  List<String> history = [];

  @override
  void initState() {
    initHistory();
    _searchController.addListener(() {
      setState(() {});
    });
    _searchFocusNode.addListener(() {
      setState(() {});
    });
    super.initState();
  }

  initHistory() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      history = prefs.getStringList(widget.tag) ?? [];
    });
  }

  saveHistory() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(widget.tag, history);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
        setState(() {});
      },
      child: Scaffold(
        appBar: appBarBuilder(context),
        body: _bodyBuilder(context),
      ),
    );
  }

  AppBar appBarBuilder(BuildContext context) {
    return AppBar(
      title: Container(
          alignment: Alignment.centerLeft,
          height: 28,
          padding: EdgeInsets.fromLTRB(8, 0, 8, 0),
          decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(50)), color: Colors.white),
          child: TextFormField(
            textAlignVertical: TextAlignVertical.center,
            style: TextStyle(
              color: Colors.black87,
              fontSize: 14,
              textBaseline: TextBaseline.alphabetic,
              fontWeight: FontWeight.w400,
            ),
            focusNode: _searchFocusNode,
            controller: _searchController,
            decoration: InputDecoration(
              isDense: true,
              border: InputBorder.none,
              contentPadding: EdgeInsets.zero,
              prefixIcon: Container(
                padding: EdgeInsets.only(right: 4),
                child: Icon(CupertinoIcons.search, size: 18, color: Colors.grey),
              ),
              prefixIconConstraints: BoxConstraints(),
              hintText: '搜索',
              hintStyle: TextStyle(
                color: Colors.grey,
                fontSize: 14,
                textBaseline: TextBaseline.alphabetic,
                fontWeight: FontWeight.w400,
              ),
              suffixIcon: _searchController.text.length > 0
                  ? GestureDetector(
                child: Container(
                  padding: EdgeInsets.only(left: 4),
                  child: Icon(CupertinoIcons.clear_thick_circled, size: 18, color: Colors.grey),
                ),
                onTap: () {
                  _searchController.clear();
                  setState(() {
                    _state = PageState.search;
                  });
                },
              )
                  : null,
              suffixIconConstraints: BoxConstraints(),
            ),
          )),
      actions: [
        InfoButton(
            name: _state == PageState.search ? '确定' : '取消',
            onPressed: () {
              if (_state == PageState.search) {
                getSearchData(_searchController.text, true);
              } else {
                _searchController.clear();
                setState(() {
                  _state = PageState.search;
                });
              }
            }),
      ],
    );
  }

  Future<dynamic> getSearchData(String info, bool insert) async {
    if (DU.isStrEmpty(info.trim())) {
      return;
    }
    final result = await widget.resultData(info.trim());
    searchResult = result;
    if (insert) {
      history.insert(0, info);
    }
    setState(() {
      _searchController.text = info;
      _state = PageState.result;
    });
  }

  Widget _bodyBuilder(BuildContext context) {
    final Widget body;
    switch (_state) {
      case PageState.search:
        body = _searchBuilder(context);
        break;
      case PageState.result:
        body = _resultBuilder(context);
        break;
    }
    return Container(
      child: body,
    );
  }

  Widget _searchBuilder(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(bottom: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '最近搜索',
                  style: TextStyle(fontSize: 18.0, color: Color(0x8a000000), fontWeight: FontWeight.w500),
                ),
                Spacer(),
                ...historyEdit
                    ? [
                  InfoButton(
                    name: '全部删除',
                    color: Color(0x8a000000),
                    minSize: Size(0, 0),
                    onPressed: () {
                      setState(() {
                        history.clear();
                      });
                    },
                  ),
                  Container(
                    width: 0.5,
                    height: 18,
                    color: Color(0x8a000000),
                    margin: EdgeInsets.fromLTRB(4, 4, 4, 0),
                  ),
                  InfoButton(
                    name: '完成',
                    minSize: Size(0, 0),
                    color: Color(0xFFFF4D4D),
                    onPressed: () {
                      setState(() {
                        saveHistory();
                        historyEdit = false;
                      });
                    },
                  ),
                ]
                    : [
                  GestureDetector(
                    child: Icon(CupertinoIcons.delete, size: 16, color: Colors.grey),
                    onTap: () {
                      setState(() {
                        historyEdit = true;
                      });
                    },
                  ),
                ],
              ],
            ),
          ),
          Wrap(
            direction: Axis.horizontal,
            alignment: WrapAlignment.start,
            spacing: 12,
            runSpacing: 12,
            children: history.map<Widget>((data) {
              return historyItemBuilder(context, data);
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget historyItemBuilder(BuildContext context, String info) {
    return GestureDetector(
      child: Container(
        padding: EdgeInsets.fromLTRB(8, 4, 8, 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20)),
          color: Color(0xffF2F2F2),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              info,
              style: TextStyle(
                color: Color(0x8a000000),
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
            historyEdit
                ? Container(
              margin: EdgeInsets.only(left: 4),
              child: Icon(
                CupertinoIcons.xmark,
                size: 14,
                color: Colors.grey,
              ),
            )
                : SizedBox(),
          ],
        ),
      ),
      onTap: () {
        if (historyEdit) {
          setState(() {
            history.remove(info);
          });
        } else {
          getSearchData(info, false);
        }
      },
    );
  }

  Widget _resultBuilder(BuildContext context) {
    return widget.resultBuilder(context, searchResult);
  }

  @override
  void dispose() {
    saveHistory();
    super.dispose();
  }
}
