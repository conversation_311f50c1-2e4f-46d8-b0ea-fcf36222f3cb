import 'dart:math' as math;
import 'dart:ui';

import 'package:ent_secutity_app/dnc/network/Http.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hand_signature/signature.dart';

import '../../utils/WidgetUtils.dart';
import '../components/Pop.dart';

class SignatureBoarPage extends StatefulWidget {
  final bool isVertical;
  final ValueChanged? onSignature;

  const SignatureBoarPage({Key? key, this.isVertical = false, this.onSignature}) : super(key: key);

  static Future<String?> show(BuildContext context) async {
    return await WU.nextPage(
      context,
      SignatureBoarPage(
        isVertical: false,
      ),
    );
  }

  @override
  State<StatefulWidget> createState() => SignatureBoarPageState();
}

class SignatureBoarPageState extends State<SignatureBoarPage> {
  bool isVertical = true;

  @override
  void initState() {
    isVertical = widget.isVertical;
    if (!isVertical) {
      SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    }
    super.initState();
  }

  void changeScreen() {
    isVertical = !isVertical;
    print('changeScreen: $isVertical');
    if (isVertical) {
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    } else {
      SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: SafeArea(
        child: SignatureBoardWidget(
            onSignature: (url) {
              if (url == null) {
                return;
              }

              if (widget.onSignature != null) {
                widget.onSignature!(url);
              }

              Navigator.pop(context, url);
            },
            onClose: () {
              Navigator.pop(context, null);
            },
            onScreenChange: changeScreen,
            isVertical: isVertical),
      ),
    );
  }

  @override
  void dispose() {
    if (!isVertical) {
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    }
    super.dispose();
  }
}

class SignatureBoardSheet {
  static Future<String?> show(BuildContext context) async {
    final widget = SignatureBoardWidget();
    return await BottomSheetPop.show(
      context,
      widget,
      title: '请签名',
      enableDrag: false,
      onCommand: () async {
        return await widget.upload();
      },
    );
  }
}

class SignatureBoardWidget extends StatefulWidget {
  final bool isVertical;
  final VoidCallback? onClose;
  final ValueChanged? onSignature;
  final VoidCallback? onScreenChange;
  final _state = {};

  SignatureBoardWidget({Key? key, this.isVertical = true, this.onSignature, this.onClose, this.onScreenChange})
      : super(key: key);

  Future<String?> upload() async {
    final SignatureBoardWidgetState state = _state['sf'];
    return await state.upload();
  }

  @override
  State<StatefulWidget> createState() => _state['sf'] = SignatureBoardWidgetState();
}

class SignatureBoardWidgetState extends State<SignatureBoardWidget> {
  final Color paintColor = Color(0xaa000000);
  HandSignatureControl control = new HandSignatureControl(
    threshold: 2,
    smoothRatio: 1,
    velocityRange: 2,
  );

  @override
  Widget build(BuildContext context) {
    final signature = Expanded(
      child: ConstrainedBox(
        constraints: BoxConstraints.expand(),
        child: CustomPaint(
          painter: DashRectPainter(),
          child: Padding(
            padding: const EdgeInsets.all(2),
            child: HandSignature(
              color: paintColor,
              width: 0.8,
              maxWidth: 12,
              control: control,
              type: SignatureDrawType.shape,
            ),
          ),
        ),
      ),
    );
    final List<Widget> commands = [
      Expanded(
        child: Align(
          alignment: Alignment.topLeft,
          child: widget.onClose != null
              ? IconButton(
                  icon: const Icon(Icons.close),
                  color: Colors.blue,
                  onPressed: () {
                    widget.onClose!();
                  },
                )
              : SizedBox(),
        ),
      ),
      ...widget.onScreenChange != null
          ? [
              IconButton(
                icon: const Icon(CupertinoIcons.arrow_2_circlepath),
                color: Colors.blue,
                onPressed: () {
                  widget.onScreenChange!();
                },
              ),
              SizedBox(width: 8),
            ]
          : [],
      IconButton(
        icon: const Icon(Icons.undo),
        color: Colors.blue,
        onPressed: () {
          control.stepBack();
        },
      ),
      SizedBox(width: 8),
      //CLEAR CANVAS
      IconButton(
        icon: const Icon(Icons.replay),
        color: Colors.blue,
        onPressed: () {
          control.clear();
        },
      ),
      ...widget.onSignature != null
          ? [
              SizedBox(width: 8),
              IconButton(
                icon: const Icon(Icons.check),
                color: Colors.blue,
                onPressed: () async {
                  final result = await upload();
                  widget.onSignature!(result);
                },
              )
            ]
          : [],
    ];
    return Container(
      padding: EdgeInsets.all(8),
      constraints: BoxConstraints.expand(),
      color: Colors.white,
      child: widget.isVertical
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: commands,
                ),
                signature,
              ],
            )
          : Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                signature,
                Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: commands,
                ),
              ],
            ),
    );
  }

  Future<String?> upload() async {
    ByteData? bytes = await control.toImage(
      width: widget.isVertical ? 288 : 512,
      height: widget.isVertical ? 512 : 288,
      background: Colors.white,
      format: ImageByteFormat.png,
    );
    if (bytes == null) {
      Toast.show('请签名后再提交');
      return null;
    }
    String? result = await Http().uploadImage(image: bytes.buffer.asUint8List(), tag: 'signature');
    return result;
  }
}

class DashRectPainter extends CustomPainter {
  final gap = 8;

  @override
  void paint(Canvas canvas, Size size) {
    Paint dashedPaint = Paint()
      ..color = Color(0x2a000000)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;
    double x = size.width;
    double y = size.height;
    Path _topPath = getDashedPath(
      a: math.Point(0, 0),
      b: math.Point(x, 0),
      gap: gap,
    );
    canvas.drawPath(_topPath, dashedPaint);
    Path _rightPath = getDashedPath(
      a: math.Point(x, 0),
      b: math.Point(x, y),
      gap: gap,
    );
    canvas.drawPath(_rightPath, dashedPaint);
    Path _bottomPath = getDashedPath(
      a: math.Point(0, y),
      b: math.Point(x, y),
      gap: gap,
    );
    canvas.drawPath(_bottomPath, dashedPaint);
    Path _leftPath = getDashedPath(
      a: math.Point(0, 0),
      b: math.Point(0.001, y),
      gap: gap,
    );
    canvas.drawPath(_leftPath, dashedPaint);
  }

  Path getDashedPath({
    required math.Point<double> a,
    required math.Point<double> b,
    required gap,
  }) {
    Size size = Size(b.x - a.x, b.y - a.y);

    Path path = Path();

    path.moveTo(a.x, a.y);

    bool shouldDraw = true;

    math.Point<double> currentPoint = math.Point(a.x, a.y);

    num radians = math.atan(size.height / size.width);

    num dx = math.cos(radians) * gap < 0 ? math.cos(radians) * gap * -1 : math.cos(radians) * gap;

    num dy = math.sin(radians) * gap < 0 ? math.sin(radians) * gap * -1 : math.sin(radians) * gap;

    while (currentPoint.x <= b.x && currentPoint.y <= b.y) {
      shouldDraw ? path.lineTo(currentPoint.x, currentPoint.y) : path.moveTo(currentPoint.x, currentPoint.y);

      shouldDraw = !shouldDraw;

      currentPoint = math.Point(
        currentPoint.x + dx,
        currentPoint.y + dy,
      );
    }

    return path;
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return true;
  }
}
