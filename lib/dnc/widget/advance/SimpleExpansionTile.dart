import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../utils/Colors.dart';

class SimpleExpansionTile extends StatefulWidget {
  final EdgeInsetsGeometry? padding;
  final dynamic data;
  final bool Function(dynamic current) selected;
  final String Function(dynamic data) title;
  final List Function(dynamic data) children;
  final ValueChanged<dynamic> onChanged;

  const SimpleExpansionTile({
    super.key,
    this.padding,
    required this.data,
    required this.selected,
    required this.title,
    required this.children,
    required this.onChanged,
  });

  @override
  State<StatefulWidget> createState() => SimpleExpansionTileState();
}

class SimpleExpansionTileState extends State<SimpleExpansionTile> {
  bool expanded = false;

  @override
  Widget build(BuildContext context) {
    final color = widget.selected(widget.data) ? ColorsI.blue : ColorsI.black66;

    List children = widget.children(widget.data);

    return Container(
      padding: widget.padding,
      child: Column(
        children: [
          InkWell(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (children.isNotEmpty)
                  Padding(
                    padding: EdgeInsets.only(top: 4),
                    child: Icon(
                        expanded ? Icons.arrow_drop_down : Icons.arrow_right,
                        size: 15,
                        color: color),
                  ),
                SizedBox(width: 4),
                Expanded(
                  child: Text(
                    widget.title(widget.data),
                    style: TextStyle(
                      color: color,
                      fontSize: 15,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              expanded = !expanded;
              setState(() {});
              widget.onChanged(widget.data);
            },
          ),
          if (expanded)
            ...children.map((item) {
              return Padding(
                padding: EdgeInsets.only(left: 16),
                child: SimpleExpansionTile(
                    padding: widget.padding,
                    data: item,
                    selected: (item) => widget.selected(item),
                    title: (item) => widget.title(item),
                    children: (item) => widget.children(item),
                    onChanged: widget.onChanged),
              );
            }).toList(),
        ],
      ),
    );
  }
}
