import 'package:flutter/material.dart';
import 'package:flutter_swiper_null_safety/flutter_swiper_null_safety.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';


///**grid，tab的自定义指示器**
///参见插件[SmoothPageIndicator]
class SwiperIndicator extends SwiperPlugin {
  @override
  Widget build(BuildContext context, SwiperPluginConfig config) {
    return Container(
      padding: EdgeInsets.only(bottom: 8),
      child: SmoothPageIndicator(
          controller: config.pageController!, // PageController
          count: config.itemCount,
          effect: ExpandingDotsEffect(
            expansionFactor: 3,
            offset: 8,
            dotWidth: 8,
            dotHeight: 8,
            spacing: 8,
            radius: 4,
            activeDotColor: Colors.white,
            dotColor: Colors.grey.withOpacity(0.7),
            strokeWidth: 1,
            paintStyle: PaintingStyle.fill,
          ), // your preferred effect
          onDotClicked: (index) {}),
    );
  }
}
