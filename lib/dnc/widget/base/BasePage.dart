import 'package:ent_secutity_app/dnc/widget/base/BaseTabPage.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:ent_secutity_app/dnc/widget/base/Reader.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../main.dart';
import 'DefaultThemeData.dart';

abstract class BasePageState<T extends StatefulWidget> extends BState<T> with RouteAware {
  /// 用于实现page的主题，有缺省值
  ThemeData getThemeData() {
    return DefaultThemeData.get;
  }

  Color getAppBarBackGroundColor() {
    return getThemeData().primaryColor;
  }

  void goBack() {
    Navigator.of(context).pop();
  }

  void goBackWithRefresh() {
    Navigator.pop(context, 'refresh');
  }

  /// appBar阴影
  bool get appBarShadow => true;

  PreferredSizeWidget createAppBar(BuildContext context) => AppBar(

        centerTitle: true,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
        ),
        backgroundColor: getAppBarBackGroundColor(),
        // brightness: Theme.of(context).appBarTheme.brightness,
        title: createTitle(context),
        actions: createActions(context),
        // surfaceTintColor:Colors.white,
        bottom: createBottom(context),
        elevation: appBarShadow ? null : 0.1,
      );

  /// 用于实现Title组件
  Widget createTitle(BuildContext context) =>
      kDebugMode ? Reader(child: Text(strTitle())) : Text(strTitle());

  /// 用于设置title显示的文字
  @protected
  String strTitle();

  /// 用于实现title的Toolbar菜单 一般使用IconButton，PopupMenuButton
  @protected
  List<Widget> createActions(BuildContext context) => [];

  /// 用于实现TabBar
  @protected
  PreferredSize? createBottom(BuildContext context) => null;

  /// 用于实现 FloatingActionButton
  @protected
  Widget? createFloatingActionButton(BuildContext context) => null;

  /// 用于实现页面主体
  @protected
  Widget body(BuildContext context);

  @protected
  @mustCallSuper
  @override
  void initState() {
    super.initState();
  }

  withKeyBoardHidden(Widget content) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: content,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
        data: getThemeData(),
        child: PageData(
          this,
          Container(child: withKeyBoardHidden(getPageWidget(context))),
        ));
  }

  @protected
  void onRefresh() {}

  bool get autoRefresh => false;

  @override
  void didPopNext() {
    onRefresh();
    super.didPopNext();
  }

  @override
  void didChangeDependencies() {
    if (autoRefresh) {
      MyApp.routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
    }
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    if (autoRefresh) {
      MyApp.routeObserver.unsubscribe(this);
    }
    super.dispose();
  }

// - page
  Widget getPageWidget(BuildContext context) {

    return Container(
      child: Scaffold(
        appBar: createAppBar(context),
        floatingActionButton: createFloatingActionButton(context),
        body: body(context),
      ),
    );
  }

  // - data

  Map<String, dynamic> _data = {};
  String _dataSign = "";

  isLoading([List? types]) {
    if (types == null) types = ["default"];
    for (var type in types) {
      if (_data[type + 'state'] == null ? true : _data[type + 'state']) {
        // print('isLoading $type： ${_data[type + 'state']}');
        return true;
      }
    }
    return false;
  }

  requestData([type]) {
    if (type == null) type = "default";
    setState(() {
      _data[type + 'state'] = true;
    });
  }

  setData(data, [type]) {
    if (type == null) type = "default";
    if (mounted) {
      setState(() {
        _data[type + 'state'] = false;
        _data[type] = data;
        _data["u_k"] = UniqueKey().toString();
      });
    }
  }

  getData([type]) {
    if (type == null) type = "default";
    return _data[type];
  }

  _refresh() {
    if (_data.isEmpty) return false;
    final temp = _dataSign;
    _dataSign = _data["u_k"];
    return temp != _data["u_k"];
  }
}

class PageData extends InheritedWidget {
  final _state;

  PageData(this._state, child) : super(child: child);

  T getState<T extends BaseTabPageState>() {
    return _state;
  }

  isLoading([type]) {
    return _state.isLoading(type);
  }

  getData([type, safeValue]) {
    return _state.getData(type) ?? safeValue;
  }

  setData(data, [type]) {
    _state.setData(data, type);
  }

  @override
  bool updateShouldNotify(covariant InheritedWidget oldWidget) {
    return _state._refresh();
  }

  static PageData? of(BuildContext context) {
    InheritedElement? inherited = context.getElementForInheritedWidgetOfExactType<PageData>();
    if (inherited != null) {
      return inherited.widget as PageData;
    } else {
      print('PageData is Null!');
      return null;
    }
  }

  static PageData? reg(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<PageData>();
  }
}
