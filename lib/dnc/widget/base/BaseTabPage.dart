import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../utils/Colors.dart';
import 'BasePage.dart';

abstract class BaseTabPageState<T extends StatefulWidget> extends BasePageState<T>
    with SingleTickerProviderStateMixin {
  GlobalKey tabViewKey = GlobalKey();
  late TabController _tabController;
  dynamic tabSetState;

  @protected
  @mustCallSuper
  @override
  void initState() {
    _tabController = TabController(
      initialIndex: 0,
      vsync: this,
      length: getTabNames().length,
    );
    _tabController.addListener(tabListener);
    super.initState();
  }

  /// 实现所有Tab的名字
  @protected
  List<String> getTabNames();

  /// 实现所有TabWidget；需要注意的是List需要和[getTabNames]的长度、顺序一一对应。
  @protected
  List<Widget> tabBody(BuildContext context);

  /// 当tab发生变化时调用
  @protected
  void onTabChange(int index) {}

  /// 获取当前tab索引
  get tabIndex => _tabController.index;

  /// Tab文本局部刷新
  void setTabState(VoidCallback callback) {
    tabSetState(() {});
  }

  @override
  PreferredSize createBottom(BuildContext context) {
    final Widget? tabHeadWidget = tabHead(context);
    return PreferredSize(
      preferredSize: Size.fromHeight(tabHeadWidget == null ? 40 : 80),
      child: Material(
        color: Colors.white,
        child: Column(
          children: [
            if (tabHeadWidget != null) ...[
              SizedBox(
                height: 39,
                child: tabHeadWidget,
              ),
              Divider(
                color: ColorsI.dividerColor,
                thickness: 1,
                height: 1,
              ),
            ],
            StatefulBuilder(builder: (context, setState) {
              tabSetState = setState;
              return Container(
                alignment: Alignment.center,
                width: double.infinity,
                color: Colors.white,
                child: TabBar(
                    controller: _tabController,
                    indicatorSize: TabBarIndicatorSize.tab,
                    unselectedLabelColor: Theme.of(context).tabBarTheme.unselectedLabelColor,
                    unselectedLabelStyle: Theme.of(context).tabBarTheme.unselectedLabelStyle,
                    labelColor: Theme.of(context).tabBarTheme.labelColor,
                    labelStyle: Theme.of(context).tabBarTheme.labelStyle,
                    isScrollable: getTabNames().length > 4,
                    tabs: getTabs(context)),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget? tabHead(BuildContext context) {
    return null;
  }

  List<Tab> getTabs(BuildContext context) {
    List<Tab> tabs = [];
    List<String> names = getTabNames();

    names.forEach((element) {
      tabs.add(Tab(text: element));
    });
    return tabs;
  }

  @override
  Widget body(BuildContext context) {
    return TabBarView(
      key: tabViewKey,
      controller: _tabController,
      children: tabBody(context),
    );
  }

  void tabRefresh() {
    tabViewKey = GlobalKey();
    setState(() {});
  }

  void tabListener() {
    if (_tabController.index.toDouble() == _tabController.animation?.value) {
      onTabChange(_tabController.index);
    }
  }
}
