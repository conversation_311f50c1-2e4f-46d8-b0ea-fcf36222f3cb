import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/base/MultiStateWidget.dart';
import 'package:ent_secutity_app/base/base_state.dart' as base;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../main.dart';
import '../../utils/WidgetUtils.dart';

abstract class BState<T extends StatefulWidget> extends State<T> {
  Widget body(BuildContext context);
}

abstract class BaseStatefulWidgetState<T extends StatefulWidget> extends BState<T>
    with AutomaticKeepAliveClientMixin, RouteAware {
  @protected
  bool get wantKeepAlive => false;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      // color: Color(0xffEEEEEE), //Theme.of(context).backgroundColor,
      child: body(context),
      // FractionallySizedBox(
      //   alignment: Alignment.center,
      //   heightFactor: zoom,
      //   widthFactor: zoom,
      //   child:
      // ),
    );
  }

  @protected
  Widget body(BuildContext context);

  @protected
  void onRefresh() {}

  bool get autoRefresh => false;

  @override
  void didPopNext() {
    onRefresh();
    super.didPopNext();
  }

  @override
  void didChangeDependencies() {
    if (autoRefresh) {
      MyApp.routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
    }
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    if (autoRefresh) {
      MyApp.routeObserver.unsubscribe(this);
    }
    super.dispose();
  }
}

@Deprecated("CustomItemListWidget")
abstract class BaseListWidgetState<T extends StatefulWidget> extends BaseStatefulWidgetState<T> {
  late BaseRefreshListViewModel _model;

  BaseListWidgetState(BaseRefreshListViewModel model) {
    _model = model;
  }

  // ChangeNotifier get changeNotifier;
  @protected
  bool get enablePullDown => false;

  @protected
  bool get enablePullUp => false;

  @protected
  bool get showSeparator => false;

  @protected
  bool get reverse => false;

  @protected
  Widget get header => RefreshHeader();

  @protected
  Widget get footer => RefreshFooter();

  @override
  void initState() {
    _model.initData();
    super.initState();
  }

  @override
  Widget body(BuildContext context) {
    return ChangeNotifierProvider(
        create: (context) => _model,
        child: Consumer<BaseRefreshListViewModel>(
          builder: (context, _model, child) {
            return SmartRefresher(
                enablePullDown: enablePullDown,
                enablePullUp: enablePullUp,
                reverse: reverse,
                header: header,
                footer: footer,
                controller: _model.refreshController,
                onRefresh: _model.refresh,
                onLoading: _model.loadMore,
                child: _model.list.length > 0
                    ? ListView.builder(
                        itemCount: _model.list.length,
                        itemBuilder: (context, index) {
                          return itemBuilder(context, _model.list[index], index, index == _model.list.length - 1);
                        },
                      )
                    : (_model.state == base.BaseState.LOADING)
                        ? LoadingStateWidget()
                        : WU.emptyWidget(context));
          },
        ));
  }

  Widget itemBuilder(BuildContext context, var item, int index, bool isEnd);

  Widget? separatorBuilder(BuildContext context, int index) {
    return !showSeparator || index < _model.list.length - 1
        ? Divider(color: Theme.of(context).dividerColor, height: 8)
        : null;
  }
}

class RefreshHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ClassicHeader(idleText: "下拉刷新", releaseText: "松手，刷新数据", refreshingText: '正在刷新', completeText: "刷新成功");
  }
}

class RefreshFooter extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ClassicFooter(idleText: "上拉加载", loadingText: '加载中', noDataText: "没有更多数据了!", canLoadingText: "松手，加载数据");
  }
}
