import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DefaultThemeData {
  static ThemeData get get {
    return ThemeData(
      useMaterial3: false,
      primarySwatch: Colors.blue,
      primaryColor: Color(0xff2A80FF),
      primaryColorDark: Color(0x8a000000),
      backgroundColor: Color(0xfff1f1f1),
      dividerColor: Color(0xffF6F6F6),
      cardColor: Colors.white,
      hintColor: Color(0xffAAAAAA),
      /// 选中状态的颜色
      selectedRowColor: Colors.blue,
      /// 未选中状态的颜色
      unselectedWidgetColor: Color(0x81ACACAC),
      iconTheme: IconThemeData(
        color: Color(0x8a000000),
        size: 20,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Color(0xff2A80FF),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Color(0xff2A80FF),
        ),
      ),
      tabBarTheme: TabBarTheme(
        indicatorSize: TabBarIndicatorSize.tab,
        unselectedLabelColor: Color(0xff999999),
        unselectedLabelStyle: TextStyle(fontSize: 16),
        labelColor: Color(0xff2A80FF),
        labelStyle: TextStyle(fontSize: 16),
      ),
      inputDecorationTheme: InputDecorationTheme(
        contentPadding: EdgeInsets.fromLTRB(0, 10, 0, 10),
        hintStyle: TextStyle(
            color: Color(0xffCDCDCD),
            fontSize: 15,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis),
        border: InputBorder.none,
      ),
      cardTheme: CardTheme(
        color: Colors.white,
        shadowColor: Color(0x81ACACAC),
        elevation: 4,
        margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
      ),
      primaryTextTheme: TextTheme(
        // TextFormField inputText
        bodyText1: TextStyle(color: Color(0x8a000000), fontSize: 15),

        /// 用于tile标题
        headline1: TextStyle(
          color: Color(0xff1B1B1B),
          fontSize: 18,
          fontWeight: FontWeight.w500,
          // overflow: TextOverflow.ellipsis,
        ),

        /// 用于Label灰色
        headline2: TextStyle(
          color: Color(0xff666666),
          fontSize: 16,
          fontWeight: FontWeight.w400,
          // overflow: TextOverflow.ellipsis,
        ),

        /// 用于Label黑色
        headline3: TextStyle(
          color: Color(0xff333333),
          fontSize: 16,
          fontWeight: FontWeight.w400,
          // overflow: TextOverflow.ellipsis,
        ),
      ),
      textTheme: TextTheme(
        subtitle1: TextStyle(
            color: Color(0xca000000),
            fontSize: 16,
            fontWeight: FontWeight.w500,
            overflow: TextOverflow.ellipsis),

        /// 用于tile标题
        headline1: TextStyle(
            color: Color(0xca000000),
            fontSize: 16,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis),

        /// 用于tile信息
        headline2: TextStyle(
            color: Color(0x8a000000),
            fontSize: 16,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis),

        /// 用于hint信息
        headline3: TextStyle(
            color: Color(0xffCDCDCD),
            fontSize: 15,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis),

        /// 用于大标题
        headline4: TextStyle(
            color: Color(0x9a000000),
            fontSize: 17,
            fontWeight: FontWeight.w600,
            overflow: TextOverflow.ellipsis),
        headline6: TextStyle(
            color: Colors.blue,
            fontSize: 16,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis),
        bodyText1: TextStyle(
            color: Color(0x8a000000),
            fontSize: 15,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis),
      ),
    );
  }

  static InputDecoration inputDecoration(BuildContext context, String hint) {
    return InputDecoration(
      hintText: hint,
      contentPadding: Theme.of(context).inputDecorationTheme.contentPadding,
      hintStyle: Theme.of(context).inputDecorationTheme.hintStyle,
      border: Theme.of(context).inputDecorationTheme.border,
    );
  }
}
