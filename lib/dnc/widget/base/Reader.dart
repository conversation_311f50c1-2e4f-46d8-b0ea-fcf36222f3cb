import 'package:ent_secutity_app/dnc/utils/StoneUtils.dart';
import 'package:flutter/material.dart';

class Reader extends StatefulWidget {
  final Widget child;
  final bool showMark;
  final bool enable;

  const Reader(
      {super.key,
      required this.child,
      this.showMark = true,
      this.enable = true});

  @override
  State<StatefulWidget> createState() => ReaderState();
}

class ReaderState extends State<Reader> {
  ReaderInfo info = ReaderInfo();

  @override
  void initState() {
    info.init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final builder = Container(
      color: Colors.transparent,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: widget.child,
          ),
          if (widget.showMark)
            Text(
              '★',
              style: TextStyle(color: Colors.blue[500]),
            ),
        ],
      ),
    );

    return widget.enable
        ? GestureDetector(
            child: builder,
            onTap: () {
              final next = info.forward();
              show(info.start, next);
              info.start = next;
            },
            onDoubleTap: () {
              final pre = info.backward();
              show(pre, info.start);
              info.start = pre;
            },
            onLongPress: () {
              print(info.start);
              final next = info.forward();
              show(info.start, next);
              print(info.start);
            },
          )
        : builder;
  }

  show(int start, int end) {
    for (int i = start; i < end; i++) {
      final line = info.lines[i];
      if (line == '------------') {
        print('$line $i');
      } else {
        print(info.lines[i]);
      }
    }
  }
}

class ReaderInfo {
  static final ReaderInfo _singleton = ReaderInfo._internal();

  ReaderInfo._internal();

  factory ReaderInfo() {
    return _singleton;
  }

  List<String> lines = [];
  int start = 159386;

  init() async {
    if (lines.isNotEmpty) {
      return;
    }
    String path = await StoneUtils.basePath + '/hmss.txt';
    lines = List.from(await StoneUtils.readFileByLines(path) ?? []);
    // lines.removeWhere((element) => element == '');
  }

  int backward() {
    for (int i = start; i >= 0; i--) {
      final line = lines[i];
      if (i < start - 10 && line == '------------') {
        return i;
      }
    }
    return 0;
  }

  int forward() {
    for (int i = start; i < lines.length; i++) {
      final line = lines[i];
      if (i > start + 10 && line == '------------') {
        return i;
      }
    }
    return lines.length - 1;
  }
}
