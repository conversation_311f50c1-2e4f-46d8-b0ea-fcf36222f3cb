import 'package:dio/dio.dart';
import 'package:ent_secutity_app/dnc/network/Http.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:flutter/material.dart';

import '../../network/Request.dart';
import '../../utils/DataUtils.dart';

mixin RequestStateProvider<T extends StatefulWidget> on State<T> {
  /// 0 默认正常，1 加载中，2 提交中 ，-1 错误
  int providerState = 0;
  final contentData = {};

  @override
  void initState() {
    refresh(true);
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  refresh([bool first = false]) async {
    final requests = onCreateRequest();
    if (DU.isListEmpty(requests)) return;
    providerState = 1;
    if (onCreateRequestInorder()) {
      for (Request request in requests!) {
        final result = await Http().request(request);
        if (result is DioException) {
          providerState = -1;
          contentData['_error'] = result.message;
          break;
        }
        if (mounted && request.tag != null) {
          contentData[request.tag] = result;
        }
      }
    } else {
      final tagRequests = [];
      final List<Future> apis = [];
      for (Request request in requests!) {
        final api = Http().request(request);
        if (request.tag != null) {
          tagRequests.add(request);
          apis.add(api);
        }
      }
      final results = await Future.wait(apis);
      for (int i = 0; i < tagRequests.length; i++) {
        final tagRequest = tagRequests[i];
        final result = results[i];
        if (result is DioException) {
          providerState = -1;
          contentData['_error'] = result.message;
          break;
        }
        contentData[tagRequest.tag] = result;
      }
    }
    if (providerState == 1) {
      providerState = 0;
      onRequestAfter(first, contentData);
    }

    if (mounted) {
      setState(() {});
    }
  }

  Widget body(BuildContext context) {
    if (providerState == 1) {
      //加载中
      return WU.loadingWidget(context);
    } else if (providerState == -1) {
      //错误页面
      return WU.errorWidget(context, contentData['_error']);
    }
    final empty = contentEmpty(contentData);
    if (empty) {
      //空页面
      return WU.emptyWidget(context);
    } else if (providerState == 2) {
      return Stack(
        children: [
          content(context, contentData),
          WU.commitWidget(context),
        ],
      );
    } else {
      return content(context, contentData);
    }
  }

  Future<dynamic> commit(Request request) async {
    setState(() {
      providerState = 2;
    });
    final result = await Http().request(request);
    if (result is DioException) {
      contentData['_error'] = result.message;
    }
    setState(() {
      providerState = 0;
    });
    return result;
  }

  @protected
  List<Request>? onCreateRequest();

  @protected
  bool onCreateRequestInorder() {
    return false;
  }

  @protected
  bool contentEmpty(dynamic contentData) => false;

  void onRequestAfter(bool first, dynamic contentData) {}

  @protected
  Widget content(BuildContext context, dynamic contentData);
}
