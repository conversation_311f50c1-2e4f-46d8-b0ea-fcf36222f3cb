// ignore_for_file: deprecated_member_use
import 'package:flutter/material.dart';

import '../../utils/DataUtils.dart';
import '../../utils/PlusUtils.dart';
import '../../utils/WidgetUtils.dart';
import 'File.dart';
import 'Image.dart';
import 'Pop.dart';

class ButtonAction {
  final String name;
  final void Function() onTap;
  final String? type;

  ButtonAction({required this.name, required this.onTap, this.type});
}

class ContentButton extends StatelessWidget {
  final String name;
  final onPressed;
  final Size? minSize;
  final EdgeInsetsGeometry? padding;
  final String type;

  const ContentButton({
    Key? key,
    required this.name,
    this.minSize,
    this.onPressed,
    this.padding,
    this.type = 'def',
  }) : super(key: key);

  ContentButton.fromAction({
    Key? key,
    required ButtonAction action,
    this.minSize,
    this.padding,
  })  : name = action.name,
        onPressed = action.onTap,
        type = action.type ?? 'def',
        super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = minSize != null ? minSize : Size(50, 26);
    final _padding = padding != null ? padding : EdgeInsets.all(0);
    Color textColor = Color(0xff2A80FF);
    Color bgColor = Color(0xffBDD3FF);
    Color sideColor = Color(0xff2A80FF);
    if (type == 'grey') {
      textColor = Color(0xff969696);
      bgColor = Color(0xffE3E3E3);
      sideColor = Color(0xff969696);
    }

    return OutlinedButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(size),
        padding: MaterialStateProperty.all(_padding),
        foregroundColor: MaterialStateProperty.all(textColor),
        backgroundColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.pressed)) {
            return Colors.blue[200];
          }
          return bgColor;
        }),
        side: //
            MaterialStateProperty.all(BorderSide(color: sideColor, width: 1)),
      ),
      child: Text(name),
      onPressed: () {
        FocusScope.of(context).unfocus();
        if (onPressed != null) {
          onPressed();
        }
      },
    );
  }
}

class OutlineStyleButton extends StatelessWidget {
  final String name;
  final onPressed;
  final EdgeInsetsGeometry? padding;
  final Color? textColor;
  final Color? outlineColor;
  final Color? backgroundColor;
  final double? borderRadius;

  const OutlineStyleButton(
      {Key? key,
      required this.name,
      this.onPressed,
      this.padding,
      this.textColor,
      this.outlineColor,
      this.backgroundColor,
      this.borderRadius})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(Size(88, 32)),
        padding: MaterialStateProperty.all(padding ?? EdgeInsets.all(0)),
        foregroundColor: MaterialStateProperty.all(textColor ?? Color(0xff2981FF)),
        backgroundColor: MaterialStateProperty.all(backgroundColor ?? Color(0xffE4F0FE)),
        side: MaterialStateProperty.all(
            BorderSide(color: outlineColor ?? Color(0xff2981FD), width: 1)),
      ),
      child: Text(name),
      onPressed: () {
        FocusScope.of(context).unfocus();
        if (onPressed != null) {
          onPressed();
        }
      },
    );
  }
}

class IconTextButton extends StatelessWidget {
  final String name;
  final IconData iconData;
  final onPressed;
  final Size? minSize;
  final Color? color;

  const IconTextButton({
    Key? key,
    required this.iconData,
    required this.name,
    this.minSize,
    this.onPressed,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(minSize),
        padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
        foregroundColor: MaterialStateProperty.all(this.color ?? Colors.white),
      ),
      child: Row(
        children: [
          Icon(iconData),
          Text(name),
        ],
      ),
      onPressed: () {
        FocusScope.of(context).unfocus();
        if (onPressed != null) {
          onPressed();
        }
      },
    );
  }
}

class InfoButton extends StatelessWidget {
  final String name;
  final onPressed;
  final Size? minSize;
  final Color? color;

  const InfoButton({Key? key, required this.name, this.minSize, this.onPressed, this.color})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = minSize != null ? minSize : Size(50, 26);
    return TextButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(size),
        padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
        foregroundColor: MaterialStateProperty.all(this.color ?? Colors.white),
      ),
      child: Text(name),
      onPressed: () {
        FocusScope.of(context).unfocus();
        if (onPressed != null) {
          onPressed();
        }
      },
    );
  }
}

class IConButton extends StatelessWidget {
  final Icon icon;
  final onPressed;
  final Size? size;
  final Color? bgColor;
  final EdgeInsets? padding;

  const IConButton(this.icon, {Key? key, this.size, this.onPressed, this.bgColor, this.padding})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(size ?? Size(32, 32)),
        padding: MaterialStateProperty.all(this.padding ?? EdgeInsets.all(0)),
        backgroundColor: MaterialStateProperty.all(this.bgColor ?? Colors.transparent),
        shape: MaterialStateProperty.all(CircleBorder(side: BorderSide())),
        side: MaterialStateProperty.all(
            BorderSide(color: this.bgColor ?? Colors.transparent, width: 1)),
      ),
      child: icon,
      onPressed: () {
        FocusScope.of(context).unfocus();
        if (onPressed != null) {
          onPressed();
        }
      },
    );
  }
}

class SimpleButton extends StatelessWidget {
  final String name;
  final onPressed;

  SimpleButton({Key? key, required this.name, this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(Size(50, 26)),
        padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
        foregroundColor: MaterialStateProperty.all(Colors.white70),
        backgroundColor: MaterialStateProperty.resolveWith((states) {
          //设置按下时的背景颜色
          if (states.contains(MaterialState.pressed)) {
            return Colors.blue;
          }
          //默认不使用背景颜色
          return Colors.blue[400];
        }),
        elevation: MaterialStateProperty.all(0),
      ),
      child: Text(name),
      onPressed: () {
        FocusScope.of(context).unfocus();
        onPressed();
      },
    );
  }
}

class TileButton extends StatelessWidget {
  final String name;
  final onPressed;
  final EdgeInsetsGeometry? margin;
  final Color? textColor;
  final Color? bgColor;

  TileButton(
      {Key? key, required this.name, this.onPressed, this.margin, Color? textColor, Color? bgColor})
      : this.textColor = textColor ?? Colors.white,
        this.bgColor = bgColor ?? Color(0xff2A80FF),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ElevatedButton(
        style: ButtonStyle(
          textStyle: MaterialStateProperty.all(TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis,
          )),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          minimumSize:
              MaterialStateProperty.all(Size(MediaQuery.of(context).size.width * 4 / 5, 40)),
          padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
          foregroundColor: MaterialStateProperty.all(textColor),
          backgroundColor: MaterialStateProperty.all(bgColor),
          elevation: MaterialStateProperty.all(0),
          shape:
              MaterialStateProperty.all(StadiumBorder(side: BorderSide(style: BorderStyle.none))),
        ),
        child: Text(name),
        onPressed: () {
          FocusScope.of(context).unfocus();
          onPressed();
        },
      ),
    );
  }
}

class ResourceButton extends StatelessWidget {
  final String name;
  final String url;

  const ResourceButton({Key? key, required this.name, required this.url}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(Size(50, 26)),
        padding: MaterialStateProperty.all(const EdgeInsets.all(0)),
        foregroundColor: MaterialStateProperty.all(Color(0xff4383FF)),
        backgroundColor: MaterialStateProperty.resolveWith((states) {
          //设置按下时的背景颜色
          if (states.contains(MaterialState.pressed)) {
            return Color(0xffBDD3FF);
          }
          //默认不使用背景颜色
          return null;
        }),
        side: //
            MaterialStateProperty.all(BorderSide(color: Color(0xff4383FF), width: 1)),
      ),
      child: Text(name),
      onPressed: () async {
        final ext = DU.getExtName(url);
        if (DU.isStrEmpty(ext)) {
          Toast.show('未知文件,请从电脑端查看');
          return;
        } else if (DU.isImageFile(url)) {
          WU.nextPage(
            context,
            ImageViewWrapper(
              FileData.byUrl(url),
              backgroundDecoration: const BoxDecoration(
                color: Colors.black,
              ),
              heroTag: 'ResourceButton',
              scrollDirection: Axis.horizontal,
            ),
          );
          return;
        } else if (DU.isOfficeFile(url)) {
          FilePreviewManager.show(context: context, name: name, url: url);
          return;
        } else {
          Launcher.openWeb(url);
          return;
        }
      },
    );
  }
}

class CustomButton extends StatelessWidget {
  final String name;
  final Function()? onPressed;
  final Color textColor, bgColor, sideColor;
  final EdgeInsets? padding;
  final Size? minimumSize;

  CustomButton(
      {Key? key,
      required this.name,
      this.onPressed,
      Color? textColor,
      Color? bgColor,
      Color? sideColor,
      EdgeInsets? padding,
      this.minimumSize})
      : this.textColor = textColor ?? Color(0xff3A86DF),
        this.bgColor = bgColor ?? Color(0xffCADEFD),
        this.sideColor = sideColor ?? Color(0xffbdd5fc),
        this.padding = padding ?? const EdgeInsets.all(0),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ButtonStyle(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: MaterialStateProperty.all(minimumSize ?? Size(88, 36)),
        padding: MaterialStateProperty.all(padding),
        foregroundColor: MaterialStateProperty.all(textColor),
        backgroundColor: MaterialStateProperty.all(bgColor),
        side: //
            MaterialStateProperty.all(BorderSide(color: sideColor, width: 0.8)),
        elevation: MaterialStateProperty.all(0),
      ),
      child: Container(
        // constraints: BoxConstraints(minWidth: 64,minHeight: 24),
        child: Text(name),
      ),
      onPressed: () {
        FocusScope.of(context).unfocus();
        if (onPressed != null) {
          onPressed!();
        }
      },
    );
  }
}
