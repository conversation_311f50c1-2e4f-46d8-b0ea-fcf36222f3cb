import 'dart:math';

import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:flutter/material.dart';

import '../../utils/Colors.dart';
import 'File.dart';
import 'Image.dart';

class CustomCard extends StatelessWidget {
  final Widget body;
  final String? tip;
  final Color? tipColor;
  final Color? tipBgColor;
  final Color? bgColor;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? borderColor;
  final double? radius;
  final BoxShadow? shadow;

  const CustomCard(
      {super.key,
      required this.body,
      this.tip,
      this.tipColor,
      this.tipBgColor,
      this.bgColor,
      this.onTap,
      this.margin,
      this.padding,
      this.radius,
      this.shadow,
      this.borderColor});

  @override
  Widget build(BuildContext context) {
    final bool showTip = !DU.isStrEmpty(tip);
    double fontSize = 0;
    if (showTip) {
      if (tip!.length >= 6) {
        fontSize = 10;
      } else if (tip!.length == 5) {
        fontSize = 11;
      } else if (tip!.length == 4) {
        fontSize = 12;
      } else {
        fontSize = 13;
      }
    }
    final double h = 22;
    final double w = 88;
    return InkWell(
      child: Container(
        color: ColorsI.transparent,
        margin: margin ?? EdgeInsets.all(12),
        child: Stack(
          clipBehavior: Clip.antiAlias,
          children: [
            Container(
              color: Colors.transparent,
              child: Container(
                padding: padding ?? EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: bgColor ?? Colors.white,
                  border: borderColor != null ? Border.all(color: borderColor!, width: 1.0) : null,
                  borderRadius: BorderRadius.all(Radius.circular(radius ?? 6)),
                  boxShadow: shadow != null ? [shadow!] : null,
                ),
                child: body,
              ),
            ),
            if (showTip)
              Positioned(
                right: -0.1,
                top: sqrt(w * w / 2 - sqrt2 * w * h + h * h),
                child: Transform.rotate(
                  alignment: Alignment.bottomRight,
                  angle: pi / 4,
                  child: Container(
                    color: tipBgColor ?? Colors.blue,
                    width: w,
                    height: h,
                    alignment: Alignment.center,
                    child: Text.rich(TextSpan(
                        text: tip,
                        style: TextStyle(
                          fontSize: fontSize,
                          color: tipColor ?? Colors.white,
                        ))),
                  ),
                ),
              ),
          ],
        ),
      ),
      onTap: onTap,
    );
  }

  static BoxShadow defShadow = BoxShadow(
    offset: Offset(0, 0), //阴影在X轴和Y轴上的偏移
    color: Color(0xFFE8E8E8), //阴影颜色
    blurRadius: 1, //阴影程度
    spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
  );
}

class SimpleCard extends StatelessWidget {
  final Widget body;
  final tip;
  final TextStyle? style;
  final isSingle;
  final onTap;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final double? radius;
  final showShadow;
  final defBcColor = Colors.red.withOpacity(0.6);
  final defFontColor = Colors.white;

  SimpleCard(
      {required this.body,
      this.isSingle = true,
      this.tip,
      this.style,
      this.onTap,
      this.margin,
      this.padding,
      this.radius,
      this.showShadow,
      Key? key})
      : super(key: key);

  Widget build(BuildContext context) {
    late double w, h;
    late TextSpan span;
    bool showTip = tip is String && tip != null && tip != '';
    if (showTip) {
      double fontSize = 14;
      if (tip.length >= 6) {
        fontSize = 10;
      } else if (tip.length == 5) {
        fontSize = 11;
      } else if (tip.length == 4) {
        fontSize = 13;
      }
      span = TextSpan(
          text: tip,
          style: TextStyle(
            fontSize: fontSize,
            color: style != null && style!.color != null ? style!.color : defFontColor,
          ));
      final tp = TextPainter(
        text: span,
        textDirection: TextDirection.ltr,
      )..layout();
      h = 24;
      w = 88;
    }
    final List<BoxShadow> shadow = showShadow == null || !showShadow
        ? []
        : [
            BoxShadow(
              offset: Offset(0, 1), //阴影在X轴和Y轴上的偏移
              color: Colors.black26, //阴影颜色
              blurRadius: 4, //阴影程度
              spreadRadius: 1, //阴影扩散的程度 取值可以正数,也可以是负数
            ),
          ];
    final bgColor =
        style != null && style!.backgroundColor != null ? style!.backgroundColor : defBcColor;
    return GestureDetector(
      child: Container(
        margin: margin != null
            ? margin
            : EdgeInsets.fromLTRB(10, 10, 10, isSingle != null && isSingle ? 10 : 0),
        child: Stack(
          clipBehavior: Clip.antiAlias,
          children: [
            Container(
              color: Colors.transparent,
              child: Container(
                padding: padding ?? EdgeInsets.fromLTRB(8, 2, 8, 2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(radius ?? 5)),
                  color: Theme.of(context).cardTheme.color,
                  boxShadow: shadow,
                ),
                child: body,
              ),
            ),
            showTip
                ? Positioned(
                    right: -0.1,
                    top: sqrt(w * w / 2 - sqrt2 * w * h + h * h),
                    child: Transform.rotate(
                      alignment: Alignment.bottomRight,
                      angle: pi / 4,
                      child: Container(
                        color: bgColor,
                        width: w,
                        height: h,
                        alignment: Alignment.center,
                        child: Text.rich(span),
                      ),
                    ),
                  )
                : Container(),
          ],
        ),
      ),
      onTap: onTap,
    );
  }
}

class TileCard extends StatelessWidget {
  final List<Widget> children;
  final isSingle;
  final tip;
  final style;
  final EdgeInsetsGeometry? margin;
  final double? radius;
  final onTap;

  TileCard(
      {required this.children,
      this.isSingle,
      this.tip,
      this.style,
      this.onTap,
      this.margin,
      this.radius,
      Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SimpleCard(
      body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: children),
      isSingle: isSingle,
      tip: tip,
      style: style,
      margin: margin,
      radius: radius,
      onTap: onTap,
    );
  }
}

class StyleCard1 extends StatelessWidget {
  final String title;
  final String? subTitle;
  final String? tip;
  final TextStyle? style;
  final List<Map<String, dynamic>> info;
  final Function()? onTap;
  final Widget? bottom;
  final List<Widget>? buttons;
  final EdgeInsetsGeometry? margin;
  final double? radius;

  StyleCard1({
    required this.title,
    this.subTitle,
    this.tip,
    this.style,
    required this.info,
    this.margin,
    this.radius,
    this.onTap,
    this.bottom,
    this.buttons,
  });

  @override
  Widget build(BuildContext context) {
    return SimpleCard(
      body: Container(
        alignment: Alignment.topLeft,
        padding: EdgeInsets.all(4),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(4),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  // crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Flexible(
                      child: Text(
                        title,
                        maxLines: 2,
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xff333333),
                          fontWeight: FontWeight.w500,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      // flex: 3,
                    ),
                    if(subTitle != null)
                        ...[
                            SizedBox(height: 5),
                           Text(
                              subTitle!,
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xff333333),
                                fontWeight: FontWeight.w500,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ]
                        else ...[SizedBox(height: 5)],
                  ],
                ),
              ),
              ...info.map<Widget>((data) {
                return subItemBuilder(context, data);
              }).toList(),
              this.bottom != null ? this.bottom! : SizedBox(),
              ...buttons != null
                  ? [
                      Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
                      Container(
                        padding: EdgeInsets.fromLTRB(0, 8, 0, 4),
                        child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: buttons ?? [],
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly),
                      ),
                    ]
                  : [],
            ]),
      ),
      margin: margin,
      radius: radius,
      tip: tip,
      style: style,
      onTap: onTap,
    );
  }

  Widget subItemBuilder(BuildContext context, Map<String, dynamic> data) {
    return Container(
      padding: EdgeInsets.all(2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment:
            (data['single'] ?? true) ? CrossAxisAlignment.center : CrossAxisAlignment.start,
        children: [
          Text(
            DU.isStrEmpty(data['name']) ? '' : '${data['name']}:',
            style: TextStyle(
              color: Color(0xffB0B0B0),
              fontSize: 14,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: DU.isStrEmpty(data['name']) ? 0 : 8),
          Flexible(
              child: Text(
            data['value'] ?? '',
            style: TextStyle(
              color: Color(0xffB0B0B0),
              fontSize: 14,
              // overflow: (data['single'] ?? true) ? TextOverflow.ellipsis : null,
            ),
                softWrap: true,
          )),
        ],
      ),
    );
  }
}

class StyleCard2 extends StatelessWidget {
  final String? image;
  final String? tip;
  final TextStyle? style;
  final List<Map<String, String>> info;
  final Function()? onTap;
  final Widget? bottom;
  final List<Widget>? buttons;
  final EdgeInsetsGeometry? margin;
  final double? radius;

  StyleCard2({
    this.tip,
    this.style,
    required this.info,
    this.margin,
    this.radius,
    this.onTap,
    this.bottom,
    this.buttons,
    this.image,
  });

  @override
  Widget build(BuildContext context) {
    FileData? imageData;
    final showImg = !DU.isStrEmpty(image);
    if (showImg) {
      imageData = FileData.byUrl(image);
    }

    return SimpleCard(
      body: Column(
        children: [
          Container(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (showImg)
                  SizedBox(
                    width: 96,
                    height: 96,
                    child: OperableImage(
                      imageData!,
                      width: 96,
                      height: 96,
                    ),
                  ),
                SizedBox(width: 12),
                Expanded(
                  child: Container(
                    alignment: Alignment.topLeft,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: info.map<Widget>((data) {
                        return subItemBuilder(context, data);
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          this.bottom != null ? this.bottom! : SizedBox(),
          ...buttons != null
              ? [
                  Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1),
                  Container(
                    padding: EdgeInsets.fromLTRB(0, 4, 0, 4),
                    child: Row(
                        mainAxisSize: MainAxisSize.max,
                        children: buttons ?? [],
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly),
                  ),
                ]
              : [],
        ],
      ),
      margin: margin,
      radius: radius,
      tip: tip,
      style: style,
      onTap: onTap,
    );
  }

  Widget subItemBuilder(BuildContext context, Map<String, String> data) {
    return Container(
      padding: EdgeInsets.all(2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            DU.isStrEmpty(data['name']) ? '' : '${data['name']}:',
            style: TextStyle(
              color: Color(0xffB0B0B0),
              fontSize: 14,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: DU.isStrEmpty(data['name']) ? 0 : 8),
          Flexible(
              child: Text(
            data['value'] ?? '',
            style: TextStyle(
              color: Color(0xffB0B0B0),
              fontSize: 14,
              overflow: TextOverflow.ellipsis,
            ),
          )),
        ],
      ),
    );
  }
}
