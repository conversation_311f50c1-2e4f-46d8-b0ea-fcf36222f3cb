import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';

import '../../viewmodel/BaseModel.dart';

class DataWidget<T extends BaseModel> extends StatefulWidget {
  final T model;
  final Widget Function(BuildContext context, T model) builder;

  const DataWidget({super.key, required this.model, required this.builder});

  @override
  State<StatefulWidget> createState() => DataWidgetState();
}

class DataWidgetState<T extends BaseModel> extends State<DataWidget> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (BuildContext context) => widget.model,
      child: Consumer<T>(
        builder: (BuildContext context, model, Widget? child) {
          return widget.builder(context, model);
        },
      ),
    );
  }
}
