import 'dart:typed_data';

import 'package:ent_secutity_app/dnc/utils/StoneUtils.dart';
import 'package:ent_secutity_app/dnc/widget/base/BasePage.dart';
import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:file_preview/file_preview.dart';
import 'package:flutter/material.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../utils/DataUtils.dart';
import '../../utils/PlusUtils.dart';
import '../../utils/WidgetUtils.dart';
import 'Image.dart';

enum FileType { none, image, video, office, other }

class FileData {
  FileType type = FileType.none;
  dynamic data;
  String? url;
  String? name;
  Uint8List? imgData;

  // FileData({String? url, dynamic data, FileType? type})
  //     : url = url,
  //       data = data,
  //       type = type ?? FileType.none;

  FileData.byAssetEntity(AssetEntity asset) {
    if (asset.type == AssetType.image) {
      type = FileType.image;
    } else if (asset.type == AssetType.video) {
      type = FileType.video;
    } else {
      type = FileType.none;
    }
    this.data = asset;
  }

  FileData.byImageData(this.imgData, {this.data}) {
    if (imgData == null) {
      type = FileType.none;
    } else {
      type = FileType.image;
    }
  }

  FileData.byUrl(this.url) {
    type = setTypeByUrl(url);
  }

  FileData.byName(this.url, this.name) {
    type = setTypeByUrl(url);
  }

  static Future<FileData?> byPath(String path) async {
    final data = await StoneUtils.readFileByBytes(path);
    if (data is Uint8List) {
      return FileData.byImageData(data);
    } else {
      return null;
    }
  }

  // static Future<FileData> byAssetData(Asset asset) async {
  //   final dat = await asset.getByteData();
  //   final imgData = dat.buffer.asUint8List();
  //   return FileData.byImageData(imgData, data: asset);
  // }
  //
  // Future<void> getImgData(Asset data) async {}

  setTypeByUrl(String? url) {
    final ext = DU.getExtName(url);
    if (DU.isStrEmpty(ext)) {
      return FileType.none;
    } else if (DU.isImageFile(url)) {
      return FileType.image;
    } else if (DU.isVideo(url)) {
      return FileType.video;
    } else if (DU.isOfficeFile(url)) {
      return FileType.office;
    } else {
      return FileType.other;
    }
  }
}

class FileWidget extends StatelessWidget {
  final FileData data;

  const FileWidget({Key? key, required this.data}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (data.type == FileType.image || data.type == FileType.video) {
      return OperableImage(data);
    } else if (data.type == FileType.office) {
      String? name = data.name ?? DU.getFileName(data.url);
      return GestureDetector(
        child: Stack(
          children: [
            Container(
              padding: EdgeInsets.all(4),
              margin: EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Color(0xfffffcfc),
                borderRadius: BorderRadius.all(Radius.circular(4)),
                // 边框
                border: Border.all(width: 3, color: Color(0xfff9e1df)),
              ),
              alignment: Alignment.center,
              child: Text(
                name ?? '未知文件,请从电脑端查看',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0x8a000000),
                  fontSize: 12,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
            Positioned(
              left: 4,
              top: 10,
              child: Container(
                padding: EdgeInsets.only(left: 8, right: 6, top: 5, bottom: 4),
                // 右上、右下圆角3
                decoration: BoxDecoration(
                  color: Color(0xfffa4e4e),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(3),
                    bottomRight: Radius.circular(3),
                  ),
                ),
                child: Text(
                  getExtName(data.url).toUpperCase(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
        onTap: () {
          if (name != null) {
            FilePreviewManager.show(
                context: context, name: name, url: data.url!);
          }
        },
      );
    } else if (data.type == FileType.other) {
      final name = DU.getFileName(data.url) ?? '';
      return GestureDetector(
        child: Container(
          padding: EdgeInsets.all(4),
          color: Color(0xffEEEEEE),
          alignment: Alignment.center,
          child: Text(
            name.isNotEmpty ? name : '未知文件,请从电脑端查看',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Color(0x8a000000),
              fontSize: 12,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
        onTap: () {
          if (name.isNotEmpty) {
            Launcher.openWeb(data.url!);
          }
        },
      );
    } else {
      return Container(
        color: Color(0xffEEEEEE),
        alignment: Alignment.center,
        child: Text(
          "错误类型",
          style: TextStyle(
            color: Color(0x8a000000),
            fontSize: 12,
            fontWeight: FontWeight.normal,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      );
    }
  }

  // 获取地址后缀
  static String getExtName(String? url) {
    if (url == null) {
      return '';
    }
    final index = url.lastIndexOf('.');
    if (index == -1) {
      return '';
    }
    return url.substring(index + 1);
  }
}

class FilePreviewManager {
  static Future<bool> init() async {
    bool isInit = await FilePreview.tbsHasInit();
    if (!isInit) {
      print('[FilePreviewManager initTBS >]');
      isInit = await FilePreview.initTBS(license: '');
      print('[FilePreviewManager initTBS: $isInit]');
    }
    return isInit;
  }

  static void cleanCache() {
    FilePreview.deleteCache();
  }

  static show(
      {required BuildContext context,
        required String name,
        required String url}) async {
    bool isInit = await init();
    if (!isInit) {
      Toast.show('文件预览初始化中,大约需要30秒,稍后请重新启动软件尝试。');
      return;
    }
    WU.nextPage(
      context,
      FilePreviewPage(
        title: name,
        path: url,
      ),
    );
  }
}

class FilePreviewPage extends StatefulWidget {
  final String title;
  final String path;

  FilePreviewPage({Key? key, required this.path, required this.title})
      : super(key: key);

  @override
  _FilePreviewPageState createState() => _FilePreviewPageState();
}

class _FilePreviewPageState extends BasePageState<FilePreviewPage> {
  @override
  String strTitle() => widget.title;

  @override
  Widget body(BuildContext context) {
    return Container(
      alignment: Alignment.topLeft,
      child: FilePreviewWidget(
        width: double.infinity,
        height: double.infinity,
        path: widget.path,
        callBack: FilePreviewCallBack(
            onShow: () {},
            onDownload: (progress) {},
            onFail: (code, msg) {
              Toast.show('文件预览失败. $code');
            }),
      ),
    );
  }
}
