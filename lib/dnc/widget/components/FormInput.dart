import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../utils/XNumberTextInputFormatter.dart';

class FormInput extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final String hintText;
  final String? contentText;
  final bool? isDecimal;
  FormInput(
      {required this.controller, required this.labelText, required this.hintText, this.contentText, this.isDecimal});

  final TextStyle _labelTextStyle = TextStyle(fontSize: 16, color: Color(0xFF333333));
  final TextStyle _contentTextStyle = TextStyle(fontSize: 16, color: Color(0xFF666666));
  final TextStyle _hintTextStyle = TextStyle(fontSize: 16, color: Color(0xFF999999));

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 1),
      child: Row(
        children: [
          Text(labelText, style: _labelTextStyle),
          SizedBox(width: 1),
          Expanded(
            child: TextField(
              controller: controller,
              textAlign: TextAlign.right,
              style: contentText == null ? _hintTextStyle : _contentTextStyle,
              inputFormatters: isDecimal ?? false
                  ? [XNumberTextInputFormatter(maxIntegerLength: null, maxDecimalLength: 3, isAllowDecimal: true)]
                  : [],
              keyboardType: isDecimal ?? false ? TextInputType.numberWithOptions(decimal: true) : TextInputType.text,
              decoration: InputDecoration(
                counterText: '',
                hintText: hintText,
                hintStyle: _hintTextStyle,
                border: InputBorder.none,
              ),
              maxLength: isDecimal ?? false ? 10 : 30,
            ),
          ),
        ],
      ),
    );
  }
}
