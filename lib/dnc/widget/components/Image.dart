import 'dart:io';
import 'dart:typed_data';

import 'package:ent_secutity_app/dnc/widget/components/Pop.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:video_compress/video_compress.dart';
import 'package:video_player/video_player.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../utils/DataUtils.dart';
import '../../utils/WidgetUtils.dart';
import 'Button.dart';
import 'File.dart';
import 'Widget.dart';

class OperableImage extends StatefulWidget {
  final FileData file;
  final double? width;
  final double? height;
  final bool isSquare;

  const OperableImage(
    this.file, {
    this.width,
    this.height,
    Key? key,
    bool? isSquare,
  })  : this.isSquare = isSquare ?? false,
        super(key: key);

  @override
  _OperableImageState createState() => _OperableImageState();
}

class _OperableImageState extends State<OperableImage> {
  @override
  Widget build(BuildContext context) {
    final side = widget.width ?? widget.height ?? WU.getScreenWidth(context);

    final tag = DateTime.now().toString();
    Widget image;
    if (widget.file.data is AssetEntity) {
      image = AssetEntityImage(
        widget.file.data,
        isOriginal: false,
      );
    } else if (widget.file.imgData != null) {
      image = Image.memory(
        widget.file.imgData!,
        width: widget.isSquare ? side : widget.width ?? side,
        height: widget.isSquare ? side : widget.height ?? side,
        fit: BoxFit.contain,
      );
    } else if (DU.isImageFile(widget.file.url)) {
      image = Image.network(
        widget.file.url!,
        width: widget.width ?? WU.getScreenWidth(context),
        height: widget.height ?? WU.getScreenWidth(context),
        fit: BoxFit.contain,
      );
    } else if (DU.isVideo(widget.file.url)) {
      image = FutureBuilder<Uint8List>(future: Future(() async {
        final data = await VideoCompress.getByteThumbnail(widget.file.url!,
            quality: 100, // default(100)
            position: -1 // default(-1)
            );
        if (data == null) {
          throw ("获取视频错误");
        }
        return data;
      }), builder: (BuildContext context, AsyncSnapshot<Uint8List> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(),
            ),
          );
        }
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.hasError) {
          return Center(
            child: Text(
              "${snapshot.error.toString()}",
              style: TextStyle(color: Colors.white),
            ),
          );
        } else {
          return Image.memory(
            snapshot.data!,
            width: widget.isSquare ? side : widget.width ?? side,
            height: widget.isSquare ? side : widget.height ?? side,
            fit: BoxFit.contain,
          );
        }
      });
    } else {
      image = Container(
        color: Color(0xffEEEEEE),
      );
    }

    return GestureDetector(
      child: Container(
        margin: EdgeInsets.all(4),
        child: Hero(
          tag: tag,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: image,
          ),
        ),
      ),
      onTap: () {
        showImageBrowser(context, widget.file, tag);
      },
    );
  }

  void showImageBrowser(
    BuildContext context,
    FileData file,
    Object tag, {
    bool verticalGallery = false,
  }) async {
    if (widget.file.data is AssetEntity &&
        widget.file.data.type == AssetType.image) {
      widget.file.imgData = await (widget.file.data as AssetEntity).originBytes;
    }
    WU.nextPage(
      context,
      ImageViewWrapper(
        file,
        backgroundDecoration: const BoxDecoration(
          color: Colors.black,
        ),
        heroTag: tag,
        scrollDirection: verticalGallery ? Axis.vertical : Axis.horizontal,
      ),
    );
  }
}

class ImageViewWrapper extends StatefulWidget {
  final FileData file;
  final LoadingBuilder? loadingBuilder;
  final BoxDecoration? backgroundDecoration;
  final dynamic minScale;
  final dynamic maxScale;
  final Object heroTag;
  final PageController pageController;
  final Axis scrollDirection;

  ImageViewWrapper(
    this.file, {
    Key? key,
    this.loadingBuilder,
    this.backgroundDecoration,
    this.minScale,
    this.maxScale,
    required this.heroTag,
    this.scrollDirection = Axis.horizontal,
  })  : pageController = PageController(initialPage: 0),
        super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _ImageViewWrapperState();
  }
}

class _ImageViewWrapperState extends State<ImageViewWrapper> {
  int currentIndex = 0;

  void onPageChanged(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // if (currentIndex == -1) {
    //   currentIndex = 0;
    // }
    return Scaffold(
      body: Container(
        decoration: widget.backgroundDecoration,
        constraints: BoxConstraints.expand(
          height: MediaQuery.of(context).size.height,
        ),
        child: InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: viewBuilder(context, widget.file),
        ),
      ),
    );
  }

  Future<VideoPlayerController> getAssetController(AssetEntity asset) async {
    File? file = await asset.originFile;

    if (file == null) {
      throw ("获取视频错误");
    }
    final controller = VideoPlayerController.file(file);

    await controller.initialize();
    controller.play();
    return controller;
  }

  Future<VideoPlayerController> getUrlController(String url) async {
    final controller = VideoPlayerController.networkUrl(Uri.parse(url));
    await controller.initialize();
    controller.play();
    return controller;
  }

  Widget viewBuilder(BuildContext context, FileData file) {
    if (file.type == FileType.image) {
      return PhotoViewGallery.builder(
        scrollPhysics: const BouncingScrollPhysics(),
        builder: _buildItem,
        itemCount: 1,
        loadingBuilder: widget.loadingBuilder,
        backgroundDecoration: widget.backgroundDecoration,
        pageController: widget.pageController,
        onPageChanged: onPageChanged,
        scrollDirection: widget.scrollDirection,
      );
    } else if (file.type == FileType.video) {
      final asset = widget.file.data;
      Future<VideoPlayerController>? controller;
      if (asset is AssetEntity && asset.type == AssetType.video) {
        controller = getAssetController(asset);
      } else if (!DU.isStrEmpty(widget.file.url)) {
        controller = getUrlController(widget.file.url!);
      }

      if (controller != null) {
        return FutureBuilder(
            future: controller,
            builder: (BuildContext context,
                AsyncSnapshot<VideoPlayerController> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(
                  child: SizedBox(
                    width: 32,
                    height: 32,
                    child: LoadingWidget(),
                  ),
                );
              }
              if (snapshot.connectionState == ConnectionState.done &&
                  snapshot.hasError) {
                return Center(
                  child: Text(
                    "${snapshot.error.toString()}",
                    style: TextStyle(color: Colors.white),
                  ),
                );
              } else {
                print(snapshot.data!.value.aspectRatio);
                return SafeArea(
                  child: Center(
                    child: AspectRatio(
                      aspectRatio: snapshot.data!.value.aspectRatio,
                      child: VideoPlayer(snapshot.data!),
                    ),
                  ),
                );

                // VideoPlayer(snapshot.data!);
              }
            });
      }
    }
    return Container();
  }

  PhotoViewGalleryPageOptions _buildItem(BuildContext context, int index) {
    ImageProvider? image;

    if (widget.file.imgData != null) {
      image = MemoryImage(widget.file.imgData!);
    } else if (widget.file.url != null) {
      image = NetworkImage(widget.file.url!);
    } else {
      image = null;
    }


    return PhotoViewGalleryPageOptions(
      imageProvider: image,
      initialScale: PhotoViewComputedScale.contained,
      minScale: PhotoViewComputedScale.covered * 0.25,
      maxScale: PhotoViewComputedScale.covered * 4,
      heroAttributes: PhotoViewHeroAttributes(tag: widget.heroTag),
    );
  }
}

class ImageGrid extends StatefulWidget {
  final WidgetList<FileData> wl;
  final bool? read;

  const ImageGrid(this.wl, {Key? key, this.read = true}) : super(key: key);

  @override
  State<StatefulWidget> createState() => ImageGridState();
}

class ImageGridState extends State<ImageGrid> {
  @override
  Widget build(BuildContext context) {
    final kScreenWidth = MediaQuery.of(context).size.width;
    final spacing = kScreenWidth / 64;
    final list = widget.wl.list;

    return Container(
      child: GridView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.all(spacing),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
        ),
        itemBuilder: itemBuilder,
        itemCount: list.length,
      ),
    );
  }

  Widget itemBuilder(BuildContext context, int index) {
    return Stack(
      children: [
        FileWidget(data: widget.wl.list[index]),
        Positioned(
          right: 0,
          child: GestureDetector(
            child: !widget.read!
                ? IConButton(
                    Icon(CupertinoIcons.clear_circled, color: Colors.black),
                    size: Size(16, 16),
                    padding: EdgeInsets.fromLTRB(8, 0, 0, 8),
                    onPressed: () {
                      setState(() {
                        widget.wl.list.removeAt(index);
                      });
                    },
                  )
                : SizedBox(),
          ),
        ),
      ],
    );
  }
}
