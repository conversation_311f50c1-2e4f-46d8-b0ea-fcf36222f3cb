import 'package:ent_secutity_app/dnc/viewmodel/BaseModel.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../utils/WidgetUtils.dart';
import '../../viewmodel/ListViewModel.dart';

class ListViewWidget extends StatefulWidget {
  final ListViewModel model;
  final Widget Function(
      BuildContext context, dynamic data, int index, bool isEnd) itemBuilder;
  final bool enablePullDown, enablePullUp;
  final bool reverse;
  final bool autoRefresh;
  final int? pageNum, pageSize;

  const ListViewWidget({
    required this.model,
    required this.itemBuilder,
    bool? enablePullDown,
    bool? enablePullUp,
    bool? reverse,
    bool? autoRefresh,
    this.pageNum,
    this.pageSize,
    super.key,
  })  : this.enablePullDown = enablePullDown ?? false,
        this.enablePullUp = enablePullUp ?? false,
        this.reverse = reverse ?? false,
        this.autoRefresh = autoRefresh ?? false;

  @override
  State<StatefulWidget> createState() => ListViewWidgetState();
}

class ListViewWidgetState extends BaseStatefulWidgetState<ListViewWidget> {
  @override
  void initState() {
    initData();
    super.initState();
  }

  Future<void> initData() async {
    await widget.model.initData();

    widget.model.refresh();
  }

  @override
  Widget body(BuildContext context) {
    return ChangeNotifierProvider(
      create: (BuildContext context) => widget.model,
      child: Consumer<ListViewModel>(
        builder: (BuildContext context, model, Widget? child) {
          return SmartRefresher(
            controller: model.controller,
            enablePullDown: widget.enablePullDown,
            enablePullUp: widget.enablePullUp,
            reverse: widget.reverse,
            header: RefreshHeader(),
            footer: RefreshFooter(),
            onRefresh: () async {
              print("onRefresh");
              await model.loadData(true);
              model.controller.refreshCompleted(resetFooterState: true);
            },
            onLoading: () async {
              await model.loadData(false);
              model.controller.refreshCompleted(resetFooterState: false);
            },
            child: model.state == ModelState.Empty
                ? WU.emptyWidget(context)
                : model.state == ModelState.Fail
                    ? WU.errorWidget(context, model.errorInfo)
                    : model.state == ModelState.Loading
                        ? LoadingStateWidget()
                        : ListView.builder(
                            itemCount: model.list.length,
                            itemBuilder: (context, index) {
                              return widget.itemBuilder(
                                  context,
                                  model.list[index],
                                  index,
                                  index == model.list.length - 1);
                            },
                          ),
          );
        },
      ),
    );
  }
}

class LoadingStateWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        child: Text(
          "加载中...",
          style: TextStyle(color: Colors.grey, fontSize: 14),
        ),
      ),
    );
  }
}

class RefreshHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ClassicHeader(
        idleText: "下拉刷新",
        releaseText: "松手，刷新数据",
        refreshingText: '正在刷新',
        completeText: "刷新成功");
  }
}

class RefreshFooter extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ClassicFooter(
        idleText: "上拉加载",
        loadingText: '加载中',
        noDataText: "没有更多数据了!",
        canLoadingText: "松手，加载数据");
  }
}
