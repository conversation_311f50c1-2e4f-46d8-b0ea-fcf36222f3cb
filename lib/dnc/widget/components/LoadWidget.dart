import 'dart:math';

import 'package:flutter/material.dart';


class LoadRotateWidget extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => LoadRotateWidgetState();
}

class LoadRotateWidgetState extends State<LoadRotateWidget>
    with TickerProviderStateMixin {
  late Animation<double> animationX;
  late AnimationController controllerX;
  late Animation<double> animationY;
  late AnimationController controllerY;

  @override
  void initState() {
    controllerX =
        AnimationController(duration: const Duration(seconds: 2), vsync: this);
    controllerY =
        AnimationController(duration: const Duration(seconds: 3), vsync: this);
    controllerX.addListener(() {
      setState(() {});
    });
    // controllerX.addStatusListener((status) {
    //   if (controllerX.status == AnimationStatus.completed) {
    //     controllerX.reset();
    //     controllerY.forward();
    //   }
    // });
    // controllerY.addListener(() {
    //   setState(() {});
    // });
    // controllerY.addStatusListener((status) {
    //   if (controllerY.status == AnimationStatus.completed) {
    //     controllerY.reset();
    //     controllerX.forward();
    //   }
    // });
    animationX = Tween<double>(begin: 0.0, end: 1.0).animate(controllerX);
    animationY = Tween<double>(begin: 0.0, end: 1.0).animate(controllerY);
    controllerX.repeat();
    controllerY.repeat();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final matrix4 = Matrix4.identity();

    matrix4.rotateX(2 * pi * animationX.value);
    matrix4.rotateY(2 * pi * animationY.value);
    // matrix4.setEntry(1, 0, 0.01);
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white60,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(
            transform: matrix4,
            transformAlignment: Alignment.center,
            child: Image.asset(
              'lib/assets/images/logo.png',
              height: 48,
              width: 48,
            ),
          ),
          // Positioned(
          //   bottom: 4,
          //   child: Text(
          //     '.加载中.',
          //     style: TextStyle(fontSize: 9
          //         , color: Colors.white70),
          //   ),
          // ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    controllerX.dispose();
    controllerY.dispose();
    super.dispose();
  }
}
