import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

import 'Button.dart';
import 'LoadWidget.dart';

mixin SheetMixin {
  showWidget(BuildContext context, Widget widget) async {
    return await showModalBottomSheet(
        backgroundColor: Theme.of(context).cardColor,
        isScrollControlled: true,
        context: context,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10), topRight: Radius.circular(10))),
        builder: (context) {
          return AnimatedPadding(
            padding: MediaQuery.of(context).viewInsets,
            duration: const Duration(milliseconds: 100),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 2 / 3),
              child: Container(
                padding: EdgeInsets.fromLTRB(0, 8, 0, 4),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: ListTile(
                            leading: SizedBox(
                              width: 20,
                              child: Align(
                                alignment: Alignment.centerLeft,
                                child:
                                    Icon(Icons.dns, color: Color(0x8a000000)),
                              ),
                            ),
                            title: Text(
                              title ?? '',
                              style: TextStyle(
                                  inherit: false,
                                  color: Color(0x8a000000),
                                  fontSize: 20,
                                  fontWeight: FontWeight.w400,
                                  textBaseline: TextBaseline.alphabetic,
                                  decoration: TextDecoration.none),
                            ),
                          ),
                        ),
                        SimpleButton(
                          name: "确   定",
                          onPressed: () {
                            if (FocusManager.instance.primaryFocus != null) {
                              FocusManager.instance.primaryFocus!.unfocus();
                            }
                            var result = onCommand();
                            if (result != null) {
                              Navigator.of(context).pop(result);
                            }
                          },
                        ),
                        SizedBox(width: 20)
                      ],
                    ),
                    Divider(
                      color: Color(0xffDDDDDD),
                      height: 1.0,
                      thickness: 1,
                    ),
                    Flexible(
                      child: widget,
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  get title;

  void get showBtn => true;

  dynamic onCommand();
}

class BottomSheetPop {
  static Future show(BuildContext context, Widget widget,
      {String? title, dynamic Function()? onCommand, bool? enableDrag}) async {
    return await showModalBottomSheet(
        backgroundColor: Colors.white,
        isScrollControlled: true,
        enableDrag: enableDrag ?? true,
        context: context,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10), topRight: Radius.circular(10))),
        builder: (context) {
          return Container(
            constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 2 / 3),
            padding: EdgeInsets.fromLTRB(0, 8, 0, 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ListTile(
                        leading: SizedBox(
                          width: 20,
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Icon(Icons.dns, color: Color(0x8a000000)),
                          ),
                        ),
                        title: Text(
                          title ?? '',
                          style: TextStyle(
                              inherit: false,
                              color: Color(0x8a000000),
                              fontSize: 18,
                              fontWeight: FontWeight.w400,
                              textBaseline: TextBaseline.alphabetic,
                              decoration: TextDecoration.none),
                        ),
                      ),
                    ),
                    onCommand != null
                        ? SimpleButton(
                            name: "确   定",
                            onPressed: () {
                              if (FocusManager.instance.primaryFocus != null) {
                                FocusManager.instance.primaryFocus!.unfocus();
                              }
                              var result = onCommand();
                              if (result != null) {
                                Navigator.of(context).pop(result);
                              }
                            },
                          )
                        : SizedBox(),
                    SizedBox(width: 20)
                  ],
                ),
                Divider(
                  color: Color(0xffDDDDDD),
                  height: 1.0,
                  thickness: 1,
                ),
                Flexible(
                  child: widget,
                ),
              ],
            ),
          );
        });
  }
}

//弹窗测试未完成
class Toast {
  static void show(String? msg) {
    SmartDialog.showToast(msg ?? '', displayType: SmartToastType.onlyRefresh);
  }

  static fail(msg) {
    SmartDialog.showToast(msg ?? '', displayType: SmartToastType.onlyRefresh);
  }
}

class Loading {
  static List<int> loadingStack = [];
  static show() {
    loadingStack.add(DateTime.now().millisecond);
    // print('Dialog.showLoading: ${loadingStack.length}');
    WidgetsBinding.instance.addPostFrameCallback(
      (_) {
        print('Dialog.showLoading1: ${loadingStack.length}');
        if (loadingStack.length > 0) {
          SmartDialog.showLoading(
            builder: (BuildContext context) {
              return LoadRotateWidget();
            },
            animationType: SmartAnimationType.fade,
            clickMaskDismiss: false,
            useAnimation: true,
          );
        }
      },
    );
  }

  static dismiss() {
    loadingStack.removeAt(0);
    // print('Dialog.dismiss: ${loadingStack.length}');
    if (loadingStack.length > 0) {
      return;
    }
    SmartDialog.dismiss(status: SmartStatus.loading);
  }
}




class LoadingWidget extends StatefulWidget {
  @override
  LoadingWidgetState createState() => LoadingWidgetState();
}

class LoadingWidgetState extends State<LoadingWidget>
    with TickerProviderStateMixin {
  AnimationController? _controller;

  @override
  void initState() {
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _controller!.forward();
    _controller!.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _controller!.reset();
        _controller!.forward();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: Alignment.center, children: [
      Image.asset(
        'lib/assets/images/logo.png',
        height: 32,
        width: 32,
      ),
      RotationTransition(
        alignment: Alignment.center,
        turns: _controller!,
        child: Image.asset(
          'lib/assets/images/loading.png',
          height: 48,
          width: 48,
        ),
      ),
    ]);
  }

  @override
  void dispose() {
    if (_controller != null) {
      _controller!.dispose();
    }
    super.dispose();
  }
}

class Progress {
  static bool isClose = false;

  static show(ValueNotifier<double> value) {
    isClose = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isClose) return;
      SmartDialog.show(
        tag: 'Progress',
        builder: (BuildContext context) {
          return _ProgressWidget(value: value);
        },
        // displayTime: Duration(seconds: 10),
        keepSingle: true,
        animationType: SmartAnimationType.fade,
        clickMaskDismiss: false,
        useAnimation: true,
        bindPage: false,
      );
    });
  }

  static dismiss() {
    isClose = true;
    SmartDialog.dismiss(tag: 'Progress');
    // SmartDialog.dismiss();
  }
}

class _ProgressWidget extends StatefulWidget {
  final ValueNotifier<double> value;

  const _ProgressWidget({Key? key, required this.value}) : super(key: key);

  @override
  _ProgressWidgetState createState() => _ProgressWidgetState();
}

class _ProgressWidgetState extends State<_ProgressWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    _controller =
        AnimationController(duration: const Duration(seconds: 3), vsync: this);
    _controller.forward();
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _controller.reset();
        _controller.forward();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 96,
        height: 96,
        child: ValueListenableBuilder(
          builder: (context, value, child) {
            return Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  decoration: new BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  height: 64,
                  width: 64,
                  child: ClipOval(
                    child: WaveWidget(
                        config: CustomConfig(
                          colors: [
                            Colors.blue.withOpacity(0.38),
                            Colors.blue.withOpacity(0.4),
                          ],
                          durations: [4000, 6000],
                          heightPercentages: [
                            1.02 - widget.value.value,
                            1 - widget.value.value,
                          ],
                        ),
                        // backgroundColor: Colors.white,
                        backgroundImage: DecorationImage(
                          image:
                              new ExactAssetImage('lib/assets/images/logo.png'),
                        ),
                        size: Size(double.infinity, double.infinity),
                        waveAmplitude: -5,
                        wavePhase: 5,
                        waveFrequency: 1.2,
                        heightPercentage: 0.2),
                  ),
                ),
                Text(
                  '上传中...\n${(widget.value.value * 100).toStringAsFixed(1)}%',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.white,
                  ),
                ),
              ],
            );
          },
          valueListenable: widget.value,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}

class CursorDialog {
  static Future<void> show1(
      BuildContext tagContext, Widget Function(BuildContext) builder,
      {Function()? onDismiss, Alignment? alignment}) async {
    return await SmartDialog.showAttach(
      targetContext: tagContext,
      builder: builder,
      animationType: SmartAnimationType.fade,
      animationTime: Duration(milliseconds: 100),
      alignment: alignment ?? Alignment.bottomRight,
      clickMaskDismiss: true,
      onDismiss: onDismiss,
    );
  }

  static Future<void> show(
      BuildContext tagContext, Widget Function(BuildContext) builder,
      {Function()? onDismiss, Alignment? alignment}) async {
    return await SmartDialog.compatible.showAttach(
      targetContext: tagContext,
      isLoadingTemp: true,
      animationDurationTemp: Duration(milliseconds: 100),
      alignmentTemp: alignment ?? Alignment.bottomRight,
      clickBgDismissTemp: true,
      onDismiss: onDismiss,
      widget: CursorDialogWidget(tagContext, builder),
    );
  }

// Widget matrix =  Container(
//   child: Stack(
//     alignment: Alignment.topLeft,
//     children: [
//       Transform(
//         alignment: Alignment.center,
//         transform: Matrix4.identity()..rotateX(pi)..rotateY(pi),
//         child: Icon(CupertinoIcons.bubble_left_fill,color: Colors.white,size: 128,),
//       )
//
//     ],
//   ),
// );
  static dismiss() {
    SmartDialog.dismiss(status: SmartStatus.attach);
  }
}

class CursorDialogWidget extends StatelessWidget {
  final BuildContext tagContext;
  final Widget Function(BuildContext) builder;

  const CursorDialogWidget(
    this.tagContext,
    this.builder, {
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    RenderBox rb = tagContext.findRenderObject() as RenderBox;
    var offset = rb.size.width / 2;
    return Padding(
      padding: EdgeInsets.all(8),
      child: CustomPaint(
        child: builder(tagContext),
        painter: CursorPainter(offset),
      ),
    );
  }
}

class CursorPainter extends CustomPainter {
  final radius = 6.0;
  final double offset;

  CursorPainter(this.offset);

  @override
  void paint(Canvas canvas, Size size) {
    /// 绘制矩形
    var height = size.height;
    var width = size.width;

    Rect rect = Rect.fromCenter(
        center: Offset(width / 2, height / 2), width: width, height: height);
    Path path = Path()..addRRect(RRect.fromRectXY(rect, radius, radius));

    final double cursorMoveX = 16 / 2;
    final double cursorMoveY = 8;

    Path cursorPath = Path()
      ..moveTo(size.width - offset, 0)
      ..relativeLineTo(cursorMoveX, -cursorMoveY)
      ..relativeLineTo(cursorMoveX, cursorMoveY);

    path = Path.combine(PathOperation.union, cursorPath, path);
    canvas.drawPath(path, Paint()..color = Colors.white.withOpacity(0.92));
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class SDialog {
  static showCenter(Widget Function(BuildContext) builder,
      {Function()? onDismiss, String? tag}) async {
    return await SmartDialog.show(
      tag: tag ?? 'center',
      builder: builder,
      animationType: SmartAnimationType.fade,
      animationTime: Duration(milliseconds: 100),
      clickMaskDismiss: true,
      onDismiss: onDismiss,
    );
  }

  static showAttach(
      {required BuildContext tagContext,
      required Widget Function(BuildContext) builder,
      void Function()? onDismiss}) async {
    return await SmartDialog.showAttach(
      tag: 'attach',
      targetContext: tagContext,
      builder: builder,
      animationType: SmartAnimationType.fade,
      animationTime: Duration(milliseconds: 100),
      alignment: Alignment.bottomRight,
      clickMaskDismiss: true,
      onDismiss: onDismiss,
    );
  }

  static showAlert({String title = '', String content = ''}) {
    SmartDialog.compatible.show(
        widget: AlertWidget(title: title, content: content),
        isLoadingTemp: false,
        tag: 'alertDialog');
  }

  static Future showInfo2({String title = '', String info = ''}) async {
    Completer<dynamic> completer = Completer();
    SmartDialog.show(
        tag: 'showInfo2',
        builder: (BuildContext context) {
          return AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(16, 16, 16, 8),
            title: Text(title ?? '提示'),
            contentPadding: EdgeInsets.fromLTRB(16, 8, 16, 16),
            content: Text(
              info,
              maxLines: 8,
              style: TextStyle(fontSize: 14, color: Colors.black87),
            ),
            actions: <Widget>[
              SimpleButton(
                name: '确定',
                onPressed: () {
                  completer.complete(null);
                  SDialog.dismiss(tag: 'showInfo2');
                },
              )
            ],
          );
        });
  }

  static Future<dynamic> showInfo(BuildContext context,
      {String? title,
      String? info,
      dynamic Function()? onCommand,
      dynamic Function()? onCancel}) {
    Completer<dynamic> completer = Completer();
    showDialog(
        context: context,
        builder: (context) {
          return new AlertDialog(
            titlePadding: EdgeInsets.fromLTRB(16, 16, 16, 8),
            title: new Text(title ?? '提示'),
            contentPadding: EdgeInsets.fromLTRB(16, 8, 16, 16),
            content: Text(
              info ?? '',
              maxLines: 8,
              style: TextStyle(fontSize: 14, color: Colors.black87),
            ),
            actions: <Widget>[
              ...onCancel != null
                  ? [
                      SimpleButton(
                        name: '取消',
                        onPressed: () {
                          completer.complete(onCancel());
                          Navigator.of(context).pop();
                        },
                      )
                    ]
                  : [],
              SimpleButton(
                name: '确定',
                onPressed: () {
                  if (onCommand != null) {
                    completer.complete(onCommand());
                  } else {
                    completer.complete(null);
                    Navigator.of(context).pop();
                  }
                },
              )
            ],
          );
        });
    return completer.future;
  }

  static showInput(BuildContext context,
      {String? title,
      ValueChanged<String>? onCommand,
      bool isMandatory = false}) {
    SmartDialog.show(
      tag: 'input',
      clickMaskDismiss: !isMandatory,
      backDismiss: !isMandatory,
      builder: (context) {
        TextEditingController controller = TextEditingController();
        return new AlertDialog(
          titlePadding: EdgeInsets.fromLTRB(16, 16, 16, 8),
          title: new Text(title ?? '提示'),
          contentPadding: EdgeInsets.fromLTRB(16, 8, 16, 16),
          content: TextField(
            controller: controller,
            maxLines: 1,
            decoration: InputDecoration(
              hintText: '请输入...',
              contentPadding: EdgeInsets.fromLTRB(0, 19, 10, 15),
              hintStyle: TextStyle(color: Colors.grey, fontSize: 14),
              border: InputBorder.none,
            ),
            style: TextStyle(fontSize: 14, color: Colors.black87),
          ),
          actions: <Widget>[
            isMandatory
                ? Container()
                : SimpleButton(
                    name: '取消',
                    onPressed: () {
                      SmartDialog.dismiss(
                          status: SmartStatus.dialog, tag: 'input');
                    },
                  ),
            SimpleButton(
              name: '确定',
              onPressed: () {
                if (controller.text.isEmpty) {
                  return;
                }
                if (onCommand != null) {
                  onCommand(controller.text);
                }
              },
            )
          ],
        );
      },
    );

    // showDialog(
    //     context: context,
    //     builder: (context) {
    //       TextEditingController controller = TextEditingController();
    //       return new AlertDialog(
    //         titlePadding: EdgeInsets.fromLTRB(16, 16, 16, 8),
    //         title: new Text(title ?? '提示'),
    //         contentPadding: EdgeInsets.fromLTRB(16, 8, 16, 16),
    //         content: TextField(
    //           controller: controller,
    //           maxLines: 1,
    //           decoration: InputDecoration(
    //             hintText: '请输入...',
    //             contentPadding: EdgeInsets.fromLTRB(0, 19, 10, 15),
    //             hintStyle: TextStyle(color: Colors.grey, fontSize: 14),
    //             border: InputBorder.none,
    //           ),
    //           style: TextStyle(fontSize: 14, color: Colors.black87),
    //         ),
    //         actions: <Widget>[
    //           SimpleButton(
    //             name: '取消',
    //             onPressed: () {
    //               Navigator.of(context).pop();
    //             },
    //           ),
    //           SimpleButton(
    //             name: '确定',
    //             onPressed: () {
    //               if (controller.text.isEmpty) {
    //                 return;
    //               }
    //               if (onCommand != null) {
    //                 onCommand(controller.text);
    //               }
    //             },
    //           )
    //         ],
    //       );
    //     });
  }

  static beSure(BuildContext context,
      {required String content,
      required Function() onBeSure,
      Function()? onCancel}) {
    showDialog(
        context: context,
        builder: (context) {
          return new AlertDialog(
            title: new Text("提示"),
            content: new Text(
              content,
              style: TextStyle(
                color: Color(0xff333333),
                fontSize: 16,
                fontWeight: FontWeight.w400,
                overflow: TextOverflow.visible,
              ),
            ),
            actions: <Widget>[
              SimpleButton(
                name: '确认',
                onPressed: () {
                  onBeSure();
                  Navigator.of(context).pop();
                },
              ),
              new SimpleButton(
                name: '取消',
                onPressed: () {
                  if (onCancel != null) {
                    onCancel();
                  }
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
  }

  static dismiss({String? tag}) {
    SmartDialog.dismiss(status: SmartStatus.dialog, tag: tag);
  }
}

class AlertWidget extends StatelessWidget {
  final String title;
  final String content;

  AlertWidget({required this.title, required this.content});

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(10),
          width: 300,
          height: 300,
          color: Colors.white,
          child: Column(
            children: <Widget>[
              Container(
                color: Colors.red,
                padding: EdgeInsets.all(10),
                child: Stack(
                  children: <Widget>[
                    Align(
                      alignment: Alignment.center,
                      child: Container(child: Text("${this.title}")),
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: InkWell(
                        child: Icon(Icons.close),
                        onTap: () {
                          SDialog.dismiss(tag: 'alertDialog');
                        },
                      ),
                    )
                  ],
                ),
              ),
              Divider(color: Colors.red),
              Container(
                  padding: EdgeInsets.all(10),
                  width: double.infinity,
                  child: Text(
                    "${this.content}",
                    textAlign: TextAlign.left,
                  ))
            ],
          ),
        ),
      ),
    );
  }
}
