import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/dnc/utils/Colors.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:ent_secutity_app/dnc/utils/WidgetUtils.dart';
import 'package:ent_secutity_app/dnc/widget/advance/PhotoPicker.dart';
import 'package:ent_secutity_app/dnc/widget/base/DefaultThemeData.dart';
import 'package:ent_secutity_app/dnc/widget/components/Button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../advance/MatchingInput.dart';
import 'File.dart';
import 'Pop.dart';
import 'Tile.dart';
import 'Widget.dart';

class BaseEditTile extends StatelessWidget {
  final title;
  final body;
  final bottomLine;
  final readOnly;
  final IconData? icon;
  final bool? important;
  final bool? isCenter;

  BaseEditTile(
      {Key? key,
        this.title,
        this.body,
        this.icon,
        this.bottomLine,
        this.readOnly,
        this.important,
        this.isCenter})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SimpleTile(
      key: key,
      title: title,
      body: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Flexible(
            child: Container(
              alignment: Alignment.centerRight,
              child: Container(
                // color: Colors.amberAccent,
                child: body ?? Container(),
              ),
            ),
          ),
          readOnly ?? false
              ? SizedBox()
              : icon == null
              ? SizedBox(width: 20)
              : Icon(icon, size: 20, color: Color(0x6a000000)),
        ],
      ),
      isCenter: isCenter,
      important: (important ?? false) && !(readOnly ?? false),
      bottomLine: bottomLine,
    );
  }
}

class DataInputTile extends StatelessWidget {
  final title, hint;
  final WidgetData wd;
  final bool readOnly;
  final bottomLine;
  final bool? important;
  final TextInputType? keyboardType;
  final TextEditingController controller;
  final List<TextInputFormatter>? inputFormatters;
  final void Function(WidgetData wd, String value) setter;
  final String Function(WidgetData wd) getter;
  final int? limit;

  DataInputTile(
      {Key? key,
        this.title,
        this.hint,
        this.bottomLine,
        required this.wd,
        bool? readOnly,
        this.important,
        this.limit,
        this.keyboardType,
        this.inputFormatters,
        required this.setter,
        required this.getter})
      : this.readOnly = readOnly ?? false,
        controller = TextEditingController(text: getter(wd)),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return BaseEditTile(
      title: title ?? '',
      body: TextField(
        keyboardType: keyboardType,
        enabled: !readOnly,
        controller: controller,
        textAlign: TextAlign.right,
        style: Theme.of(context).textTheme.headline2,
        maxLines: 1,
        minLines: 1,
        decoration: DefaultThemeData.inputDecoration(
            context, readOnly ? '无' : hint ?? '请填写'),
        onChanged: (v) {
          setter(wd, v);
        },
        inputFormatters: inputFormatters == null
            ? <TextInputFormatter>[
          LengthLimitingTextInputFormatter(limit ?? 128) //限制长度
        ]
            : inputFormatters,
      ),
      readOnly: readOnly,
      bottomLine: bottomLine,
      important: important,
    );
  }
}

class InputTile extends StatelessWidget {
  final title, hint;
  final WidgetData<String> wd;
  final bool readOnly;
  final bottomLine;
  final bool? important;
  final TextInputType? keyboardType;
  final TextEditingController controller;
  final List<TextInputFormatter>? inputFormatters;
  final int? limit;

  InputTile(
      {Key? key,
        this.title,
        this.hint,
        this.bottomLine,
        required this.wd,
        bool? readOnly,
        this.important,
        this.limit,
        this.keyboardType,
        this.inputFormatters})
      : this.readOnly = readOnly ?? false,
        controller = TextEditingController(text: wd.data ?? ''),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return BaseEditTile(
      title: title ?? '',
      body: TextField(
        keyboardType: keyboardType,
        enabled: !readOnly,
        controller: controller,
        textAlign: TextAlign.right,
        style: Theme.of(context).textTheme.headline2,
        maxLines: 1,
        minLines: 1,
        decoration: DefaultThemeData.inputDecoration(
            context, readOnly ? '无' : hint ?? '请填写'),
        onChanged: (v) {
          wd.data = v;
        },
        inputFormatters: inputFormatters == null
            ? <TextInputFormatter>[
          LengthLimitingTextInputFormatter(limit ?? 128) //限制长度
        ]
            : inputFormatters,
      ),
      readOnly: readOnly,
      bottomLine: bottomLine,
      important: important,
    );
  }
}

class ExtensibleInputTile extends StatefulWidget {
  final title, hint, tip;
  final icon;
  final WidgetData<String> wd;
  final bottomLine;
  final readOnly;
  final ValueChanged<String>? onChange;
  final Future<List<String>> Function()? matchingList;
  final bool? important;
  final int? limit;

  ExtensibleInputTile(
      {Key? key,
        this.title,
        this.hint,
        required this.wd,
        this.bottomLine,
        this.icon,
        this.readOnly,
        this.tip,
        this.matchingList,
        this.important,
        this.onChange,
        this.limit})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => ExtensibleInputTileState();
}

class ExtensibleInputTileState extends State<ExtensibleInputTile> {
  final TextEditingController controller = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    controller.text = widget.wd.data ?? '';
    final enabled = widget.readOnly == null || !widget.readOnly;
    return BaseContentTile(
      title: widget.title,
      icon: widget.icon,
      tip: widget.tip,
      bottomLine: widget.bottomLine,
      important: (widget.important ?? false) && enabled,
      body: Container(
        padding: EdgeInsets.fromLTRB(8, 0, 8, 8),
        child: GestureDetector(
          child: TextField(
            enabled: enabled && widget.matchingList == null,
            controller: controller,
            // initialValue: wd.data,
            textAlign: TextAlign.left,
            minLines: 3,
            maxLines: 8,
            decoration: DefaultThemeData.inputDecoration(
                context, widget.readOnly ?? false ? '无' : widget.hint ?? '请填写'),
            style: Theme.of(context).primaryTextTheme.bodyText1,
            enableInteractiveSelection: true,
            onChanged: (v) {
              widget.wd.data = v;
              if (widget.onChange != null) {
                widget.onChange!(v);
              }
            },
            inputFormatters: <TextInputFormatter>[
              LengthLimitingTextInputFormatter(widget.limit ?? 255) //限制长度
            ],
          ),
          onTap: () async {
            WU.removeFocus(context);
            if (enabled && widget.matchingList != null) {
              final value = await WU.nextPage(
                  context,
                  MatchingInputPage(
                    name: widget.title,
                    matchingList: widget.matchingList!,
                    hint: widget.hint,
                    value: controller.text,
                    // controller: controller,
                  ));
              if (value != null) {
                setState(() {
                  controller.text = value;
                  widget.wd.data = controller.text;
                });
                if (widget.onChange != null) {
                  widget.onChange!(controller.text);
                }
              }
            }
          },
        ),
      ),
    );
  }
}

class SingleSelectorTile extends StatefulWidget {
  final String title;
  final Future<List> Function() data;
  final Future<dynamic> Function(List list)? defSel;
  final WidgetData wd;
  final String? k, v;
  final ValueChanged? onChange;
  final Widget Function(dynamic item)? icon;
  final bool readOnly;
  final bottomLine;
  final bool? important;
  final String? hint;
  final Function()? readOnlyOnTap;
  final bool cleanAble;

  SingleSelectorTile({
    Key? key,
    required this.title,
    required this.data,
    required this.wd,
    this.defSel,
    this.k,
    this.v,
    this.onChange,
    this.bottomLine,
    bool? readOnly,
    this.icon,
    this.important,
    this.hint,
    this.readOnlyOnTap,
    this.cleanAble = false,
  })  : this.readOnly = readOnly ?? false,
        super(key: key);

  @override
  State<StatefulWidget> createState() {
    return SingleSelectorTileState();
  }
}

class SingleSelectorTileState extends State<SingleSelectorTile>
    with SheetMixin {
  List dick = [];
  String k = '', v = '';

  @override
  void initState() {
    initData();
    super.initState();
  }

  Future initData() async {
    k = widget.k ?? "code";
    v = widget.v ?? "label";
    dick = await widget.data();
    if (widget.wd.data != null) {
      widget.wd.data = DU.findItemByDict(dick, widget.wd.data, c: k, v: v);
    } else if (widget.defSel != null) {
      widget.wd.data = await widget.defSel!(dick);
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void didUpdateWidget(covariant SingleSelectorTile oldWidget) {
    initData();
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    var str;
    var style;
    if (widget.wd.data == null) {
      str = widget.readOnly ? '无' : widget.hint ?? '请选择';
      style = Theme.of(context).textTheme.headline3;
    } else {
      str = widget.wd.data[v] ?? '';
      style = Theme.of(context).textTheme.headline2;
    }
    return GestureDetector(
      child: BaseEditTile(
        key: widget.key,
        title: widget.title,
        body: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            widget.wd.data != null && widget.icon != null
                ? Container(
              margin: EdgeInsets.only(right: 8),
              child: SizedBox(
                width: 28,
                height: 28,
                child: widget.icon!(widget.wd.data),
              ),
            )
                : SizedBox(),
            Text(str, style: style),
            if (widget.cleanAble && widget.wd.data != null)
              InkWell(
                child: Icon(
                  CupertinoIcons.clear_fill,
                  color: ColorsI.blue66,
                ),
                // Container(
                //   margin: EdgeInsets.only(left: 8),
                //     padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                //     decoration: BoxDecoration(
                //       color: ColorsI.blue66,
                //       borderRadius: BorderRadius.all(Radius.circular(2)),
                //     ),
                //     child: Text(
                //       '清',
                //       style: TextStyle(fontSize: 12, color: Colors.white),
                //     )),
                onTap: () {
                  setState(() {
                    widget.wd.data = null;
                  });
                  if (widget.onChange != null) {
                    widget.onChange!(null);
                  }
                },
              ),
          ],
        ),
        important: widget.important,
        bottomLine: widget.bottomLine,
        readOnly: widget.readOnly,
        icon: Icons.arrow_forward_ios,
      ),
      onTap: () async {
        if (FocusManager.instance.primaryFocus != null) {
          FocusManager.instance.primaryFocus!.unfocus();
        }
        if (widget.readOnly) {
          if (widget.readOnlyOnTap != null) {
            widget.readOnlyOnTap!();
          }
          return;
        }
        final result = await _show(context);
        if (result == null) {
          return;
        }
        setState(() {
          widget.wd.data = result;
        });
        if (widget.onChange != null) {
          widget.onChange!(widget.wd.data);
        }
      },
    );
  }

  @override
  onCommand() {
    return widget.wd.data;
  }

  onSelected(var item) {
    Navigator.of(context).pop(item);
  }

  @override
  get title => widget.title;

  _show(BuildContext context) async {
    return await showWidget(
        context,
        SingleSelectorWidget(
            wd: widget.wd,
            data: () async => dick,
            compare: (a, b) => a[k] == b[k],
            name: (d) => d?[v] ?? '',
            icon: widget.icon,
            onSelected: this.onSelected));
  }
}

class MultipleSelectorTile extends StatefulWidget {
  final String title;
  final Future<List> Function() data;
  final dynamic Function(List list)? defSel;
  final WidgetList wl;
  final String? k, v;
  final ValueChanged? onChange;
  final void Function(List list, dynamic item)? onSelected;
  final Widget Function(dynamic item)? icon;
  final bool readOnly;
  final bottomLine;
  final bool? important;
  final String? hint;

  MultipleSelectorTile({
    Key? key,
    required this.title,
    required this.data,
    required this.wl,
    this.defSel,
    this.k,
    this.v,
    this.onChange,
    this.onSelected,
    this.bottomLine,
    bool? readOnly,
    this.icon,
    this.important,
    this.hint,
  })  : this.readOnly = readOnly ?? false,
        super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MultipleSelectorTileState();
  }
}

class MultipleSelectorTileState extends State<MultipleSelectorTile>
    with SheetMixin {
  List dick = [];
  String k = '', v = '';

  @override
  void initState() {
    initData();
    super.initState();
  }

  void initData() async {
    k = widget.k ?? "code";
    v = widget.v ?? "name";
    dick = await widget.data();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    var str;
    var style;
    if (widget.wl.list.length == 0) {
      str = widget.readOnly ? '无' : '请选择';
      style = Theme.of(context).textTheme.headline3;
    } else {
      str = DU.mergeInfo(widget.wl.list, [this.v]);
      style = Theme.of(context).textTheme.headline2;
    }
    return GestureDetector(
      child: BaseEditTile(
        key: widget.key,
        title: widget.title,
        body: Text(str, style: style),
        important: widget.important,
        bottomLine: widget.bottomLine,
        readOnly: widget.readOnly,
        icon: Icons.arrow_forward_ios,
      ),
      onTap: () async {
        if (FocusManager.instance.primaryFocus != null) {
          FocusManager.instance.primaryFocus!.unfocus();
        }
        if (widget.readOnly) return;
        final items = await _show(context);
        if (items != null) {
          setState(() {
            widget.wl.list = items;
          });
        }
        if (widget.onChange != null) {
          widget.onChange!(widget.wl.list);
        }
      },
    );
  }

  @override
  onCommand() {
    this.widget.wl.changed();
    return this.widget.wl.list;
  }

  onSelected(var item) {
    Navigator.of(context).pop(item);
  }

  @override
  get title => widget.title;

  _show(BuildContext context) async {
    return await showWidget(
        context,
        MultipleSelectorWidget(
          wl: widget.wl,
          data: () async => dick,
          compare: (a, b) => a[k] == b[k],
          onSelected: widget.onSelected ?? (list, item) => null,
          name: (d) => d?[v] ?? '',
        ));
  }
}

class DateTimeSelectorTile extends StatefulWidget {
  final title;
  final bottomLine;
  final WidgetData<DateTime> wd;
  final DateTime? minDate;
  final readOnly;
  final bool? important;
  final String? dateType;
  final DateTime? Function(DateTime date)? onCheck;

  DateTimeSelectorTile(
      {Key? key,
        this.title,
        date,
        this.bottomLine,
        required this.wd,
        this.minDate,
        this.readOnly,
        this.important,
        this.dateType,
        this.onCheck})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return DateTimeSelectorTileState();
  }
}

class DateTimeSelectorTileState extends State<DateTimeSelectorTile>
    with SheetMixin {
  late DateTimePickerWidget picker;

  @override
  Widget build(BuildContext context) {
    var str;
    var style;
    if (widget.wd.data == null) {
      str = '请选择';
      style = Theme.of(context).textTheme.headline3;
    } else {
      str = DateUtil.formatDate(widget.wd.data,
          format: widget.dateType ?? DateFormats.y_mo_d_h_m);
      style = Theme.of(context).textTheme.headline2;
    }
    return GestureDetector(
      child: BaseEditTile(
        key: widget.key,
        title: widget.title,
        body: Text(str, style: style),
        bottomLine: widget.bottomLine,
        readOnly: widget.readOnly,
        icon: Icons.arrow_forward_ios,
        important: (widget.important ?? false) && !(widget.readOnly ?? false),
      ),
      onTap: () async {
        if (widget.readOnly ?? false) return;
        FocusManager.instance.primaryFocus!.unfocus();
        final result = await _show(context);
        if (result != null) {
          setState(() {
            if (widget.onCheck != null) {
              final date = widget.onCheck!(result);
              widget.wd.data = date;
            } else {
              widget.wd.data = result;
            }
          });
        }
      },
    );
  }

  _show(BuildContext context) async {
    picker = DateTimePickerWidget(
      date: widget.wd.data,
      minDate: widget.minDate,
      dateType: widget.dateType,
    );
    return await showWidget(context, picker);
  }

  @override
  onCommand() {
    return picker.dateTime;
  }

  @override
  get title => widget.title;
}

class PhotoSelectorTile extends StatefulWidget {
  final title;
  final readOnly;
  final WidgetList<FileData> wl;
  final maxNum;
  final bottomLine;
  final bool? important;

  PhotoSelectorTile(
      {this.title,
        this.readOnly,
        required this.wl,
        Key? key,
        this.maxNum,
        this.bottomLine,
        this.important})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return PhotoSelectorWidgetState();
  }
}

class PhotoSelectorWidgetState extends State<PhotoSelectorTile> {
  @override
  Widget build(BuildContext context) {
    return BaseContentTile(
      title: widget.title,
      bottomLine: widget.bottomLine,
      important: !widget.readOnly && (widget.important ?? false),
      body: PhotoPickerWidget(
        margin: EdgeInsets.symmetric(vertical: 8),
        wl: widget.wl,
        maxCount: widget.maxNum,
        read: widget.readOnly,
      ),

      // PhotoPickerCountTool(
      //   maxNum: widget.maxNum,
      //   initImage: widget.wl.list,
      //   readonly: widget.readOnly != null && widget.readOnly ? 'yes' : 'no',
      //   callBack: (type) {
      //     setState(() {});
      //   },
      // ),
    );
  }
}
