import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class StepTile extends StatelessWidget {
  final List stepList;
  final String Function(dynamic item) name;
  final int step;
  final int? current;
  final void Function(dynamic item)? onTap;

  const StepTile({
    Key? key,
    required this.stepList,
    required this.name,
    required this.step,
    this.onTap,
    this.current,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(16, 8, 16, 8),
      color: Colors.white,
      child: stepWidgetsBuilder(context),
    );
  }

  Widget stepWidgetsBuilder(BuildContext context) {
    List<Widget> steps = [];
    for (int i = 0; i < stepList.length; i++) {
      final item = stepList[i];
      final active = i <= this.step - 1;
      final current = this.current != null && this.current == i;
      final isEnd = i == stepList.length - 1;
      final step = stepWidget(item, active, current);
      steps.add(step);
    }
    return Stack(
      children: [
        Container(
          margin: EdgeInsets.all(20),
          color: Color(0xffCECECE),
          height: 1,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: steps,
        ),
      ],
    );
  }

  stepWidget(dynamic item, bool active, bool current) {
    return GestureDetector(
      child: Container(
        width: 52,
        // margin: EdgeInsets.fromLTRB(0, 15, 0, 0),
        child: Column(
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: Image(
                image: AssetImage('assets/images/public/${active ? 'step_active.png' : 'step.png'}'),
                fit: BoxFit.fill,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 2),
              child: Text(name(item),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: active ? Color(0xff575757) : Color(0xffCECECE),
                    fontSize: 13,
                    fontWeight: current ? FontWeight.bold : FontWeight.normal,
                  )),
            ),
          ],
        ),
      ),
      onTap: () {
        if (onTap != null) {
          onTap!(item);
        }
      },
    );
  }
}
//
