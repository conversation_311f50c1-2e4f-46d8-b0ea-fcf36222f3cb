import 'package:flutter/cupertino.dart';

class PaddingText extends StatelessWidget {
  final String data;
  final EdgeInsetsGeometry? padding;
  final TextStyle? style;
  final AlignmentGeometry? alignment;

  const PaddingText(this.data, {Key? key, this.padding, this.style, this.alignment})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: alignment ?? Alignment.topLeft,
      padding: padding,
      child: Text(data, style: style),
    );
  }
}
