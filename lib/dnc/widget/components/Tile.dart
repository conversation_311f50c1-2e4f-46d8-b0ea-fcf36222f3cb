import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

import 'Button.dart';

class BaseTile extends StatelessWidget {
  final Widget head, body;
  final bottomLine;
  final double? minHeight;
  final bool? isCenter;

  const BaseTile(
      {Key? key,
      required this.head,
      required this.body,
      this.bottomLine,
      this.minHeight,
      this.isCenter})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).cardColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(vertical: 4),
            constraints: BoxConstraints(
              minHeight: minHeight ?? 48,
            ),
            // margin: EdgeInsets.fromLTRB(8, 10, 8, 10),
            child: Row(
              crossAxisAlignment:
              isCenter ?? true ? CrossAxisAlignment.center : CrossAxisAlignment.start,
              children: [
                Container(
                    alignment: Alignment.centerLeft,
                    constraints: BoxConstraints(
                      minWidth: 80,
                    ),
                    child: head),
                SizedBox(width: 8),
                Expanded(
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: body,
                  ),
                ),
              ],
            ),
          ),
          (bottomLine == null || bottomLine)
              ? Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1)
              : Container(),
        ],
      ),
    );
  }
}

class TitleTile extends StatelessWidget {
  final String title;
  final bool? bottomLine;
  final styleType;
  final double? minHeight;
  final icon;
  final tip;
  final maxLines;
  final bool important;
  final bool isGrey;

  const TitleTile(
      {Key? key,
      required this.title,
      this.bottomLine,
      this.minHeight,
      this.icon,
      this.styleType,
      this.tip,
      this.maxLines,
      bool? important,
      bool? isGrey})
      : this.important = important ?? false,
        this.isGrey = isGrey ?? false,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    // var style = Theme.of(context).textTheme.subtitle1;
    // if (styleType == "blue") {
    //   style = Theme.of(context).textTheme.headline1;
    // }
    return Container(
      color: Theme.of(context).cardColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            constraints: BoxConstraints(
              minHeight: minHeight ?? 48,
              minWidth: 100,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Flexible(
                  child: Text(
                    title,
                    maxLines: maxLines != null ? maxLines : 5,
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.normal,
                        color: isGrey ? Colors.black54 : Colors.black87),
                  ),
                ),
                ...important
                    ? [
                        SizedBox(width: 2),
                        Text(
                          '*',
                          textAlign: TextAlign.left,
                          style: TextStyle(color: Color(0xfffe3d3f), fontSize: 16),
                        ),
                      ]
                    : [],
                SizedBox(width: 12),
                icon != null
                    ? Tooltip(
                        message: tip != null ? tip : "",
                        showDuration: Duration(seconds: 10),
                        child: Icon(icon, size: 16, color: Color(0xff6094f8)),
                        padding: EdgeInsets.all(8),
                        // ToolTip 内边距
                        margin: EdgeInsets.fromLTRB(18, 4, 18, 18), // ToolTip 外边距
                      )
                    : Container(),
              ],
            ),
          ),
          (bottomLine == null || bottomLine!)
              ? Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1)
              : Container(),
        ],
      ),
    );
  }
}

class SimpleTile extends StatelessWidget {
  final String title;
  final double? titleSize;
  final Widget body;
  final bool? isCenter;
  final bool? bottomLine;
  final bool important;
  final double? minHeight;

  const SimpleTile(
      {Key? key,
      required this.title,
      required this.body,
      this.bottomLine,
      bool? important,
      this.isCenter,
      this.minHeight,
      this.titleSize})
      : this.important = important ?? false,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return BaseTile(
      key: key,
      head: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            textAlign: TextAlign.left,
            style: TextStyle(
                fontSize: titleSize ?? 16, fontWeight: FontWeight.normal, color: Colors.black54),
          ),
          ...important
              ? [
                  SizedBox(width: 2),
                  Text(
                    '*',
                    textAlign: TextAlign.left,
                    style: TextStyle(color: Color(0xfffe3d3f), fontSize: 15),
                  ),
                ]
              : [],
        ],
      ),
      body: body,
      minHeight: minHeight,
      isCenter: isCenter,
      bottomLine: bottomLine,
    );
  }
}

class TextTile extends StatelessWidget {
  final String title, info;
  final bool? bottomLine;
  final String? defValue;

  const TextTile(
      {Key? key, required this.title, required this.info, this.bottomLine, this.defValue})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SimpleTile(
        key: key,
        title: title,
        body: Text(info.isEmpty ? defValue ?? "无" : info,
            textAlign: TextAlign.left, maxLines: 10, style: Theme.of(context).textTheme.headline2),
        bottomLine: bottomLine);
  }
}

class InfoTile extends StatelessWidget {
  final String title;
  final String? info;

  const InfoTile({Key? key, required this.title, this.info}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title + ':',
            style: TextStyle(color: Color(0xff636164), fontSize: 15.0),
          ),
          SizedBox(width: 2),
          Expanded(
            child: Text(
              info ?? '',
              style: TextStyle(color: Color(0xff636164), fontSize: 15.0),
            ),
          ),
        ],
      ),
    );
  }
}

class HtmlTile extends StatelessWidget {
  final title, info;
  final bottomLine;

  const HtmlTile({Key? key, this.title, this.info, this.bottomLine}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BaseContentTile(
        key: key,
        title: title,
        body: info != null ? Html(shrinkWrap: true, data: info) : Container(),
        bottomLine: bottomLine);
  }
}

class FileTile extends StatelessWidget {
  final title;
  final List files;
  final bottomLine;

  const FileTile({Key? key, this.title, required this.files, this.bottomLine}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SimpleTile(
        key: key,
        title: title,
        body: files.length > 0
            ? Wrap(
                spacing: 5,
                runSpacing: 5,
                children: files.map((file) {
                  return ResourceButton(
                    name: file['name'],
                    url: file['url'],
                  );
                }).toList(),
              )
            : Text("暂无附件",
                textAlign: TextAlign.left,
                maxLines: 3,
                style: Theme.of(context).textTheme.headline2),
        bottomLine: bottomLine);
  }
}

class BaseContentTile extends StatelessWidget {
  final title;
  final icon;
  final body;
  final tip;
  final bottomLine;
  final bool? important;

  BaseContentTile(
      {Key? key, this.title, this.body, this.bottomLine, this.icon, this.tip, this.important})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 12),
          TitleTile(
              title: title,
              icon: icon,
              tip: tip,
              styleType: "blue",
              bottomLine: false,
              important: important,
              isGrey: true,
              minHeight: 0.0),
          body,
          (bottomLine == null || bottomLine)
              ? Divider(color: Theme.of(context).dividerColor, height: 1, thickness: 1)
              : Container(),
        ],
      ),
    );
  }
}

class TextContentTile extends StatelessWidget {
  final title;
  final icon;
  final info;
  final bottomLine;

  TextContentTile({Key? key, this.title, this.info, this.bottomLine, this.icon}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BaseContentTile(
      title: title,
      icon: icon,
      bottomLine: bottomLine,
      body: Container(
        padding: EdgeInsets.fromLTRB(8, 16, 8, 16),
        child: Text(info == "" ? "无" : (info == null ? "" : info),
            textAlign: TextAlign.left, maxLines: 10, style: Theme.of(context).textTheme.bodyText1),
      ),
    );
  }
}
