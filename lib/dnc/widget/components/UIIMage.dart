import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'dart:ui' as ui;

class UIIMage extends StatelessWidget {
  final Widget Function(BuildContext context, ui.Image? image) builder;

  const UIIMage({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    Image image = Image.asset(
      'assets/images/testmap.png',
      fit: BoxFit.none,
    );
    // void Function(ImageInfo image, bool synchronousCall)
    Completer<ui.Image> completer = Completer<ui.Image>();
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener(
        (ImageInfo info, bool synchronousCall) {
          completer.complete(info.image);
        },
      ),
    );
    return FutureBuilder<ui.Image>(
      future: completer.future,
      builder: (BuildContext context, AsyncSnapshot<ui.Image> snapshot) {
        return builder(context, snapshot.data);
      },
    );
  }
}
