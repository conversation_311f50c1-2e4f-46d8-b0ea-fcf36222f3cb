import 'dart:convert';

import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/dnc/widget/base/BaseWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker/Picker.dart';

import '../../utils/DataUtils.dart';

class WidgetData<T> {
  T? data;
  var arg = {};

  WidgetData([data]) {
    this.data = data;
  }

  get isEmpty {
    return data is String ? DU.isStrEmpty(data as String) : data == null;
  }
}

class WidgetList<T> {
  List<T> list = [];
  List<T> changes = [];
  var arg = {};

  WidgetList([dynamic list]) {
    setList(list);
  }

  setList([dynamic list]) {
    dynamic temp;
    if (list is String) {
      temp = DU.isStrEmpty(list) ? [] : json.decode(list);
    } else {
      temp = list;
    }
    this.list = List<T>.from(temp != null ? temp.map((e) => e) : []);
  }

  changeInit() {
    this.changes.clear();
    this.changes.addAll(this.list);
  }

  void changed() {
    list.clear();
    list.addAll(this.changes);
  }

  get isEmpty {
    return list.isEmpty;
  }
}

class SingleSelectorWidget extends StatefulWidget {
  final WidgetData? wd;
  final Future<List> Function() data;
  final bool Function(dynamic, dynamic) compare;
  final String Function(dynamic) name;
  final Widget Function(dynamic item)? icon;
  final ValueChanged? onSelected;

  SingleSelectorWidget(
      {Key? key,
      this.wd,
      required this.data,
      required this.compare,
      required this.name,
      this.onSelected,
      this.icon})
      : super(key: key);

  @override
  State<SingleSelectorWidget> createState() {
    return SingleSelectorWidgetState();
  }
}

class SingleSelectorWidgetState extends State<SingleSelectorWidget> {
  List data = [];
  WidgetData wd = new WidgetData();

  SingleSelectorWidgetState();

  @override
  void initState() {
    initData();
    super.initState();
  }

  Future initData() async {
    this.data = await widget.data();
    if (widget.wd != null) {
      wd = widget.wd!;
    }
    setState(() {});
  }

  @override
  void didUpdateWidget(covariant SingleSelectorWidget oldWidget) {
    initData();
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 12),
      child: ListView.builder(
        shrinkWrap: true,
        itemBuilder: (BuildContext context, int index) {
          final item = data[index];
          bool isSel = wd.data != null && widget.compare(item, wd.data);
          return InkWell(
            child: Container(
              color: Colors.transparent,
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(right: 16),
                    child: Icon(
                      isSel
                          ? Icons.radio_button_checked
                          : Icons.radio_button_unchecked,
                      color: isSel
                          ? Theme.of(context).selectedRowColor
                          : Theme.of(context).unselectedWidgetColor,
                      size: 16,
                    ),
                  ),
                  widget.icon != null
                      ? Container(
                          margin: EdgeInsets.only(right: 16),
                          child: SizedBox(
                            width: 32,
                            height: 32,
                            child: widget.icon!(item),
                          ),
                        )
                      : SizedBox(),
                  Expanded(
                    child: Text(
                      widget.name(item),
                      style: TextStyle(
                        color: isSel
                            ? Theme.of(context).selectedRowColor
                            : Theme.of(context).textTheme.headline1!.color,
                        fontSize:
                            Theme.of(context).textTheme.headline1!.fontSize,
                        fontWeight:
                            Theme.of(context).textTheme.headline1!.fontWeight,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            onTap: () {
              if (!isSel) {
                setState(() {
                  wd.data = item;
                });
                return;
              }
              if (this.widget.onSelected != null) {
                return this.widget.onSelected!(wd.data);
              }
            },
          );

          // ListTile(
          //   leading: SizedBox(
          //     width: 20,
          //     child: Align(
          //       alignment: Alignment.centerLeft,
          //       child: Icon(
          //         isSel ? Icons.radio_button_checked : Icons.radio_button_unchecked,
          //         color: isSel
          //             ? Theme.of(context).selectedRowColor
          //             : Theme.of(context).unselectedWidgetColor,
          //         size: 16,
          //       ),
          //     ),
          //   ),
          //   title: Text(
          //     widget.name(item),
          //     style: TextStyle(
          //       color: isSel
          //           ? Theme.of(context).selectedRowColor
          //           : Theme.of(context).textTheme.headline1!.color,
          //       fontSize: Theme.of(context).textTheme.headline1!.fontSize,
          //       fontWeight: Theme.of(context).textTheme.headline1!.fontWeight,
          //     ),
          //   ),
          //   onTap: () {
          //     if (isSel) {
          //       if (this.widget.onSelected != null) {
          //         return this.widget.onSelected!(wd.data);
          //       }
          //     } else {
          //       setState(() {
          //         wd.data = item;
          //       });
          //     }
          //   },
          // );
        },
        itemCount: data.length,
      ),
    );
  }
}

//=======================================================================
class MultipleSelectorWidget extends StatefulWidget {
  final WidgetList wl;
  final Future<List> Function() data;
  final bool Function(dynamic, dynamic) compare;
  final String Function(dynamic) name;
  final void Function(List list, dynamic item)? onSelected;
  final bool? selectAll;

  MultipleSelectorWidget(
      {Key? key,
      required this.wl,
      required this.data,
      required this.compare,
      required this.name,
      this.selectAll,
      this.onSelected})
      : super(key: key);

  @override
  State<MultipleSelectorWidget> createState() {
    return MultipleSelectorWidgetState();
  }
}

class MultipleSelectorWidgetState extends State<MultipleSelectorWidget> {
  List data = [];

  @override
  void initState() {
    initData();
    super.initState();
  }

  Future initData() async {
    this.data = await widget.data();
    widget.wl.changeInit();
    setState(() {});
  }

  @override
  void didUpdateWidget(covariant MultipleSelectorWidget oldWidget) {
    initData();
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final showSelectAll =
        widget.selectAll != null && widget.selectAll! && data.length > 0;
    final isSelectAll =
        widget.wl.changes.length > 0 && widget.wl.changes.length == data.length;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        showSelectAll
            ? InkWell(
                child: Container(
                  color: Colors.transparent,
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  child: Row(
                    children: [
                      Icon(
                        isSelectAll
                            ? Icons.check_box
                            : Icons.check_box_outline_blank,
                        color: isSelectAll
                            ? Theme.of(context).selectedRowColor
                            : Theme.of(context).unselectedWidgetColor,
                        size: 16,
                      ),
                      SizedBox(width: 12),
                      Text(
                        '全选',
                        style: TextStyle(
                          color: isSelectAll
                              ? Theme.of(context).selectedRowColor
                              : Theme.of(context).textTheme.headline2!.color,
                          fontSize:
                              Theme.of(context).textTheme.headline1!.fontSize,
                          fontWeight:
                              Theme.of(context).textTheme.headline1!.fontWeight,
                        ),
                      ),
                    ],
                  ),
                ),
                onTap: () {
                  setState(() {
                    if (isSelectAll) {
                      widget.wl.changes.clear();
                    } else {
                      widget.wl.changes.clear();
                      widget.wl.changes.addAll(data);
                    }
                  });
                },
              )

            // ListTile(
            //         leading: SizedBox(
            //           width: 20,
            //           child: Align(
            //             alignment: Alignment.centerLeft,
            //             child: Icon(
            //               isSelectAll
            //                   ? Icons.check_box
            //                   : Icons.check_box_outline_blank,
            //               color: isSelectAll
            //                   ? Theme.of(context).selectedRowColor
            //                   : Theme.of(context).unselectedWidgetColor,
            //               size: 16,
            //             ),
            //           ),
            //         ),
            //         title: Text(
            //           '全选',
            //           style: TextStyle(
            //             color: isSelectAll
            //                 ? Theme.of(context).selectedRowColor
            //                 : Theme.of(context).textTheme.headline2!.color,
            //             fontSize: Theme.of(context).textTheme.headline1!.fontSize,
            //             fontWeight:
            //                 Theme.of(context).textTheme.headline1!.fontWeight,
            //           ),
            //         ),
            //         onTap: () {
            //           setState(() {
            //             if (isSelectAll) {
            //               widget.wl.changes.clear();
            //             } else {
            //               widget.wl.changes.clear();
            //               widget.wl.changes.addAll(data);
            //             }
            //           });
            //         },
            //       )
            : Container(),
        showSelectAll
            ? Divider(
                color: Theme.of(context).dividerColor, height: 1, thickness: 1)
            : Container(),
        Expanded(
            child: ListView.builder(
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            final item = data[index];
            final isSelected = compare(widget.wl.changes, item);
            return InkWell(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                child: Row(
                  children: [
                    Icon(
                      isSelected
                          ? Icons.check_box
                          : Icons.check_box_outline_blank,
                      color: isSelected
                          ? Theme.of(context).selectedRowColor
                          : Theme.of(context).unselectedWidgetColor,
                      size: 16,
                    ),
                    SizedBox(width: 12),
                    Text(
                      widget.name(item),
                      style: TextStyle(
                        color: isSelected
                            ? Theme.of(context).selectedRowColor
                            : Theme.of(context).textTheme.headline1!.color,
                        fontSize:
                            Theme.of(context).textTheme.headline1!.fontSize,
                        fontWeight:
                            Theme.of(context).textTheme.headline1!.fontWeight,
                      ),
                    ),
                  ],
                ),
              ),
              onTap: () {
                if (widget.onSelected != null) {
                  widget.onSelected!(widget.wl.changes, item);
                }
                setState(() {
                  if (isSelected) {
                    remove(widget.wl.changes, item);
                  } else {
                    widget.wl.changes.add(item);
                  }
                });
              },
            );
            ListTile(
              leading: SizedBox(
                width: 20,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Icon(
                    isSelected
                        ? Icons.check_box
                        : Icons.check_box_outline_blank,
                    color: isSelected
                        ? Theme.of(context).selectedRowColor
                        : Theme.of(context).unselectedWidgetColor,
                    size: 16,
                  ),
                ),
              ),
              title: Text(
                widget.name(item),
                style: TextStyle(
                  color: isSelected
                      ? Theme.of(context).selectedRowColor
                      : Theme.of(context).textTheme.headline1!.color,
                  fontSize: Theme.of(context).textTheme.headline1!.fontSize,
                  fontWeight: Theme.of(context).textTheme.headline1!.fontWeight,
                ),
              ),
              onTap: () {
                if (widget.onSelected != null) {
                  widget.onSelected!(widget.wl.changes, item);
                }
                setState(() {
                  if (isSelected) {
                    remove(widget.wl.changes, item);
                  } else {
                    widget.wl.changes.add(item);
                  }
                });
              },
            );
          },
          itemCount: data.length,
          // separatorBuilder: (BuildContext context, int index) => Divider(
          //   color: Theme.of(context).dividerColor,
          //   height: 1.0,
          //   thickness: 1,
          // ),
        )),
      ],
    );
  }

  compare(var selects, var item) {
    for (var sel in selects) {
      if (widget.compare(sel, item)) {
        return true;
      }
    }
    return false;
  }

  remove(var selects, var item) {
    for (var sel in selects) {
      if (widget.compare(sel, item)) {
        selects.remove(sel);
        return;
      }
    }
  }
}

//=======================================================================

// ignore: must_be_immutable
class DateTimePickerWidget extends StatelessWidget {
  final date;
  final minDate;
  final String? dateType;
  late Picker picker;

  DateTimePickerWidget({Key? key, this.date, this.minDate, this.dateType})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final time = date ??
        DateUtil.getDateTime(DateUtil.formatDate(DateTime.now(),
            format: DateFormats.y_mo_d_h_m));
    int type = 0;
    switch (dateType) {
      case 'yyyy-MM-dd':
        type = PickerDateTimeType.kYMD;
        break;
      default:
        type = PickerDateTimeType.kYMDHM;
        break;
    }
    picker = Picker(
      adapter: DateTimePickerAdapter(
          type: type,
          isNumberMonth: true,
          yearSuffix: "年",
          monthSuffix: "月",
          daySuffix: "日",
          minValue: minDate,
          value: time),
      hideHeader: true,
      selectedTextStyle: TextStyle(color: Colors.blue),
      itemExtent: 50,
      height: 200,
    );
    return picker.makePicker();
  }

  get dateTime {
    return (picker.adapter as DateTimePickerAdapter).value;
  }
}

//=====================================================================
class CustomItemListWidget extends StatefulWidget {
  final BaseRefreshListViewModel model;
  final Widget Function(
      BuildContext context, dynamic data, int index, bool isEnd) itemBuilder;
  final enablePullDown, enablePullUp, showSeparator, reverse;
  final bool? keepAlive;
  final bool? autoRefresh;

  CustomItemListWidget(
      {required this.model,
      required this.itemBuilder,
      this.enablePullDown = false,
      this.enablePullUp = false,
      this.showSeparator = true,
      this.reverse = false,
      Key? key,
      this.keepAlive,
      this.autoRefresh})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return CustomItemListWidgetState(model);
  }
}

class CustomItemListWidgetState
    extends BaseListWidgetState<CustomItemListWidget> {
  final BaseRefreshListViewModel model;

  CustomItemListWidgetState(this.model) : super(model);

  @protected
  bool get enablePullDown => widget.enablePullDown;

  @protected
  bool get enablePullUp => widget.enablePullUp;

  @protected
  bool get showSeparator => widget.showSeparator;

  bool get reverse => widget.reverse;

  @override
  Widget itemBuilder(BuildContext context, item, int index, bool isEnd) {
    return widget.itemBuilder(context, item, index, isEnd);
  }

  @protected
  bool get wantKeepAlive => widget.keepAlive ?? false;

  @override
  bool get autoRefresh => widget.autoRefresh ?? false;

  @override
  void onRefresh() {
    model.refresh();
  }
}
