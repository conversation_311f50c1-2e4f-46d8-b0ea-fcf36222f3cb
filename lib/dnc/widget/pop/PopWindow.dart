import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import '../../utils/WidgetUtils.dart';

class PopWindow {
  static const String tag = 'popWindow';

  static Future showWindow({
    required String title,
    required Widget body,
    void Function()? onClose,
  }) async {
    return await SmartDialog.show(
      tag: tag,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(4.0)),
          ),
          width: WU.getScreenWidth(context) * 0.8,
          height: WU.getScreenHeight(context) * 0.5,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: 16,
                      ),
                    ),
                    Spacer(),
                    GestureDetector(
                      child: Container(
                        padding: EdgeInsets.all(4),
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.grey,
                        ),
                      ),
                      onTap: () {
                        if (onClose != null) {
                          onClose();
                        }
                        close();
                      },
                    ),
                  ],
                ),
              ),
              Divider(height: 1, thickness: 1, color: Colors.black12),
              Flexible(
                child: Container(
                  padding: EdgeInsets.fromLTRB(16, 8, 16, 16),
                  child: body,
                ),
              ),
            ],
          ),
        );
      },
      animationType: SmartAnimationType.fade,
      animationTime: Duration(milliseconds: 100),
      clickMaskDismiss: true,
      onDismiss: onClose,
    );
  }

  static close() {
    SmartDialog.dismiss(status: SmartStatus.dialog, tag: tag);
  }
}
