import 'dart:io';

import 'package:flutter/material.dart'; //导入了Material UI组件库
import 'package:flutter/services.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

import 'common/route.dart';
import 'dnc/logger/LoggerManager.dart';
import 'dnc/widget/base/DefaultThemeData.dart';

const String ProtocolVersions = '1.0.0';

void main() async {
  WidgetsFlutterBinding.ensureInitialized(); //不加这个强制横/竖屏会报错
  SystemChrome.setPreferredOrientations([
    // 强制竖屏
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown
  ]);
  if (Platform.isAndroid) {
    //设置状态栏颜色
    SystemUiOverlayStyle systemUiOverlayStyle = SystemUiOverlayStyle(
      statusBarColor: Color(0xff2A80FF),
    );
    SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    // print("systemUiOverlayStyle");
  }
  await LoggerManager().initLogger();
  runApp(MyApp()); //应用入口。
}

final GlobalKey<NavigatorState> navigatorKey = new GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  // This widget is the root of your application.
  static final RouteObserver<PageRoute> routeObserver =
      RouteObserver<PageRoute>();

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      //应用名称
      title: '智慧物流系统',
      //蓝色主题
      theme: DefaultThemeData.get,
      initialRoute: '/login',
//        initialRoute: '/risk_identify',
      navigatorKey: navigatorKey,
      navigatorObservers: [MyApp.routeObserver, FlutterSmartDialog.observer],
      builder: (context, child) {
        child = FlutterSmartDialog(child: child);

        ///设置文字大小不随系统设置改变
        child = MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
            child: child);
        return child;
      },
      onGenerateRoute: onGenerateRoute,
      debugShowCheckedModeBanner: false,
    );
  }

  //固定写法
  Route? onGenerateRoute(RouteSettings settings) {
    final String name = settings.name!;
    final Function? pageContentBuilder = routes[name];
    if (pageContentBuilder != null) {
      if (settings.arguments != null) {
        final Route route = MaterialPageRoute(
            builder: (context) =>
                pageContentBuilder(context, arguments: settings.arguments),
            settings: settings);
        return route;
      } else {
        final Route route = MaterialPageRoute(
            builder: (context) => pageContentBuilder(context),
            settings: settings);
        return route;
      }
    } else {
      return null;
    }
  }
}
