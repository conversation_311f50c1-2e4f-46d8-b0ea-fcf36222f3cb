import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';

class QuestionInfo {
  String id = '';
  String questionName = '';
  String questionAnswer = '';
  String questionTip = '';
  String analysis = '';
  String questionA = '';
  String questionB = '';
  String questionC = '';
  String questionD = '';
  String createTime = '';

  QuestionInfo();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'questionName': questionName,
      'questionAnswer': questionAnswer,
      'questionTip': questionTip,
      'analysis': analysis,
      'questionA': questionA,
      'questionB': questionB,
      'questionC': questionC,
      'questionD': questionD,
    };
  }

  QuestionInfo.fromJson(Map<String, dynamic> json)
      : id = DU.safeString(json, ['id']),
        questionName = DU.safeString(json, ['questionName']),
        questionAnswer = DU.safeString(json, ['questionAnswer']),
        questionTip = DU.safeString(json, ['questionTip']),
        analysis = DU.safeString(json, ['analysis']),
        questionA = DU.safeString(json, ['questionA']),
        questionB = DU.safeString(json, ['questionB']),
        questionC = DU.safeString(json, ['questionC']),
        questionD = DU.safeString(json, ['questionD']),
        createTime = DU.safeString(json, ['createTime']);
}
