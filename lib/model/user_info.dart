//危化品
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';

class UserInfo {
  int age = 0;
  String username = '';
  String name = '';
  int sex = 0;
  String endId = '';
  String email = '';
  String mobile = '';
  String departmentName = '';
  String departmentId = '';
  String duty = '';
  String id = '';

  UserInfo();

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'sex': sex,
      'age': age,
      'mobile': mobile,
      'email': email,
      'departmentId': departmentId,
      "duty": duty,
      "state": "1",
      "type": "1",
      'id': id,
    };
  }

  UserInfo.fromJson(Map<String, dynamic> json)
      : username = DU.safeString(json, ['username']),
        endId = DU.safeString(json, ['entId']),
        name = DU.safeString(json, ['name']),
        sex = DU.safeValue<int>(json, ['sex'], 0),
        age = DU.safeValue<int>(json, ['age'], 0),
        duty = DU.safeString(json, ['duty']),
        email = DU.safeString(json, ['email']),
        mobile = DU.safeString(json, ['mobile']),
        departmentName = DU.safeString(json, ['department', 'name']),
        departmentId = DU.safeString(json, ['department', 'id']),
        id = DU.safeString(json, ['id']);
}
