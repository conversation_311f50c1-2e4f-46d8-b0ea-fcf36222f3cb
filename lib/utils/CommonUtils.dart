import 'package:ent_secutity_app/common/colors.dart';
import 'package:flutter/cupertino.dart';

class CommonUtils {
  //拼ulr
  static String getPicUrls(List list) {
    String url = "";
    for (int i = 0; i < list.length; i++) {
      if (i == 0) {
        url = list[0];
      } else if (i == url.length - 1) {
        url = url + list[i];
      } else {
        url = url + list[i] + ",";
      }
    }
    return url;
  }

  static formLable(String text) {
    return Container(
        width: 135,
        child: Align(
            alignment: Alignment.topLeft,
            child: Text(text,
                textAlign: TextAlign.left,
                style: TextStyle(
                  color: MyColors.gray999,
                  height: 2,
                  fontSize: 16,
                ))));
  }

  static String noEmtpyStr(String text) {
    if (text == null) {
      return "";
    } else {
      return text;
    }
  }
}
