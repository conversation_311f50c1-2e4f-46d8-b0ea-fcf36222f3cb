import 'package:flutter/cupertino.dart';

class TextUtils {
  static bool isEmpty(String value) {
    return value == null || value == "";
  }

  static String getNonStr(String value, {String? ifNonReplace}) {
    return isEmpty(value) ? (ifNonReplace == null ? "" : ifNonReplace) : value;
  }

  // 课程、考试列表中 字段名称
  static Widget listText(String text) {
    return Text(text,
        style: TextStyle(
          color: Color(0xff636164),
          fontSize: 15.0,
          height: 2,
        ));
  }

  // 课程、考试列表中 字段内容
  static Widget listExpandedText(String text) {
    return Text(
      text,
      style: TextStyle(
        color: Color(0xff333134),
        fontSize: 15.0,
        height: 2,
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 2, //最大行数
    );
  }
}
