import 'package:ent_secutity_app/common/colors.dart';
import 'package:ent_secutity_app/widget/flat_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_picker/flutter_picker.dart';

enum DateType {
  YMD, // y, m, d
  YM, // y ,m
  YMD_HM, // y, m, d, hh, mm
  YMD_AP_HM, // y, m, d, ap, hh, mm
}

typedef StringClickCallback = void Function(int selectIndex, Object selectStr);
typedef DicClickCallback = void Function(Object code, Object name);
typedef TimeClickCallback = void Function(Object time);

class Utils {
  //气体检测频率
  static const gasCheck = ["无", "有"];

  //任务-优先级

  static const priority = ["1", "2", "3", "4", "5"];

  static const sexCheck = ["男", "女"];

  //计划任务-评估结果
  static const judgeResult = ["通过", "整改", "取消"];

  //施工人员--单位类型
  static const entType = ["企业内部", "承包商"];

  static String full = "yyyy-MM-dd HH:mm:ss";
  static String y_mo_d_h_m = "yyyy-MM-dd HH:mm";
  static String y_mo_d = "yyyy-MM-dd";
  static String y_mo = "yyyy-MM";
  static String mo_d = "MM-dd";
  static String mo_d_h_m = "MM-dd HH:mm";
  static String h_m_s = "HH:mm:ss";
  static String h_m = "HH:mm";

  static String zh_full = "yyyy年MM月dd日 HH时mm分ss秒";
  static String zh_y_mo_d_h_m = "yyyy年MM月dd日 HH时mm分";
  static String zh_y_mo_d = "yyyy年MM月dd日";
  static String zh_y_mo = "yyyy年MM月";
  static String zh_mo_d = "MM月dd日";
  static String zh_mo_d_h_m = "MM月dd日 HH时mm分";
  static String zh_h_m_s = "HH时mm分ss秒";
  static String zh_h_m = "HH时mm分";

  static getPriorityName(code) {
    switch (code) {
      case 1:
        return "最低";
      case 2:
        return "较低";
      case 3:
        return "普通";
      case 4:
        return "紧急";
      case 5:
        return "非常紧急";
      default:
        return "";
    }
  }

  // //toast
  // static toastCenter(msg) {
  //   Fluttertoast.cancel();
  //   Fluttertoast.showToast(
  //       msg: msg,
  //       toastLength: Toast.LENGTH_SHORT,
  //       gravity: ToastGravity.CENTER,
  //       timeInSecForIosWeb: 1,
  //       backgroundColor: Colors.black87,
  //       textColor: Colors.white,
  //       fontSize: 14.0);
  // }
  //
  // static toastFail(msg) {
  //   Fluttertoast.cancel();
  //   Fluttertoast.showToast(
  //       msg: msg,
  //       toastLength: Toast.LENGTH_SHORT,
  //       gravity: ToastGravity.CENTER,
  //       timeInSecForIosWeb: 1,
  //       backgroundColor: Colors.red,
  //       textColor: Colors.white,
  //       fontSize: 14.0);
  // }

  //list分割宽线
  static DividerList() {
    return Divider(color: MyColors.dividerColor, height: 10);
  }

  //获取图片url 逗号分割
  static getImgStr(imgs) {
    var imgsStr = '';
    for (var i = 0; i < imgs.length; i++) {
      if (i == imgs.length - 1) {
        imgsStr += imgs[i];
      } else {
        imgsStr += imgs[i] + ",";
      }
    }
    return imgsStr;
  }

  static textAreaStyle() {
    return InputDecoration(
      hintText: '请输入内容',
      contentPadding: EdgeInsets.fromLTRB(5, 10, 10, 5),
      hintStyle: TextStyle(color: MyColors.inputHint, fontSize: 17),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: MyColors.inputLine,
          width: 1.0,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: MyColors.inputLine,
          width: 1.0,
        ),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(
          color: MyColors.inputLine,
          width: 1.0,
        ),
      ),
    );
  }

  //下划线
  static textAreaStyleUnderline() {
    return InputDecoration(
      hintText: '请输入',
      contentPadding: EdgeInsets.fromLTRB(5, 10, 10, 5),
      hintStyle: TextStyle(color: MyColors.inputHint, fontSize: 17),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: MyColors.inputLine,
          width: 1.0,
        ),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: MyColors.inputLine,
          width: 1.0,
        ),
      ),
      // border:
      // UnderlineInputBorder(
      //   borderSide: BorderSide(
      //     color: MyColors.inputLine,
      //     width: 1.0,
      //   ),
      // ),
    );
  }

  //确认框
  static showAlertDialog(
      BuildContext context, String content, Function confirmCallback) {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return new AlertDialog(
            title: new Text("提示"),
            content: new Text(
              content,
              style: TextStyle(
                color: Color(0xff333333),
                fontSize: 16,
                fontWeight: FontWeight.w400,
                overflow: TextOverflow.visible,
              ),
            ),
            actions: <Widget>[
              new FlatButton(
                onPressed: () {
                  confirmCallback();
                  Navigator.of(context).pop();
                },
                child: new Text("确认"),
              )
            ],
          );
        });
  }

  //提示框
  static showConfirmDialog(
      BuildContext context, String content, Function confirmCallback) {
    showDialog(
        context: context,
        builder: (context) {
          return new AlertDialog(
            title: new Text("提示"),
            content: new Text(
              content,
              style: TextStyle(
                color: Color(0xff333333),
                fontSize: 16,
                fontWeight: FontWeight.w400,
                overflow: TextOverflow.visible,
              ),
            ),
            actions: <Widget>[
              new FlatButton(
                onPressed: () {
                  confirmCallback();
                  Navigator.of(context).pop();
                },
                child: new Text("确认"),
              ),
              new FlatButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: new Text("取消"),
              ),
            ],
          );
        });
  }

  //提示框
  static showReturnDialog(
      BuildContext context, String content, Function confirmCallback) {
    TextEditingController text = new TextEditingController();

    FocusNode _contentFocusNode = FocusNode();
    showDialog(
        context: context,
        builder: (context) {
          return new AlertDialog(
            content: new TextFormField(
              focusNode: _contentFocusNode,
              controller: text,
              maxLines: 5,
              minLines: 1,
              decoration: InputDecoration(
                hintText: content,
                contentPadding: EdgeInsets.fromLTRB(0, 10, 10, 10),
                hintStyle: TextStyle(color: MyColors.inputHint, fontSize: 15),
//                border: InputBorder.none,
              ),
            ),
            actions: <Widget>[
              new FlatButton(
                onPressed: () {
                  confirmCallback(text.text);
                  Navigator.of(context).pop();
                },
                child: new Text("确认"),
              ),
              new FlatButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: new Text("取消"),
              ),
            ],
          );
        });
  }

  //拨打电话
//  static  call(String tel) async {
//    String url = 'tel:'+tel;
//    if (await canLaunch(url)) {
//      await launch(url);
//    } else {
//      throw '号码异常';
//    }
//  }

  //查询展示 描述文字
  static digestText(String title, String content) {
    if (content == null) {
      content = "";
    }

    bool colorflag = false;

    if ('未整改' == content) {
      colorflag = true;
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(title,
            style: TextStyle(
              color: Color(0xff636164),
              fontSize: 15.0,
              height: 2,
            )),
        Expanded(
          child: Text(
            content,
            style: TextStyle(
              color: colorflag ? MyColors.red : Color(0xff333134),
              fontSize: 15.0,
              height: 2,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 2, //最大行数
          ),
        ),
      ],
    );
  }

  //查询展示 描述文字
  static htmlText(String title, String content) {
    if (content == null) {
      content = "";
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(title,
            style: TextStyle(
              color: Color(0xff636164),
              fontSize: 15.0,
              height: 2,
            )),
        Expanded(
          child: Html(
            // 渲染的数据
            data: content,
          ),
        ),
      ],
    );
  }

  //详情页 描述文字
  static detailTextRight(String title, String content) {
    if (content == null || content == '') {
      content = '暂无数据';
    }

    return Row(
      children: [
        Container(
          width: 130,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(title,
                textAlign: TextAlign.left,
                style: TextStyle(
                  color: Color(0xff343235),
                  height: 2,
                  fontSize: 16,
                )),
          ),
        ),
        Expanded(
          child: Container(
            alignment: Alignment.centerRight,
            margin: EdgeInsets.fromLTRB(0, 10, 0, 10),
            child: Text(content,
                textAlign: TextAlign.left,
                maxLines: 100,
                style: TextStyle(
                  color: Color(0xff444444),
                  height: 2,
                  fontSize: 16,
                )),
          ),
        ),
      ],
    );
  }

  //详情页 描述文字
  static detailText(String title, String content) {
    if (content == null || content == '') {
      content = '暂无数据';
    }

    return Column(
      children: [
        Align(
          alignment: Alignment.centerLeft,
          child: Text(title,
              textAlign: TextAlign.left,
              style: TextStyle(
                color: Color(0xff343235),
                height: 2,
                fontSize: 16,
              )),
        ),
        Container(
          alignment: Alignment.centerLeft,
          margin: EdgeInsets.fromLTRB(0, 10, 0, 10),
          child: Text(content,
              textAlign: TextAlign.left,
              maxLines: 100,
              style: TextStyle(
                color: Color(0xff676568),
                fontSize: 16,
              )),
        ),
        Divider(color: MyColors.dividerColor, height: 2, thickness: 1.0),
      ],
    );
  }

  //详情页 描述文字  -在一行
  static detailTextRow(
      [String? title, String? content, bool showDivider = true]) {
    if (content == null || content == '') {
      content = '暂无数据';
    }

    return Container(
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 130,
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(title!,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                        color: Color(0xff878689),
                        height: 2,
                        fontSize: 16,
                      )),
                ),
              ),
              Expanded(
                child: Container(
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.fromLTRB(0, 10, 0, 10),
                  child: Text(content,
                      textAlign: TextAlign.left,
                      maxLines: 100,
                      style: TextStyle(
                        color: Color(0xff444444),
                        height: 2,
                        fontSize: 16,
                      )),
                ),
              ),
            ],
          ),
          showDivider
              ? Divider(color: MyColors.dividerColor, height: 2, thickness: 1.0)
              : SizedBox(),
        ],
      ),
    );
  }

  //item 标签描述
  static itemLable(String text) {
    if (text == null || text == '') {
      text = '暂无数据';
    }
    return Container(
        width: double.infinity,
        child: Text(text,
            textAlign: TextAlign.left,
            style: TextStyle(
              color: Colors.black,
              height: 2,
              fontSize: 16,
            )));
  }

  //详情页 可点击拨打电话的文字
//  static telText(String title, String content) {
//    return Row(
////      crossAxisAlignment: CrossAxisAlignment.start,
//      children: <Widget>[
//        Text("手机号：", style: TextStyle(
//          color: MyColors.title,
//          fontSize: 15.0,
//          height: 2,
//        )),
//        Expanded(
//          child: GestureDetector(
//              child: Text(content, style: TextStyle(
//                  fontSize: 16.0,
//                  height: 2,
//                  color: Colors.blue
//              )),
//              onTap: () {
//                call(content.toString());
//              }
//          ),
//        ),
//      ],
//    );
//  }

  static pushText(String title, String content, void Function() pushMethod) {
    if (content == null) {
      content = "";
    }
    return Row(
//      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(title,
            style: TextStyle(
              color: MyColors.title,
              fontSize: 15.0,
              height: 2,
            )),
        Expanded(
          flex: 2,
          child: GestureDetector(
              child: Text(content,
                  style:
                      TextStyle(fontSize: 16.0, height: 2, color: Colors.blue)),
              onTap: () {
                pushMethod();
              }),
        ),
      ],
    );
  }

  //appbar 菜单
  static menuItem(String text, String id) {
    return new PopupMenuItem<String>(
        value: id,
        child: new Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            new Text(text),
          ],
        ));
  }

  //Lable 标签描述
  static formLable(String text) {
    return Container(
        child: Align(
            alignment: Alignment.topLeft,
            child: Text(text,
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: Colors.black54,
                    height: 2,
                    fontSize: 16,
                    fontWeight: FontWeight.w400))));
  }

  //表单input 必填标志
  static must() {
    return Text("*",
        style: TextStyle(
          color: Colors.red,
          height: 1.5,
        ));
  }

  //表单input 非必填
  static un_must() {
    return Text("  ",
        style: TextStyle(
          height: 1.5,
        ));
  }

  //表单input样式
  static inputStyle(String text) {
    return InputDecoration(
      hintText: text,
//      labelText: text,
//      labelStyle: TextStyle(
//          color: Colors.black54,
//          fontSize: 16
//      ),
      contentPadding: EdgeInsets.fromLTRB(5, 8, 0, 8),
      hintStyle: TextStyle(color: MyColors.title),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: MyColors.dividerColor,
          width: 1, //边线宽度为1
        ),
      ),
      focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(
        color: Colors.black54,
        width: 1, //宽度为1
      )),
    );
  }

  //数量 样式
  static countStyle(String text) {
    return Container(
      width: 25,
      height: 25,
      margin: EdgeInsets.fromLTRB(0, 10, 10, 0),
      padding: EdgeInsets.fromLTRB(3, 3, 3, 3),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        color: MyColors.theme,
      ),
      child: Text(text,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
          )),
    );
  }

  //表单input样式
  static inputDisable(String text) {
    return InputDecoration(
      hintText: text,
//      labelText: text,
//      labelStyle: TextStyle(
//          color: Colors.black54,
//          fontSize: 16
//      ),
      enabled: false,
      contentPadding: EdgeInsets.fromLTRB(5, 8, 0, 8),

      filled: true,
      fillColor: Colors.black12,
      hintStyle: TextStyle(color: MyColors.grey),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: MyColors.dividerColor,
          width: 1, //边线宽度为1
        ),
      ),
      focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(
        color: Colors.black54,
        width: 1, //宽度为1
      )),
    );
  }

  //单列选择器
  static showPickerModal(
    BuildContext context,
    PickerData,
    StringClickCallback clickCallBack,
  ) {
    print(PickerData);

    new Picker(
        adapter: PickerDataAdapter<dynamic>(pickerData: PickerData),
        changeToFirst: true,
        hideHeader: false,
        cancelText: '取消',
        confirmText: '确定',
        title: new Text("请选择"),
        itemExtent: 50,
        height: 200,
        selectedTextStyle: TextStyle(
            color: Colors.blue, fontSize: 20, fontWeight: FontWeight.w400),
        onConfirm: (Picker picker, List value) {
          print(value);
          //          print(picker.getSelectedValues());
          var i = int.parse(
              value.toString().replaceAll("[", "").replaceAll("]", ""));
          var selectStr =
              picker.adapter.text.replaceAll("[", "").replaceAll("]", "");
          clickCallBack(i, selectStr);
        }).showModal(context);
  }

  //单列选择器
  static dicPickerModal(
    BuildContext context,
    PickerData,
    DicClickCallback clickCallBack,
  ) {
    new Picker(
        adapter: PickerDataAdapter<String>(data: getPickerData(PickerData)),
        changeToFirst: true,
        hideHeader: false,
        cancelText: '取消',
        confirmText: '确定',
        title: Text("请选择"),
        itemExtent: 50,
        height: 200,
        selectedTextStyle: TextStyle(
            color: Colors.blue, fontSize: 20, fontWeight: FontWeight.w400),
        onConfirm: (Picker picker, List value) {
          var i = int.parse(
              value.toString().replaceAll("[", "").replaceAll("]", ""));
          var selectStr = PickerData[i]["name"];
          var selectCode = PickerData[i]["code"];
//          print("    print(value);-----------");
//          print(i);
//          print(selectCode);
          clickCallBack(selectCode, selectStr);
        }).showModal(context);
  }

  //声明返回的类型为 List对象
  static List<PickerItem<String>> getPickerData(List data,
      {String label = 'name',
      String value = 'code',
      String children = 'children'}) {
    //遍历生成相对应的PickerItem
    List<PickerItem<String>> list = data.map((item) {
      //pickerItem的类型为PickerItem<String> 不指明类型会报错
      PickerItem<String> pickerItem = PickerItem(
          text: Text(item[label]),
          value: item[value],
          //如果子项有数据就递归，否则子项的children就是空List
          children: item.containsKey(children) && item[children].length > 0
              ? getPickerData(item[children])
              : []);
      //最终把遍历的每一项返出去
      return pickerItem;
    }).toList();
    return list;
  }

  //单列选择器
  static dataPickerModal(
    BuildContext context,
    PickerData,
    DicClickCallback clickCallBack,
  ) {
    new Picker(
        adapter: PickerDataAdapter<String>(data: getPickerData2(PickerData)),
        changeToFirst: true,
        hideHeader: false,
        cancelText: '取消',
        confirmText: '确定',
        title: new Text("请选择"),
        itemExtent: 50,
        height: 200,
        selectedTextStyle: TextStyle(
            color: Colors.blue, fontSize: 20, fontWeight: FontWeight.w400),
        onConfirm: (Picker picker, List value) {
          var i = int.parse(
              value.toString().replaceAll("[", "").replaceAll("]", ""));
          var selectStr = PickerData[i]["name"];
          var selectCode = PickerData[i]["id"];
//          print("    print(value);-----------");
//          print(i);
//          print(selectCode);
          clickCallBack(selectCode, selectStr);
        }).showModal(context);
  }

  //声明返回的类型为 List对象
  static List<PickerItem<String>> getPickerData2(List data,
      {String label = 'name',
      String value = 'id',
      String children = 'children'}) {
    //遍历生成相对应的PickerItem
    List<PickerItem<String>> list = data.map((item) {
      //pickerItem的类型为PickerItem<String> 不指明类型会报错
      PickerItem<String> pickerItem = PickerItem(
          text: Text(item[label]),
          value: item[value],
          //如果子项有数据就递归，否则子项的children就是空List
          children: item.containsKey(children) && item[children].length > 0
              ? getPickerData2(item[children])
              : []);
      //最终把遍历的每一项返出去
      return pickerItem;
    }).toList();
    return list;
  }

  //日期选择器
  static void showDatePicker(
    BuildContext context,
    TimeClickCallback clickCallBack, {
    DateType? dateType,
    DateTime? maxValue,
    DateTime? minValue,
    DateTime? value,
    int? yearBegin = 1900,
    int? yearEnd = 2100,
  }) {
    int timeType;
    if (dateType == DateType.YM) {
      timeType = PickerDateTimeType.kYM;
    } else if (dateType == DateType.YMD_HM) {
      timeType = PickerDateTimeType.kYMDHM;
    } else if (dateType == DateType.YMD_AP_HM) {
      timeType = PickerDateTimeType.kYMD_AP_HM;
    } else {
      timeType = PickerDateTimeType.kYMD;
//      timeType = PickerDateTimeType.YMD_HM;
    }

    new Picker(
        adapter: DateTimePickerAdapter(
          type: timeType,
          isNumberMonth: true,
          yearSuffix: "年",
          monthSuffix: "月",
          daySuffix: "日",
          strAMPM: const ["上午", "下午"],
          maxValue: maxValue,
          minValue: minValue,
          value: value ?? DateTime.now(),
          yearBegin: yearBegin,
          yearEnd: yearEnd,
        ),
        title: new Text("请选择"),
//          selecteds: selecteds,
        cancelText: '取消',
        confirmText: '确定',
        selectedTextStyle: TextStyle(color: Colors.blue),
        textAlign: TextAlign.right,
        itemExtent: 50,
        height: 200,
        onConfirm: (Picker picker, List<int> selecteds) {
          var time = (picker.adapter as DateTimePickerAdapter).value;
          clickCallBack(time!.millisecondsSinceEpoch);
        }).showModal(context);
  }

  //日期时间选择器
  static void showDateTimePicker(
    BuildContext context,
    TimeClickCallback clickCallBack, {
    DateType? dateType,
    DateTime? maxValue,
    DateTime? minValue,
    DateTime? value,
  }) {
    int timeType;
    timeType = PickerDateTimeType.kYMDHM;

    new Picker(
        adapter: DateTimePickerAdapter(
          type: timeType,
          isNumberMonth: true,
          yearSuffix: "年",
          monthSuffix: "月",
          daySuffix: "日",
          strAMPM: const ["上午", "下午"],
          maxValue: maxValue,
          minValue: minValue,
          value: value ?? DateTime.now(),
        ),
        title: new Text("请选择"),
//          selecteds: selecteds,
        cancelText: '取消',
        confirmText: '确定',
        selectedTextStyle: TextStyle(color: Colors.blue),
        textAlign: TextAlign.right,
        itemExtent: 50,
        height: 200,
        onConfirm: (Picker picker, List<int> selecteds) {
          var time = (picker.adapter as DateTimePickerAdapter).value;
          clickCallBack(time!.millisecondsSinceEpoch);
        }).showModal(context);
  }

  //手机号正则
  static bool isPhone(String str) {
    if (str == null || str.isEmpty) return false;
    return new RegExp(
            '^((13[0-9])|(15[^4])|(166)|(17[0-8])|(18[0-9])|(19[8-9])|(147,145))\\d{8}\$')
        .hasMatch(str);
  }

  //身份证正则
  static bool isIdentityCard(String id) {
    // 校验基本格式
    final RegExp regex = RegExp(r'^\d{15}$|^\d{17}[\dXx]$');
    if (!regex.hasMatch(id)) {
      return false; // 格式错误
    }

    // 如果是 15 位身份证号，直接通过（老格式不需要校验码）
    if (id.length == 15) {
      return true;
    }

    // 对于 18 位身份证号，校验最后一位校验码
    final List<int> weightFactors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    final List<String> checksumTable = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

    int sum = 0;
    for (int i = 0; i < 17; i++) {
      sum += int.parse(id[i]) * weightFactors[i];
    }

    int mod = sum % 11;
    String expectedChecksum = checksumTable[mod];

    return id[17].toUpperCase() == expectedChecksum; // 验证最后一位校验码
  }

  //车牌号校验
  static bool isPlateNumber(String plateNumber) {
    //普通车牌号
    final regularRegex = RegExp(r'^[\u4e00-\u9fa5][A-Z][A-Z0-9]{5}$');
    //新能源车牌号
    final newEnergyRegex = RegExp(r'^[\u4e00-\u9fa5][A-Z][A-Z0-9]{6}$');
    return regularRegex.hasMatch(plateNumber) || newEnergyRegex.hasMatch(plateNumber);
  }

  //邮箱正则
  static bool isEmail(String input) {
    if (input == null || input.isEmpty) return false;
    return new RegExp("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*\$")
        .hasMatch(input);
  }

  // static String getDate(String dateStr, String fromFormat, String toFormat) {}

  //替换换行符号
  static String replaceStr(String str) {
    String b = str.replaceAll("\n", "\\n");
    String c = b.replaceAll("\t", "\\t");
    String d = c.replaceAll("\r", "\\r");
    String e = d.replaceAll("\f", "\\f");
    String f = e.replaceAll("\b", "\\b");
    return f;
  }

  //替换换行符号
  static String replaceStr2(String str) {
    String b = str.replaceAll("\n", "");
    String c = b.replaceAll("\t", "");
    String d = c.replaceAll("\r", "");
    String e = d.replaceAll("\f", "");
    String f = e.replaceAll("\b", "");
    return f;
  }

  //暂无数据
  static emptyComponet() {
    return Center(
      child: GestureDetector(
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.fromLTRB(0, 80, 0, 0),
                child: Column(
                  children: [
                    Image(
                      image: AssetImage('lib/assets/images/no_data.png'),
                      fit: BoxFit.contain,
                      width: 200,
                      height: 200,
                    ),
                    Text(
                      "暂无数据",
                      style: TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
          onTap: () {
//            Utils.toastCenter("请尝试退出页面重新进入");
          }),
    );
  }

  static bool isStringEmpty(String s) {
    return s == null || s.trim() == "";
  }
}
