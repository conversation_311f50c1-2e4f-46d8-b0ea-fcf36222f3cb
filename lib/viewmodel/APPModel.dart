import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

import '../dnc/widget/components/Pop.dart';

typedef SubSuccess = void Function(Map<String, dynamic> json);

class APPModel extends BaseViewModel {
  @override
  getAppVersion({SubSuccess? subSuccess}) async {
    Http().get(Urls.appUpdate, {
      "id": "1",
    }, success: (json) {
      if (subSuccess != null) {
        subSuccess(json);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }
}
