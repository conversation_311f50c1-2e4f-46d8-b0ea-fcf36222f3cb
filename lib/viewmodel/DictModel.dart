import 'dart:async';

import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

import '../dnc/widget/components/Pop.dart';

typedef SubSuccess = void Function(List<dynamic> list);

class DictModel extends BaseViewModel {
  @override
  getDict(String code, {SubSuccess? subSuccess}) async {
    Completer<List<dynamic>> completer = Completer();
    Http().dict(Urls.dict + code, {

    }, success: (list) {
      completer.complete(list);
      if (subSuccess != null) {
        subSuccess(list);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      completer.complete([]);
      setStateFail();
    }, after: () {});
    return completer.future;
  }

  @override
  getDict2(String code, {SubSuccess? subSuccess, url}) async {
    return Http().dictGet(url, {"code": code}, success: (list) {
      if (subSuccess != null) {
        subSuccess(list);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }
}
