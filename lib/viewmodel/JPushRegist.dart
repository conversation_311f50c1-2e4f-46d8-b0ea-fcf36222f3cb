import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

import '../common/global.dart';

/// 极光推送相关

class JPushModel extends BaseViewModel {
  var url;

  void submitRegistrationId(var registrationId) {
    url = Urls.jPushSubmitRegistrationId;
    Http().post(url, {
      "address": registrationId,
      "remarks": Global.username,
    });
  }
}
