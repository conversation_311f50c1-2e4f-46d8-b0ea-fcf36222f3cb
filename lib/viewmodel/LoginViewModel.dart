import 'dart:convert';
import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';
import 'package:flutter/services.dart';
import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../dnc/plugs/Encryptor.dart';
import '../dnc/widget/components/Pop.dart';
import 'JPushRegist.dart';

typedef LoginSuccess = void Function();

class LoginViewModel extends BaseViewModel {
  late SharedPreferences prefs;

  login(String username, String password, {LoginSuccess? loginSuccess}) async {
    print('[Login Encrypt is ${Global.loginIsEncrypt},$username,$password]');
    setStateLoding();
    final String userKey =
    Global.loginIsEncrypt ? base64Encode(utf8.encode(username)) : username;
    final String passwordKey =
    Global.loginIsEncrypt ? await Encryptor().encrypt(password) : password;
    Http().post(Urls.login, {"username": userKey, "password": passwordKey},
        // , "grant_type": 'password'
        success: (json) async {
          setStateContent();
          //保存到本地数据
          // prefs = await SharedPreferences.getInstance();
          // prefs.setString("username", username);
          // prefs.setString("password", password);
          // Global.authorization = '${json['tokenType']} ${json['accessToken']}';
          //新
          Global.username = userKey;
          Global.authorization = '${json['accessToken']['token']}';
          print('Global.authorization:');
          print(Global.authorization);
          // prefs.setString(
          //     "Authorization", json['tokenType'] + " " + json['accessToken']);
        }, fail: (reason, code) {
          Toast.show(reason);
          setStateFail();
        }, after: () {
          //请求用户角色
          Http().get(Urls.userInfo, {}, success: (json) async {
            setStateContent();
            Global.userInfo = json;
            //获取用户身份-通过用户组名称     权限：系统管理员>部门负责人>部门普通员工
            // for (var i = 0; i < json['userGroup'].length; i++) {
            //   Global.role = json['userGroup'][i]["name"];
            //   if (json['userGroup'][i]["name"] == '系统管理员') {
            //     break;
            //   } else if (json['userGroup'][i]["name"] == '部门负责人') {
            //     break;
            //   }
            // }
            if (json['employee'] != null) {
              Global.name = json['employee']["name"];
              Global.userName = json['employee']["user"];
            }
            print("用户身份:${Global.name}");
            print("用户身份:${Global.userName}");
            // Global.menus = json['menus'].where((e) => e['type'] == 5).toList();

            // L.d("menus: ${Global.menus}");

            //根据用户身份--分配数据权限标识
            // if (Global.role != '系统管理员') {
            //   if (Global.role != '部门负责人') {
            //     Global.userFlag = "1"; //flag:1  表明接口需要查询用户的id作为筛选
            //   } else {
            //     Global.departmentFlag = "1"; //flag:1  表明接口需要查询用户的部门作为筛选
            //   }
            // }
            // Global.userId = json['id'];
            //存储用户名
            // Global.name = json['name'];
            // Global.userName = json['username'];
            // Global.entId = json['entId'] ?? '';
            // Global.entName = DU.safeString(json, ['entInfo', 'name']);
            //存储部门名称
            // Global.departmentName = json['department'] != null ? json['department']["name"] : "";
            //存储用户所在部门
            // Global.departmentId = json['department'] != null ? json['department']["id"] : "";
            // Global.systemRole =
            //     (json['role'] != null && json['role'].length > 0) ? json['role'][0]["name"] : "";
            // Global.YJMaster = json['stopYjFlag'] ?? false;
            if (loginSuccess != null) {
              loginSuccess();
            }
          }, fail: (reason, statuscode) {
            Toast.show(reason);
            setStateFail();
          }, after: () {
            print('@@@@@@@@@@@@@@@@@@@@@@@@@1');
            JPush jPush = JPush();
            try {
              jPush.addEventHandler(
                  onConnected: (Map<String, dynamic> message) async {
                print("flutter onConnected: $message");
              });
            } catch(e) {
              print(" //catch 提示" + "e = $e");
              print(e);
            }
            // on PlatformException {
            //   platformVersion = 'Failed to get platform version.';
            // }
            print('@@@@@@@@@@@@@@@@@@@@@@@@@2');
            jPush.setAuth(enable: true);
            print('@@@@@@@@@@@@@@@@@@@@@@@@@3');
            jPush.setup(
              appKey: "dff4337d07f5c22d79b6c7af",
              channel: "theChannel",
              production: false,
              debug: true, // 设置是否打印 debug 日志
            );
            print('@@@@@@@@@@@@@@@@@@@@@@@@@4');
            jPush.getRegistrationID().then((registrationId) {
              print("=========================RegistrationID=======================");
              print(registrationId);
              JPushModel().submitRegistrationId(registrationId);
              // SDialog.showAlert(title: "registrationId", content: "$registrationId");
              // print("============================GET END===========================");
            });
            //获取用户权限
            Http().get(Urls.userRole, {}, success: (json) async {
              setStateContent();
              Global.role = json;
              //"administrator" 该角色什么都可以操作
              //"quyangyuan" 只能质检模块
              //"kuguanyuan" 只能库管模块
              //"menwei"  只能门卫模块
              print("用户权限--123:${json}");
            }, fail: (reason, statuscode) {
              Toast.show(reason);
              setStateFail();
            }, after: () {});
          });
        });
  }
}
