import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

class BpmListViewModel extends BaseRefreshListViewModel {
//  var status;
//  var departmentFlag;
//  var userFlag;

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
//    if (status == null) {
//      status = "";
//    }
//    if (departmentFlag == null) {
//      departmentFlag = "";
//    }
//    if (userFlag == null) {
//      userFlag = "";
//    }

    var responseData = await Http().get(Urls.bpmTODOList, {
      "page": pageNum,
      "limit": pageSize,
    });

    List list = responseData['list'];
    return list;
  }
}
