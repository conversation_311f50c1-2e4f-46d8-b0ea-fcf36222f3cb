import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/base/base_state.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

import '../../dnc/widget/components/Pop.dart';

typedef SubSuccess = void Function();
typedef SubFail = void Function();
typedef SubSuccess1 = void Function(Map<String, dynamic> json);

class BpmModel extends BaseViewModel {
  var bpmTaskId;
  var startData;

  bpmStart(Map<String, dynamic> param,
      {SubSuccess? subSuccess, SubFail? subFail}) {
    setStateLoding();
    Http().post(Urls.bpmStart, param, success: (json) {
      setStateContent();
      if (json != null) {
        startData = json;
      }
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
      if (subFail != null) {
        subFail();
      }
    }, after: () {});
  }

  bpmSubmit2(String taskID, Map formData,
      {Map? reform, Function? subSuccess, Function? subFail}) async {
    Map fromData2 = formData;
    if (reform != null) {
      fromData2['reform'] = reform;
    }
    var databpmSubmit = {
      "taskId": taskID,
      "complete": true,
      "formData": fromData2
    };
    await Http().post(Urls.bpmClaim, {"taskId": taskID});
    Http().post(Urls.bpmSubmit, databpmSubmit, success: (json) {
      if (json == null) {
        json = Map<String, dynamic>();
      }
      if (subSuccess != null) {
        subSuccess(json);
      }
    }, fail: (reason, code) {
      Toast.show('请通知部门负责人进行处理');
      if (subFail != null) {
        subFail();
      }
    });
  }

  bpmSubmit3(String taskID, Map formData,
      {Map? reform, Function? subSuccess, Function? subFail}) async {
    Map fromData2 = formData;
    if (reform != null) {
      fromData2['reform'] = reform;
    }
    var databpmSubmit = {
      "taskId": taskID,
      "complete": true,
      "formData": fromData2
    };
    await Http().post(Urls.bpmClaim, {"taskId": taskID}, loading: false);
    Http().post(Urls.bpmSubmit, databpmSubmit, loading: false, success: (json) {
      if (json == null) {
        json = Map<String, dynamic>();
      }
      if (subSuccess != null) {
        subSuccess(json);
      }
    }, fail: (reason, code) {
      Toast.show('请通知部门负责人进行处理');
      if (subFail != null) {
        subFail();
      }
    });
  }

  bpmSubmit(Map<String, dynamic> param,
      {SubSuccess? subSuccess, SubFail? subFail}) {
    setStateLoding();
    Http().post(Urls.bpmSubmit, param, success: (json) {
//          print("---sss-----");
//          print(json);
      setStateContent();
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
      if (subFail != null) {
        subFail();
      }
    }, after: () {});
  }

//  bpmInfo(Map param, {SubSuccess subSuccess}) {
//    setStateLoding();
//    Http().post(Urls.bpmInfo, param,
//        success: (json) {
//          setStateContent();
//          subSuccess();
//        }, fail: (reason, statuscode) {
//          showMessage(reason);
//          setStateFail();
//        }, after: () {});
//  }
  bpmClaim(String taskId, {SubSuccess? subSuccess}) {
    setStateLoding();
    Http().post(Urls.bpmClaim, {"taskId": taskId}, success: (json) {
      setStateContent();
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  bpmUnClaim(String taskId, {SubSuccess? subSuccess}) {
    setStateLoding();
    Http().post(Urls.bpmUnClaim, {"taskId": taskId}, success: (json) {
      setStateContent();
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  @override
  todoByProcessInstanceId(String processInstanceId,
      {SubSuccess1? subSuccess, SubFail? subFail,bool showLoading=true}) async {
    state = BaseState.LOADING;
    notifyListeners();
    setStateLoding();
    final json = await Http().get(
        Urls.todoByProcessInstanceId, {"processInstanceId": processInstanceId},
        loading: showLoading, success: (json) {
//      print("-------------------bpmTask:");
//      print(json);
      if (json != null) {
        bpmTaskId = json["id"];
        if (subSuccess != null) {
          subSuccess(json);
        }
      } else {
        // Toast.show('未查询到相关任务');
        if (subFail != null) {
          subFail();
        }
      }
      state = BaseState.CONTENT;
      setStateContent();
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
      if (subFail != null) {
        subFail();
      }
    }, after: () {});
    if (json != null) {
      return json["id"];
    } else {
      return null;
    }
  }

  todoByProcessInstanceId_reformtask(String processInstanceId,
      {SubSuccess? subSuccess}) async {
    state = BaseState.LOADING;
    notifyListeners();
    setStateLoding();
    Http().get(Urls.todoByProcessInstanceId, {
      "processInstanceId": processInstanceId,
    }, success: (json) {
//      print("-------------------bpmTask:");
//      print(json);
      if (json != null) {
        bpmTaskId = json["id"];
      }
      state = BaseState.CONTENT;
      setStateContent();
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }
}
