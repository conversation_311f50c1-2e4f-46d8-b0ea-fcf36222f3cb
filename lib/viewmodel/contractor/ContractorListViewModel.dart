import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

class ContractorListViewModel extends BaseRefreshListViewModel {
  var status;
  var name;
  var contacts;

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    if (name == null) {
      name = "";
    }
    if (contacts == null) {
      contacts = "";
    }

    var responseData =
        await Http().get(Urls.contractorList, {"page": pageNum, "limit": pageSize, "name": name, "contacts": contacts});
    List list = responseData['list'];
    return list;
  }
}
