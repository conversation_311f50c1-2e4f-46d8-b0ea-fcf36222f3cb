import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

import '../../dnc/widget/components/Pop.dart';

typedef SubSuccess = void Function();
typedef SubSuccessJson = void Function(List<dynamic> json);
typedef SubSuccessObj = void Function(dynamic json);

class ContractorViewModel extends BaseViewModel {
  var returnData;
  var reformTaskDetailData;

  //违章记录
  getViolationList(Map<String, dynamic> param, {SubSuccessObj? subSuccess}) {
    setStateLoding();
    Http().get(Urls.violationList, param, success: (json) {
      setStateContent();
      subSuccess!(json);
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  //添加违章
  addViolation(Map<String, dynamic> param, {SubSuccessObj? subSuccess}) async {
    setStateLoding();
    Http().post(Urls.violationAdd, param, success: (json) {
      setStateContent();
      subSuccess!(json);
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  //人员列表
  getPeopleList(Map<String, dynamic> param, {SubSuccessObj? subSuccess}) {
    setStateLoding();
    Http().get(Urls.peopleList, param, success: (json) {
      setStateContent();
      subSuccess!(json);
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  //所有承包商
  getAll(Map<String, dynamic>? param, {SubSuccessJson? subSuccess}) {
    setStateLoding();
    Http().get(Urls.contractorAll, param, success: (json) {
      setStateContent();
      subSuccess!(json);
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }
}
