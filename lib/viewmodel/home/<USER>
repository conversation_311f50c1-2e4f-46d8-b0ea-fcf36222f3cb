import '../../base/BaseRefreshListViewModel.dart';
import '../../common/http.dart';
import '../../dnc/widget/components/Pop.dart';

class TodoTasksListViewModel extends BaseRefreshListViewModel {
  final _type = 2;
  final _state = 0;
  final _receiver = "APP";
  String? businessType;

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    var result = await baseData(pageNum: pageNum, pageSize: pageSize);
    if (result != null) {
      List list = result['list'] ?? [];
      return list;
    } else {
      return [];
    }
  }

  Future<dynamic> baseData({int? pageNum = 1, int? pageSize = 10}) async {
    // final url = "/qyaq/app/upcomingTaskList";
    final url = "/notifications";
    return await Http().get(url, {
      'type': _type,
      'state': _state,
      "receiver": _receiver,
      'channelName': 'internal-message',
      'businessType': businessType,
      "page": pageNum,
      "limit": pageSize,
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  Future<dynamic> todoTasksTypeInfo() async {
    final url = "/notifications/quantity";
    final result = await Http().get(url, {
      'type': 2,
      'state': 0,
      'channelName': 'internal-message',
      'receiver': 'APP',
    }, fail: (reason, code) {
      Toast.show(reason);
    });
    return result;
  }
}
