import '../../base/BaseViewModel.dart';
import '../../common/http.dart';
import '../../dnc/widget/components/Pop.dart';

class HomeViewModel extends BaseViewModel {
  Future<dynamic> getHomeData() {
    final url = "/qyaq/app/indexInterface";
    return Http().get(url, {}, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  Future<dynamic> updateMineMenus(List menus) async {
    final url = "/qyaq/app/userResource/update";
    final result = await Http().post(url, {'resourceData': menus}, fail: (reason, code) {
      Toast.show(reason);
    });
    return result['resourceData'];
  }

  Future<dynamic> homeSearch(String info) async {
    final url = "/qyaq/app/appSearch";
    final result = await Http().get(url, {'searchName': info}, fail: (reason, code) {
      Toast.show(reason);
    });
    return result;
  }
//消息通知已读设置
  Future<dynamic> messageRead(String id) async {
    final url = '/notification/${id}';
    await Http().patch(url, fail: (reason, code) {
      Toast.show(reason);
    });
    // return result;
  }
}
