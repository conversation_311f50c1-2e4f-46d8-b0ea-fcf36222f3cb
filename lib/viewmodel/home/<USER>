import '../../base/BaseRefreshListViewModel.dart';
import '../../common/http.dart';
import '../../dnc/widget/components/Pop.dart';

class MessageListViewModel extends BaseRefreshListViewModel {
  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    checkParentNum = false;
    final url = "/notification";
    final result = await Http().get(url, {
      // 'channelName':'internal-message',
      "page": pageNum,
      "perPage": pageSize,
      "platform":'app',
    }, fail: (reason, code) {
      Toast.show(reason);
    });
    // 处理返回结果
    if (result.isNotEmpty) {
      return result['items'];
    } else {
      // 如果返回的是其他类型，返回空列表
      return [];
    }
  }
}
