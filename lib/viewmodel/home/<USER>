import 'package:common_utils/common_utils.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/dnc/utils/DataUtils.dart';

import '../../base/BaseViewModel.dart';
import '../../common/http.dart';
import '../../dnc/widget/components/Pop.dart';

class StatisticsPageViewModel extends BaseViewModel {
  void getTodayData(Function(dynamic) success) {
    final url = "/qb/report/todayData";
    final timestamp = DU.getDayStart();
    Http().get(url, {
      'entId': Global.entId,
      'startTime': timestamp.toIso8601String(),
      'endTime': DateTime.now().toIso8601String(),
    }, success: (data) {
      final result = data.isEmpty ? {} : data.first;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  getWeekData(Function(dynamic) success) {
    final url = "/qb/report/weekYH";
    final timestamp = DU.getWeekStart();
    Http().get(url, {
      'entId': Global.entId,
      'startTime': timestamp.toIso8601String(),
      'endTime': DateTime.now().toIso8601String(),
    }, success: (data) {
      final result = data.isEmpty ? {} : data.first;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  getMonthData(Function(dynamic) success) {
    final url = "/qb/report/monthData";
    final timestamp = DU.getMonthStart();
    Http().get(url, {
      'entId': Global.entId,
      'startTime': timestamp.toIso8601String(),
      'endTime': DateTime.now().toIso8601String(),
    }, success: (data) {
      final result = data.isEmpty ? {} : data.first;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  getEducationData1(Function(dynamic) success) {
    final url = "/qb/report/ksCount";
    final start = DU.getMonthStart();
    final end = DU.getMonthEnd();
    Http().get(url, {
      'entId': Global.entId,
      'startTime': start.toIso8601String(),
      'endTime': end.toIso8601String(),
    }, success: (data) {
      final result = data.isEmpty ? {} : data.first;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  getEducationData2(Function(dynamic) success) {
    final url = "/qb/report/answerPeopleCount";
    final start = DU.getMonthStart();
    final end = DU.getMonthEnd();
    Http().get(url, {
      'entId': Global.entId,
      'startTime': start.toIso8601String(),
      'endTime': end.toIso8601String(),
    }, success: (data) {
      final result = data.isEmpty ? {} : data.first;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  getEducationData3(Function(dynamic) success) {
    final url = "/qb/report/aqTrainPolyline";
    final timestamp = DU.getYearStart();
    Http().get(url, {
      'entId': Global.entId,
      'startTime': timestamp.toIso8601String(),
      'endTime': DateTime.now().toIso8601String(),
    }, success: (data) {
      final result = data;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  getDualSystemData(Function(dynamic) success) {
    final url = "/qb/report/group_single";
    final timestamp = DU.getYearStart();
    Http().get(url, {
      'entId': Global.entId,
      'beginDate': timestamp.toIso8601String(),
      'endDate': DateTime.now().toIso8601String(),
    }, success: (data) {
      final result = data;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }

  getDualSystemRank(Function(dynamic) success) {
    final url = "/qb/report/conpanyScoreTable";
    final now = DateTime.now();
    final timestamp = DateTime(now.year, now.month - 1);
    Http().get(url, {
      'entId': Global.entId,
      'month': DateUtil.formatDate(timestamp, format: DateFormats.y_mo),
    }, success: (data) {
      final result = data;
      success(result);
    }, fail: (reason, code) {
      Toast.show(reason);
    });
  }
}
