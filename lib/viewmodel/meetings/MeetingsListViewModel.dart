import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';

class MeetingsListViewModel extends BaseRefreshListViewModel {
  final url = '/qyaq/grda/meeting/search';

  String? name;

  String? mettingPeriod;

  String? mettingType;

  String? startTime;

  String? endTime;

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    var responseData = await Http().get(url, {
      "entId": Global.entId,
      'name': name,
      'mettingPeriod': mettingPeriod,
      'mettingType': mettingType,
      'startTime': startTime,
      'endTime': endTime,
      "page": pageNum,
      "limit": pageSize
    });
    List list = responseData['list'];
    // print('list: $list');
    return list;
  }
}
