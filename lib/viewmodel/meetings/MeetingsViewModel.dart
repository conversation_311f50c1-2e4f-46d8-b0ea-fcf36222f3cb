import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';
import 'package:flutter/cupertino.dart';

import '../../dnc/widget/components/Pop.dart';

typedef Success = void Function();

class MeetingsViewModel extends BaseViewModel {
  //发起维修
  add(Map<String, dynamic> param, {Success? success}) {
    final url = "/qyaq/grda/meeting/add";
    if (param == null) return;
    param["entId"] = Global.entId;
    Http().post(url, param, success: (json) {
      Toast.show('提交成功');
      if (success != null) {
        success();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }

  get(String meetingId, {ValueChanged? success}) {
    final url = "/qyaq/grda/meeting/get";
    final param = {"entId": Global.entId, "id": meetingId};
    Http().get(url, param, success: (json) {
      if (success != null) {
        success(json);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }

  userList(String meetingId, {ValueChanged? success}) {
    final url = "/qyaq/grda/meetingUser/listByMeetingUser";
    final param = {"entId": Global.entId, "meetingId": meetingId};
    Http().get(url, param, success: (json) {
      if (success != null) {
        success(json);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }
}
