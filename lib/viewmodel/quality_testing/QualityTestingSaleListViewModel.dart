import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';

class MeetingsListViewModel extends BaseRefreshListViewModel {
  final url = '/appBussiness/zhijian/stockUp';

  String? billNo = "";
  String? billDate = "";
  String? carNo = "";
  String? driverPhone = "";
  String? driverIdCard = "";
  String? driverNm = "";
  String? materielNm = "";
  String? checkResult = "";

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    var responseData = await Http().get(url, {
      "page": pageNum,
      "perPage": pageSize,
      'carNo': carNo,
      'driverPhone': driverPhone,
      'driverIdCard': driverIdCard,
    });
    List list = responseData['items'];
    // print('list: $list');
    return list;
  }
}
