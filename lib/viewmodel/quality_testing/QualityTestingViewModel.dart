import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';
import 'package:flutter/cupertino.dart';

import '../../dnc/widget/components/Pop.dart';

typedef Success = void Function(dynamic json);

class MeetingsViewModel extends BaseViewModel {
  //销售质检员提交质检
  saleAdd(Map<String, dynamic> param, {Success? success}) {
    final url = "/appBussiness/zhijian/checkStockUp";
    if (param == null) return;
    Http().post(url, param, success: (json) {
      Toast.show('提交成功');
      if (success != null) {
        success(json);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }
  //采购质检员提交质检
  purchaseAdd(Map<String, dynamic> param, {Success? success}) {
    final url = "/appBussiness/zhijian/checkRawMaterial";
    if (param == null) return;
    Http().post(url, param, success: (json) {
      Toast.show('提交成功');
      if (success != null) {
        success(json);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }
}
