import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';
import 'package:ent_secutity_app/model/question_info.dart';
import 'package:dio/dio.dart';
import 'package:ent_secutity_app/common/ServiceManager.dart';

class QuestionListViewModel extends BaseRefreshListViewModel {
  String? questionName;

  // 模拟数据 - 仅在API调用失败时使用
  final List<Map<String, dynamic>> _mockData = [
    {
      'id': '1',
      'questionName': '以下哪种行为是安全生产的正确做法？',
      'questionAnswer': 'A',
      'questionTip': '请选择正确的安全生产做法',
      'analysis': '佩戴安全帽是进入施工现场的基本安全要求，可以有效防止头部受到伤害。',
      'questionA': '进入施工现场佩戴安全帽',
      'questionB': '在禁烟区吸烟',
      'questionC': '疲劳驾驶',
      'questionD': '超负荷运行设备',
      'createTime': '2023-01-01 08:00:00'
    },
    {
      'id': '2',
      'questionName': '发生火灾时，下列哪种做法是错误的？',
      'questionAnswer': 'C',
      'questionTip': '请选择火灾时的错误做法',
      'analysis': '火灾发生时，乘坐电梯可能导致被困或因电梯故障而无法逃生，应使用安全通道或楼梯逃生。',
      'questionA': '保持镇定',
      'questionB': '用湿毛巾捂住口鼻',
      'questionC': '乘坐电梯逃生',
      'questionD': '沿着安全通道撤离',
      'createTime': '2023-01-02 09:15:00'
    },
    {
      'id': '3',
      'questionName': '下列关于个人防护装备(PPE)的说法，正确的是？',
      'questionAnswer': 'D',
      'questionTip': '请选择关于PPE的正确说法',
      'analysis': '个人防护装备是保护工作人员安全的最后一道防线，应当按照规定正确佩戴和使用。',
      'questionA': '只有新员工需要使用个人防护装备',
      'questionB': '个人防护装备可以随意更换或修改',
      'questionC': '感觉不舒适时可以不使用个人防护装备',
      'questionD': '应当按照规定正确佩戴和使用个人防护装备',
      'createTime': '2023-01-03 10:30:00'
    },
    {
      'id': '4',
      'questionName': '化学品泄漏时，首先应该采取的措施是什么？',
      'questionAnswer': 'B',
      'questionTip': '请选择化学品泄漏时的首要措施',
      'analysis': '化学品泄漏时，应立即疏散人员，避免人员接触泄漏物质而造成伤害。',
      'questionA': '用水冲洗泄漏区域',
      'questionB': '疏散人员到安全区域',
      'questionC': '尝试用手收集泄漏物',
      'questionD': '立即关闭所有电源',
      'createTime': '2023-01-04 11:45:00'
    },
    {
      'id': '5',
      'questionName': '在进行高空作业时，以下哪项是必须的安全措施？',
      'questionAnswer': 'A',
      'questionTip': '请选择高空作业必须的安全措施',
      'analysis': '高空作业必须使用安全带或安全绳，以防止坠落事故发生。',
      'questionA': '使用安全带或安全绳',
      'questionB': '穿着轻便的运动鞋',
      'questionC': '单人独自作业',
      'questionD': '在未经检查的脚手架上工作',
      'createTime': '2023-01-05 14:20:00'
    }
  ];

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    try {
      // 构建API请求参数
      var params = {
        "page": (pageNum ?? 1).toString(),
        "perPage": (pageSize ?? 20).toString(),
      };

      // 如果有搜索条件，添加到请求参数中
      if (questionName != null && questionName!.isNotEmpty) {
        params["questionName[CONTAINS]"] = questionName!;
      }

      // 调用API接口
      var responseData = await Http().get("/exam/question-info", params);

      // 处理返回数据
      if (responseData == null) {
        return _mockData;
      }

      if (responseData is List) {
        return responseData;
      }

      if (responseData is Map) {
        List list = [];
        if (responseData.containsKey('list')) {
          list = responseData['list'];
        } else if (responseData.containsKey('items')) {
          list = responseData['items'];
        } else if (responseData.containsKey('records')) {
          list = responseData['records'];
        } else if (responseData['data'] is List) {
          list = responseData['data'];
        }
        if (list.isNotEmpty) {
          return list;
        }
      }

      // 如果解析不到有效数据，使用模拟数据
      return _mockData;
    } catch (e) {
      print("加载题目数据失败: $e");
      // 发生异常时使用模拟数据
      return _mockData;
    }
  }

  // 删除题目
  Future<bool> deleteQuestion(String id) async {
    try {
      if (id.isEmpty) return false;

      // 创建Dio实例
      var dio = Dio();

      // 构建完整的URL
      final url = '${ServiceManager.currentHost}/api/exam/question-info/${id}';

      // 设置请求头
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': Global.authorization,
        'QbApplicationId': Global.qbApplicationId,
      };

      // 发送DELETE请求
      await dio.delete(
        url,
        options: Options(headers: headers),
      );

      return true;
    } catch (e) {
      print("删除题目失败: $e");
      return false;
    }
  }

  // 保存题目（添加或更新）
  Future<bool> saveQuestion(QuestionInfo question) async {
    try {
      // 准备请求数据
      Map<String, dynamic> data = question.toMap();

      if (question.id.isEmpty) {
        // 添加新题目 - 使用POST请求，application/json格式
        await Http().post("/exam/question-info", data);
      } else {
        // 更新现有题目 - 使用POST请求，application/json格式
        await Http().post("/exam/question-info/update/${question.id}", data);
      }
      return true;
    } catch (e) {
      print("保存题目失败: $e");
      return false;
    }
  }
}
