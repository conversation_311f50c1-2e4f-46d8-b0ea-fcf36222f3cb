import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/base/base_state.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';
import 'package:ent_secutity_app/common/global.dart';
import '../../dnc/widget/components/Pop.dart';

typedef Success = void Function();
typedef SubSuccessObj = void Function(dynamic json);
typedef SubFail = void Function();

class SystemViewModel extends BaseViewModel {
  var entId = Global.entId;

  getSystemSettingData(String settingCode,
      {SubSuccessObj? subSuccess, SubFail? subFail}) async {
    Http().get(Urls.systemSetting, {
      "entId": Global.entId,
      "isSystem": 2,
      "settingCode": settingCode
    }, success: (json) {
      setStateContent();
      if (subSuccess != null) {
        subSuccess(json);
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
      if (subFail != null) {
        subFail();
      }
    }, after: () {});
  }
}
