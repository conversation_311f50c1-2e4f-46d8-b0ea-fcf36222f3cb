import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';

//class UserInfo {
//  String name;
//  String id;
//  bool isSelected;
//
//  UserInfo({this.name, this.id, this.isSelected = false});
//}

class UserListViewModel extends BaseRefreshListViewModel {
  var departmentId = "";

  @override
  Future<List> loadData({int? pageNum, int? pageSize, String? dpId}) async {
    var id = dpId == null ? departmentId : dpId;
    if (id == null || id == "") {
      id = Global.entId;
    }
    var url = Urls.userList;
    var responseData = await Http().get(url, {"departmentId": id, "page": pageNum ?? 1, "limit": 10000});

//
//    for (int i = 0; i <= responseData['list'].length; i++) {
//      responseData['list'][i]["avatar"] = false;
//    }

    List list = responseData['list'] ?? [];

    return list;
  }

  Future<List> loadDepartment() async {
    var responseData = await Http().get(Urls.limitedDepartmentTree, {
      "entId": Global.entId,
    });
    return responseData ?? [];
  }
}
