import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';
import 'package:ent_secutity_app/model/user_info.dart';

import '../../common/global.dart';
import '../../dnc/plugs/Encryptor.dart';
import '../../dnc/widget/components/Pop.dart';

typedef Success = void Function();
typedef SubFail = void Function();

class UserViewModel extends BaseViewModel {
  UserInfo? userInfo;
  dynamic userInfo1;

  getUserInfo(Success success) {
    setStateLoding();
    Http().get(Urls.userInfo, {}, success: (json) async {
      userInfo1 = json;
      userInfo = UserInfo.fromJson(json);
      // success();
      setStateContent();
      success();
      //获取用户身份-通过用户组名称     权限：系统管理员>部门负责人>部门普通员工
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      // setStateFail();
    }, after: () {});
  }

  getUserInfo1(Success success) {
    setStateLoding();
    Http().get(Urls.userInfo, {}, success: (json) async {
      userInfo1 = json;
      // success();
      setStateContent();
      success();
      //获取用户身份-通过用户组名称     权限：系统管理员>部门负责人>部门普通员工
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      // setStateFail();
    }, after: () {});
  }

  editUserInfo(dynamic info, {Success? subSuccess}) {
    setStateLoding();
    Http().post(Urls.userinoEdit, info, success: (json) {
      Toast.show("修改成功");
      setStateContent();
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }

  editUserInfo1(dynamic userInfo, {Success? subSuccess}) {
    setStateLoding();
    Http().post(Urls.userinoEdit, userInfo, success: (json) {
      Toast.show("修改成功");
      setStateContent();
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }

  editPsd(String oldP, String newP,
      {Success? subSuccess, SubFail, subfail}) async {
    setStateLoding();
    String oldKey =
        Global.loginIsEncrypt ? await Encryptor().encrypt(oldP) : oldP;
    String newKey =
        Global.loginIsEncrypt ? await Encryptor().encrypt(newP) : newP;

    Http().post(Urls.userpsdEdit, {"credentials": oldKey, "newPassword": newKey, "verificationMode":"password"},
        success: (json) {
      setStateContent();
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateContent();
    }, after: () {});
  }
// changeStatus(String id,String status, {SubSuccess subSuccess}) {
//   setStateLoding();
//   Http().post(Urls.reportEdit, {"id":id,"status":status},
//       success: (json) {
//         setStateContent();
//         subSuccess();
//       }, fail: (reason, statuscode) {
//         Toast.show(reason);
//       }, after: () {});
// }
}
