import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';

class MeetingsListViewModel extends BaseRefreshListViewModel {
  final url = '/appBussiness/kuguan/saleWayBillList';

  String? endTime;
  String billNo = "";
  String customerNm = "";
  String carNo = "";
  String driverPhone = "";
  String driverIdCard = "";
  String materielNm = "";
  String billDate = "";
  String packagNm = "";
  double planAmount = 0.0;
  double realityAmount = 0.0;

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    var responseData = await Http().get(url, {
      "page": pageNum,
      "perPage": pageSize,
      'carNo': carNo,
      'driverPhone': driverPhone,
      'driverIdCard': driverIdCard
    });
    List list = responseData['items'];
    // print('list: $list');
    return list;
  }
}
