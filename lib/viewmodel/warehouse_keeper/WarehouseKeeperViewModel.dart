import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';
import 'package:flutter/cupertino.dart';

import '../../dnc/widget/components/Pop.dart';

typedef Success = void Function();

class MeetingsViewModel extends BaseViewModel {
  //保存实际发货数
  add(Map<String, dynamic> param, {Success? success}) {
    final url = "/appBussiness/kuguan/saveRealityAmount";
    if (param == null) return;
    Http().post(url, param, success: (json) {
      Toast.show('提交成功');
      if (success != null) {
        success();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
    }, after: () {});
  }
}
