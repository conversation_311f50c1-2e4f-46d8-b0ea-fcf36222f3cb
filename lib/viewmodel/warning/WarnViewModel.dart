import 'package:ent_secutity_app/base/BaseViewModel.dart';
import 'package:ent_secutity_app/base/base_state.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/http.dart';

import '../../dnc/widget/components/Pop.dart';

typedef SubSuccess = void Function();

class WarnViewModel extends BaseViewModel {
  var returnData;
  var reformTaskDetailData;
  var reformtaskId;

  handleWarnXY(Map<String, dynamic> param, {SubSuccess? subSuccess}) {
    setStateLoding();
    Http().post(Urls.handleWarnXY, param, success: (json) {
      setStateContent();
      Toast.show('提交成功');
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  handleWarnJY(Map<String, dynamic> param, {SubSuccess? subSuccess}) {
    setStateLoding();
    Http().post(Urls.handleWarnJY, param, success: (json) {
      setStateContent();
      Toast.show('提交成功');
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  check(Map<String, dynamic> param, {SubSuccess? subSuccess}) {
    setStateLoding();
    Http().post(Urls.checkProblemFeedback, param, success: (json) {
      setStateContent();
      Toast.show('提交成功');
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  @override
  getData(String id) async {
    state = BaseState.LOADING;
    notifyListeners();
    setStateLoding();
    Http().get(Urls.getProblemFeedback, {
      "id": id,
    }, success: (json) {
      returnData = json;
      state = BaseState.CONTENT;
      setStateContent();
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  edit(Map<String, dynamic> param, {SubSuccess? subSuccess}) {
    setStateLoding();
    Http().post(Urls.editProblemFeedback, param, success: (json) {
      setStateContent();
      Toast.show('提交成功');
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }

  @override
  reformTaskDetail(String reformtaskId) async {
    if (null == reformtaskId || "" == reformtaskId) {
      return;
    }
    state = BaseState.LOADING;
    notifyListeners();
    setStateLoding();
    Http().get(Urls.reformTaskDetail, {
      "id": reformtaskId,
    }, success: (json) {
      reformTaskDetailData = json;
      state = BaseState.CONTENT;
      setStateContent();
    }, fail: (reason, statuscode) {
      Toast.show(reason);
//          setIdle();
      setStateFail();
    }, after: () {});
  }

  //创建整改--根据问题反馈
  createReform(Map<String, dynamic> param, {SubSuccess? subSuccess}) {
    setStateLoding();
    Http().post(Urls.createReform, param, success: (json) {
      setStateContent();
//          Toast.show('提交成功');
      reformtaskId = json != null ? json["id"] : "";
      if (subSuccess != null) {
        subSuccess();
      }
    }, fail: (reason, statuscode) {
      Toast.show(reason);
      setStateFail();
    }, after: () {});
  }
}
