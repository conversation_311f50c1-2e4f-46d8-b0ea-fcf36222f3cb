import 'package:ent_secutity_app/base/BaseRefreshListViewModel.dart';
import 'package:ent_secutity_app/common/Urls.dart';
import 'package:ent_secutity_app/common/global.dart';
import 'package:ent_secutity_app/common/http.dart';

class WarningListViewModel extends BaseRefreshListViewModel {
  var type;
  var status;

  @override
  Future<List> loadData({int? pageNum, int? pageSize}) async {
    var url = '';
    if (type != null) {
      if (type == 'xy') {
        url = Urls.xyOilWarning;
      } else if (type == 'jy') {
        url = Urls.jyOilWarning;
      } else if (type == 'ent') {
        url = Urls.EntWarning;
      }
    }

    if (status == null) {
      status = '';
    }

    var responseData = await Http().get(url, {
      'entId': Global.entId,
      "page": pageNum,
      "limit": pageSize,
      "alarmAction": status,
    });

    List list = responseData['list'];
    return list;
  }
}
