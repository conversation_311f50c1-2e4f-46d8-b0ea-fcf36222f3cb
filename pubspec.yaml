name: ent_secutity_app
description: A new Flutter application.

version: 4.7.4

environment:

  sdk: '>=3.2.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_swiper_null_safety: 1.0.2
  smooth_page_indicator: 1.1.0
  cupertino_icons: 1.0.6  #  Use with the CupertinoIcons class for iOS style icons.
  image_picker: 0.8.6 #拍照 【TUI】
  pull_to_refresh: 2.0.0  #上拉加载 下拉刷新
  shared_preferences: 2.2.2   #本地存储sharedPerferences
  flutter_image_compress: 1.1.3 #图片压缩 【TUI】
  photo_view: 0.14.0    #图片预览
  wechat_assets_picker: 8.9.0-dev.1   #相册选取多张
  wechat_camera_picker: 4.2.0-dev.3 #拍照
  video_compress: ^3.1.2
  permission_handler: 10.4.5 #权限申请 【TUI】
  url_launcher: 6.2.4 #拨打电话 访问外部URL
  flutter_picker: 2.1.0  #选择器
  date_format: 2.0.7 #时间处理
  provider: 6.1.1
  dio: 5.4.0
  flutter_nfc_kit: 3.3.1 #【TUI】
  ndef: 0.3.1 #【TUI】
  common_utils: 2.1.0
  cached_network_image: 3.3.1
  barcode_scan2: 4.3.0  #二维码扫描
  hand_signature: 3.0.2
  shimmer: 3.0.0 #微光
  wave: 0.2.2
  convex_bottom_bar: 3.2.0
  file_preview: 1.1.8
  pubspec_parse: 1.2.3
  syncfusion_flutter_charts: 19.4.56  #图表
  logger: 2.2.0
  package_info_plus: 4.2.0   #查询版本号
  r_upgrade: 0.4.2 #安装apk
  flutter_smart_dialog: 4.9.6 #dialog
  flutter_advanced_seekbar: 0.0.2 #进度条
  jpush_flutter: 2.5.0 #极光推送
  html: 0.15.4 #html解析
  properties: 2.1.0
  reorderables: 0.6.0 #拖拽
  dropdown_search: 5.0.6 #dropdown
  encrypt: 5.0.3 #加密
  path_provider: 2.1.2 #存储
  device_information: 0.0.4  #设备信息
  device_info_plus: 9.1.1
  sqflite: 2.3.1 # 数据库
  #  connectivity_plus: 4.0.2 # 网络监听
  hawk_fab_menu: 1.2.0
  geolocator: 10.1.0 #loaction
  app_settings: 5.1.1
  badges: 3.1.2
#  flutter_rating_bar: 4.0.1 #评级组件
  qr_flutter: 4.1.0 #二维码组件
#  qyplayer: 0.1.2 #直播视频组件
#  lecle_yoyo_player: 0.0.8

  build_runner: 2.2.0
  flutter_html: 3.0.0-alpha.5
#  tencent_cloud_chat_uikit: 2.4.2


  #  image_picker_android: 0.8.5+7
  #  photo_manager: 2.7.0

  #教育培训
  pdf_viewer_plugin: 2.0.1  #pdf预览
  video_player: 2.8.2 #视频播放
  chewie: 1.7.4  #视频播放

#dev_dependencies:
#  flutter_test:
#    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # 配置文件
    - assets/code/address.json
    - assets/key/rsa_public_key.pem
    # menu 仅作菜单管理用，请勿将模块资源放在此路径！
    - assets/images/module/check_task/
    - assets/images/module/chemicals/
    - assets/images/module/contractor/
    - assets/images/module/device/
    - assets/images/module/meetings/
    - assets/images/module/online_examination/
    - assets/images/module/safety_checks/
    - assets/images/module/schedule_task/
    - assets/images/module/take_picture/
    - assets/images/module/warning/
    - assets/images/module/work_permit/
    - assets/images/module/xj_task_simple/
    - assets/images/module/other/
    - assets/images/module/emergency/
    - assets/images/module/dual_prevention/
    - assets/images/module/plan_budget/
    - assets/images/module/education_training/
    - assets/images/module/tsak_like/
    - assets/images/module/equipment_account/
    - assets/images/module/education/
    - assets/images/module/training_management/
    #base
    - assets/images/base/
    #login
    - assets/images/login/
    # 企业logo
    - assets/images/ent_logo/
    # public
    - assets/images/public/
    # home
    - assets/images/home/
    # 统计
    - assets/images/home/<USER>/
    # 应急响应
    - assets/images/emergency/
    # 应急演练
    - assets/images/contingency/
    # 教育培训
    - assets/images/education_training/
    # 特殊作业
    - assets/images/special_tasks/
    # nfc
    - assets/images/nfc/
    # old
    - lib/assets/images/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
